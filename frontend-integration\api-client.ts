/**
 * Qareeb API Client
 * TypeScript client for integrating with Qareeb Plus Backend
 * 
 * Usage:
 * const api = new QareebApiClient('http://localhost:3000/v1');
 * await api.auth.login({ loginId: '<EMAIL>', password: 'password' });
 */

// ============== TYPE DEFINITIONS ==============

export interface ApiResponse<T> {
  count?: number;
  items?: T[];
  data?: T;
}

export interface PaginationParams {
  offset?: number;
  limit?: number;
  sortKey?: string;
  searchKey?: string;
}

// ============== AUTH TYPES ==============
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  phoneNumberRegion?: string;
}

export interface LoginRequest {
  loginId: string;
  password: string;
  roleName?: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// ============== USER TYPES ==============
export interface User {
  id: number;
  firstName: string;
  lastName?: string;
  phoneNumber?: string;
  email?: string;
  status: string;
  roleId: number;
  role?: Role;
  createdAt: Date;
  updatedAt: Date;
}

export interface Role {
  id: number;
  name: string;
  permissions: Permission[];
}

export interface Permission {
  id: number;
  action: string;
  manuallyAdded: boolean;
}

// ============== BUSINESS TYPES ==============
export interface Business {
  id: number;
  nameAr: string;
  nameEn: string;
  phoneNumber?: string;
  whatsAppNumber?: string;
  priceRange?: 'LOW' | 'MEDIUM' | 'HIGH';
  locationId: number;
  primaryCategoryId: number;
  operatingHours?: OperatingHours;
  isOpen24x7?: boolean;
  ramadanHours?: OperatingHours;
  logoUrl?: string;
  coverPhotoUrl?: string;
  galleryUrls?: string[];
  shortDescriptionAr?: string;
  shortDescriptionEn?: string;
  fullDescriptionAr?: string;
  fullDescriptionEn?: string;
  isActive?: boolean;
  isVerified?: boolean;
  isPremium?: boolean;
  premiumExpiresAt?: Date;
  averageRating?: number;
  totalReviewsCount?: number;
  totalViewsCount?: number;
  lastMonthViews?: number;
  ownerUserId: number;
  paymentMethods?: PaymentMethod[];
  location?: Location;
  primaryCategory?: Category;
  features?: Feature[];
  owner?: User;
  createdAt: Date;
  updatedAt: Date;
}

export interface OperatingHours {
  [key: string]: {
    open: string;
    close: string;
    isClosed: boolean;
  };
}

export type PaymentMethod = 'cash' | 'card' | 'mobile_wallet';

export interface GetAllBusinessParams extends PaginationParams {
  locationId?: number;
  primaryCategoryId?: number;
  ownerUserId?: number;
  featureId?: number;
  priceRange?: 'LOW' | 'MEDIUM' | 'HIGH';
  paymentMethod?: PaymentMethod;
  isActive?: boolean;
  isVerified?: boolean;
  isPremium?: boolean;
  isOpen24x7?: boolean;
  minRating?: number;
  cityEn?: string;
  cityAr?: string;
  includeLocation?: boolean;
  includeCategory?: boolean;
  includeFeatures?: boolean;
  includeOwner?: boolean;
}

// ============== CATEGORY TYPES ==============
export interface Category {
  id: number;
  nameEn: string;
  nameAr: string;
  descriptionEn?: string;
  descriptionAr?: string;
  icon?: string;
  cover?: string;
  slug?: string;
  numberOfBusinesses: number;
  sortOrder: number;
  level: number;
  path?: string;
  parentId?: number;
  parent?: Category;
  children?: Category[];
  createdAt: Date;
  updatedAt: Date;
}

export interface GetAllCategoryParams extends PaginationParams {
  parentId?: number;
  level?: number;
  includeChildren?: boolean;
}

// ============== LOCATION TYPES ==============
export interface Location {
  id: number;
  nameEn: string;
  nameAr: string;
  latitude: number;
  longitude: number;
  streetAddressEn?: string;
  streetAddressAr?: string;
  cityEn?: string;
  cityAr?: string;
  stateEn?: string;
  stateAr?: string;
  countryEn?: string;
  countryAr?: string;
  postalCode?: string;
  nearestLandmarkEn?: string;
  nearestLandmarkAr?: string;
  descriptionEn?: string;
  descriptionAr?: string;
  mapUrl?: string;
  placeId?: string;
  accuracy?: number;
  altitude?: number;
  timezone?: string;
  parentId?: number;
  parent?: Location;
  children?: Location[];
  createdAt: Date;
  updatedAt: Date;
}

export interface GetAllLocationParams extends PaginationParams {
  minLatitude?: number;
  maxLatitude?: number;
  minLongitude?: number;
  maxLongitude?: number;
  centerLatitude?: number;
  centerLongitude?: number;
  radiusKm?: number;
  cityEn?: string;
  cityAr?: string;
  countryEn?: string;
  countryAr?: string;
  parentId?: number;
}

// ============== FEATURE TYPES ==============
export interface Feature {
  id: number;
  nameEn: string;
  nameAr: string;
  icon: string;
  createdAt: Date;
  updatedAt: Date;
}

// ============== API CLIENT ==============
export class QareebApiClient {
  private baseUrl: string;
  private accessToken: string | null = null;
  private isGuest: boolean = false;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/+$/, ''); // Remove trailing slashes
  }

  // Set authentication token
  setAccessToken(token: string) {
    this.accessToken = token;
    this.isGuest = false;
  }

  // Get authentication token
  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Clear authentication token
  clearAccessToken() {
    this.accessToken = null;
    this.isGuest = false;
  }

  // Enable guest mode (no authentication required)
  enableGuestMode() {
    this.isGuest = true;
    this.accessToken = null;
  }

  // Check if currently in guest mode
  isGuestMode(): boolean {
    return this.isGuest;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  // Generic HTTP request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    allowGuest: boolean = false
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add authorization header if we have a token, or if we're in guest mode
    if (this.accessToken) {
      headers.Authorization = `Bearer ${this.accessToken}`;
    } else if (allowGuest && this.isGuest) {
      // For guest requests, we still send without token but the backend will handle it
      // The backend's @AllowGuest decorator will provide a guest user context
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return response.json();
  }

  // Authentication methods
  auth = {
    register: (data: RegisterRequest): Promise<AuthResponse> =>
      this.request('/auth/register', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    login: (data: LoginRequest): Promise<AuthResponse> =>
      this.request('/auth/login', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    refresh: (data: RefreshTokenRequest): Promise<AuthResponse> =>
      this.request('/auth/refresh', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
  };

  // User methods
  users = {
    getProfile: (): Promise<User> =>
      this.request('/users/profile'),

    getAll: (params?: PaginationParams): Promise<ApiResponse<User>> =>
      this.request(`/users${this.buildQueryString(params)}`),

    getById: (id: number): Promise<User> =>
      this.request(`/users/${id}`),

    create: (data: Partial<User>): Promise<User> =>
      this.request('/users', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    update: (id: number, data: Partial<User>): Promise<User> =>
      this.request(`/users/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),

    delete: (id: number): Promise<void> =>
      this.request(`/users/${id}`, { method: 'DELETE' }),
  };

  // Business methods
  businesses = {
    getAll: (params?: GetAllBusinessParams): Promise<ApiResponse<Business>> =>
      this.request(`/businesses${this.buildQueryString(params)}`, {}, true),

    getById: (id: number): Promise<Business> =>
      this.request(`/businesses/${id}`, {}, true),

    getByLocation: (locationId: number): Promise<Business[]> =>
      this.request(`/businesses/location/${locationId}`, {}, true),

    getByCategory: (categoryId: number): Promise<Business[]> =>
      this.request(`/businesses/category/${categoryId}`, {}, true),

    getByOwner: (ownerId: number): Promise<Business[]> =>
      this.request(`/businesses/owner/${ownerId}`, {}, true),

    getByFeature: (featureId: number): Promise<Business[]> =>
      this.request(`/businesses/feature/${featureId}`, {}, true),

    getByRatingRange: (minRating: number, maxRating?: number): Promise<Business[]> => {
      const query = maxRating ? `?maxRating=${maxRating}` : '';
      return this.request(`/businesses/rating/${minRating}${query}`, {}, true);
    },

    getByCity: (cityEn?: string, cityAr?: string): Promise<Business[]> => {
      const params = new URLSearchParams();
      if (cityEn) params.append('cityEn', cityEn);
      if (cityAr) params.append('cityAr', cityAr);
      const query = params.toString() ? `?${params.toString()}` : '';
      return this.request(`/businesses/city${query}`, {}, true);
    },

    getNearby: (latitude: number, longitude: number, radiusKm?: number): Promise<Business[]> => {
      const query = radiusKm ? `?radiusKm=${radiusKm}` : '';
      return this.request(`/businesses/nearby/${latitude}/${longitude}${query}`, {}, true);
    },

    getActive: (): Promise<Business[]> =>
      this.request('/businesses/active', {}, true),

    getVerified: (): Promise<Business[]> =>
      this.request('/businesses/verified', {}, true),

    getPremium: (): Promise<Business[]> =>
      this.request('/businesses/premium', {}, true),

    create: (data: Partial<Business>): Promise<Business> =>
      this.request('/businesses', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    update: (id: number, data: Partial<Business>): Promise<Business> =>
      this.request(`/businesses/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),

    activate: (id: number): Promise<Business> =>
      this.request(`/businesses/${id}/activate`, { method: 'PATCH' }),

    deactivate: (id: number): Promise<Business> =>
      this.request(`/businesses/${id}/deactivate`, { method: 'PATCH' }),

    verify: (id: number): Promise<Business> =>
      this.request(`/businesses/${id}/verify`, { method: 'PATCH' }),

    unverify: (id: number): Promise<Business> =>
      this.request(`/businesses/${id}/unverify`, { method: 'PATCH' }),

    upgradePremium: (id: number, expiresAt: string): Promise<Business> =>
      this.request(`/businesses/${id}/upgrade-premium`, {
        method: 'PATCH',
        body: JSON.stringify({ expiresAt }),
      }),

    downgradePremium: (id: number): Promise<Business> =>
      this.request(`/businesses/${id}/downgrade-premium`, { method: 'PATCH' }),

    delete: (id: number): Promise<void> =>
      this.request(`/businesses/${id}`, { method: 'DELETE' }),
  };

  // Category methods
  categories = {
    getAll: (params?: GetAllCategoryParams): Promise<ApiResponse<Category>> =>
      this.request(`/categories${this.buildQueryString(params)}`, {}, true),

    getById: (id: number): Promise<Category> =>
      this.request(`/categories/${id}`, {}, true),

    getHierarchy: (): Promise<Category[]> =>
      this.request('/categories/hierarchy', {}, true),

    getByParent: (parentId: number): Promise<Category[]> =>
      this.request(`/categories/parent/${parentId}`, {}, true),

    getByLevel: (level: number): Promise<Category[]> =>
      this.request(`/categories/level/${level}`, {}, true),

    getWithChildren: (id: number): Promise<Category> =>
      this.request(`/categories/${id}/with-children`, {}, true),

    create: (data: Partial<Category>): Promise<Category> =>
      this.request('/categories', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    update: (id: number, data: Partial<Category>): Promise<Category> =>
      this.request(`/categories/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),

    updateBusinessCount: (id: number, count: number): Promise<void> =>
      this.request(`/categories/${id}/business-count/${count}`, { method: 'PUT' }),

    delete: (id: number): Promise<void> =>
      this.request(`/categories/${id}`, { method: 'DELETE' }),
  };

  // Location methods
  locations = {
    getAll: (params?: GetAllLocationParams): Promise<ApiResponse<Location>> =>
      this.request(`/locations${this.buildQueryString(params)}`, {}, true),

    getById: (id: number): Promise<Location> =>
      this.request(`/locations/${id}`, {}, true),

    getByCoordinates: (latitude: number, longitude: number, radiusKm?: number): Promise<Location[]> => {
      const query = radiusKm ? `?radiusKm=${radiusKm}` : '';
      return this.request(`/locations/coordinates/${latitude}/${longitude}${query}`, {}, true);
    },

    getWithinBounds: (params: {
      minLatitude: number;
      maxLatitude: number;
      minLongitude: number;
      maxLongitude: number;
    }): Promise<Location[]> =>
      this.request(`/locations/bounds${this.buildQueryString(params)}`, {}, true),

    getNearest: (latitude: number, longitude: number, limit?: number): Promise<Location[]> => {
      const query = limit ? `?limit=${limit}` : '';
      return this.request(`/locations/nearest/${latitude}/${longitude}${query}`, {}, true);
    },

    getByCity: (cityEn?: string, cityAr?: string): Promise<Location[]> => {
      const params = new URLSearchParams();
      if (cityEn) params.append('cityEn', cityEn);
      if (cityAr) params.append('cityAr', cityAr);
      const query = params.toString() ? `?${params.toString()}` : '';
      return this.request(`/locations/city${query}`, {}, true);
    },

    getByCountry: (countryEn?: string, countryAr?: string): Promise<Location[]> => {
      const params = new URLSearchParams();
      if (countryEn) params.append('countryEn', countryEn);
      if (countryAr) params.append('countryAr', countryAr);
      const query = params.toString() ? `?${params.toString()}` : '';
      return this.request(`/locations/country${query}`, {}, true);
    },

    getByParent: (parentId: number): Promise<Location[]> =>
      this.request(`/locations/parent/${parentId}`, {}, true),

    getWithChildren: (id: number): Promise<Location> =>
      this.request(`/locations/${id}/with-children`, {}, true),

    calculateDistance: (lat1: number, lon1: number, lat2: number, lon2: number): Promise<{ distance: number }> =>
      this.request(`/locations/distance/${lat1}/${lon1}/${lat2}/${lon2}`, {}, true),

    create: (data: Partial<Location>): Promise<Location> =>
      this.request('/locations', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    update: (id: number, data: Partial<Location>): Promise<Location> =>
      this.request(`/locations/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),

    delete: (id: number): Promise<void> =>
      this.request(`/locations/${id}`, { method: 'DELETE' }),
  };

  // Feature methods
  features = {
    getAll: (params?: PaginationParams): Promise<ApiResponse<Feature>> =>
      this.request(`/features${this.buildQueryString(params)}`, {}, true),

    getById: (id: number): Promise<Feature> =>
      this.request(`/features/${id}`, {}, true),

    create: (data: Partial<Feature>): Promise<Feature> =>
      this.request('/features', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    update: (id: number, data: Partial<Feature>): Promise<Feature> =>
      this.request(`/features/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),

    delete: (id: number): Promise<void> =>
      this.request(`/features/${id}`, { method: 'DELETE' }),
  };

  // Helper method to build query strings
  private buildQueryString(params?: Record<string, any>): string {
    if (!params) return '';
    
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }
}

// Export default instance
export default QareebApiClient;