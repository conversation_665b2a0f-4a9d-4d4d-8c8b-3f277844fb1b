import { ApiHideProperty } from '@nestjs/swagger';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';
import { BooleanTransform } from '../decorators/boolean-transform.decorator';

export class GetAllDto {
  @IsOptional()
  @IsNumber()
  offset?: number = 0;

  @IsOptional()
  @IsNumber()
  limit?: number = 10;

  @IsString()
  @IsOptional()
  searchKey?: string;

  @IsOptional()
  @IsString()
  @ApiHideProperty()
  searchOnJson?: string;

  @IsOptional()
  @IsString()
  @ApiHideProperty()
  searchOnRelation?: string;

  /**
   * Formatted: {propName}:{-1 | 1},
   * For Example: firstName:-1
   * where the firstName one of the model properties
   * */
  @IsOptional()
  @IsString()
  // @Matches(/([a-zA-Z_][a-zA-Z0-9_]*):(-1|1)/i)
  sortKey?: string;

  /**
   * Formatted: ISO_8601,
   * For Example: 2020-07-10
   * */
  @IsDateString()
  @IsOptional()
  createdAtFrom?: string;

  /**
   * Formatted: ISO_8601,
   * For Example: 2020-07-10
   * */
  @IsDateString()
  @IsOptional()
  createdAtTo?: string;

  /**
   * Formatted: ISO_8601,
   * For Example: 2020-07-10
   * */
  @IsDateString()
  @IsOptional()
  updateAtFrom?: string;

  /**
   * Formatted: ISO_8601,
   * For Example: 2020-07-10
   * */
  @IsDateString()
  @IsOptional()
  updateAtTo?: string;

  @IsOptional()
  @BooleanTransform()
  withDeleted?: boolean = false;

  /**
   * Comma separated string of ids
   * For Example: 1,2,3,4
   */
  @IsOptional()
  @IsString()
  ids?: string;
}
