import { DefaultRole } from '@app/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: 'The login ID (email or username)',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  loginId: string;

  @ApiProperty({
    description: 'The user password',
    example: 'StrongPassword123!',
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiPropertyOptional({
    description: 'Optional role name for role-based login',
    enum: DefaultRole,
    example: DefaultRole.CUSTOMER,
  })
  @IsOptional()
  @IsEnum(DefaultRole)
  roleName?: DefaultRole;
}
