# Generic TypeORM Components

This directory contains generic components for working with TypeORM in NestJS applications.

## Components

- `GenericRepository`: Base repository class with common CRUD operations
- `GenericService`: Service layer with business logic for entity operations

## How to Use

### 1. Create your entity

```typescript
import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../common/entities';

@Entity('users')
export class User extends BaseEntity {
  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ unique: true })
  email: string;

  @Column({ default: true })
  isActive: boolean;
}
```

### 2. Create your DTOs

```typescript
import { GetAllDto } from '../common/dtos';
import { IsEmail, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

// For querying with filters
export class GetAllUsersDto extends GetAllDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  firstName?: string;
}

// For creating new entities
export class CreateUserDto {
  @IsString()
  @ApiProperty()
  firstName: string;

  @IsString()
  @ApiProperty()
  lastName: string;

  @IsEmail()
  @ApiProperty()
  email: string;
}

// For updating entities
export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  firstName?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  lastName?: string;

  @IsOptional()
  @IsEmail()
  @ApiProperty({ required: false })
  email?: string;
}
```

### 3. Create your repository

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GenericRepository } from '../common/repositories';
import { User } from './user.entity';

@Injectable()
export class UserRepository extends GenericRepository<User, typeof User> {
  constructor(
    @InjectRepository(User)
    repository: Repository<User>,
  ) {
    super(repository);
  }

  // Add custom repository methods here
}
```

### 4. Create your service

```typescript
import { Injectable } from '@nestjs/common';
import { GenericService } from '../common/services';
import { User } from './user.entity';
import { UserRepository } from './user.repository';
import { CreateUserDto, GetAllUsersDto, UpdateUserDto } from './user.dto';

@Injectable()
export class UserService extends GenericService<
  User,
  typeof User,
  GetAllUsersDto,
  CreateUserDto,
  UpdateUserDto
> {
  constructor(repository: UserRepository) {
    super(repository);
  }

  // Add custom service methods here
}
```

### 5. Create your controller

Controllers should implement their own CRUD operations as needed, following the standard NestJS patterns with proper decorators and validation.

### 6. Register in your module

```typescript
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './user.entity';
import { UserRepository } from './user.repository';
import { UserService } from './user.service';
import { UserController } from './user.controller';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  providers: [UserRepository, UserService],
  controllers: [UserController],
  exports: [UserService],
})
export class UserModule {}
```
