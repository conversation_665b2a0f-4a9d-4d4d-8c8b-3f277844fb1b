<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الميزات - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pagination.css">
    
    <style>
        .features-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .feature-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .feature-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-gradient);
            border-radius: 12px;
            color: white;
        }
        
        .feature-info h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .feature-info p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .feature-description {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.6;
        }
        
        .feature-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .feature-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }
        
        .feature-status {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .feature-status.active {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .feature-status.inactive {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
            display: block;
        }
        
        @media (max-width: 768px) {
            .features-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">الميزات</h1>
            <p class="page-subtitle">إدارة وعرض جميع ميزات الأعمال التجارية</p>
        </div>
        
        <div class="features-header">
            <div>
                <input type="text" id="searchInput" class="search-input" placeholder="البحث في الميزات..." dir="rtl" style="padding: 10px 15px; border: 2px solid #e1e8ed; border-radius: 8px; font-size: 14px; min-width: 250px;">
            </div>
            
            <a href="form.html" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة ميزة جديدة
            </a>
        </div>
        
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p>جاري تحميل الميزات...</p>
        </div>
        
        <div id="featuresContainer" class="features-grid" style="display: none;">
            <!-- Features will be loaded here -->
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-star"></i>
            <h3>لا توجد ميزات</h3>
            <p>لم يتم العثور على أي ميزات مطابقة للبحث</p>
            <a href="form.html" class="btn btn-primary" style="margin-top: 20px;">
                <i class="fas fa-plus"></i>
                إضافة أول ميزة
            </a>
        </div>
        
        <div class="response-message" id="responseMessage"></div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script src="../../assets/js/pagination.js"></script>
    <script>
        let features = [];
        let allFeatures = [];
        let currentSearchTerm = '';
        let pagination = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePagination();
            loadFeatures();
            setupEventListeners();
        });

        function initializePagination() {
            pagination = new PaginationManager({
                containerId: 'paginationContainer',
                pageSize: 10,
                onPageChange: (page, offset) => {
                    loadFeatures(offset, pagination.pageSize);
                },
                onPageSizeChange: (pageSize) => {
                    loadFeatures(0, pageSize);
                },
                showPageSizeSelector: true,
                pageSizeOptions: [5, 10, 20, 50]
            });
        }

        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearchTerm = this.value.trim();
                    loadFeatures(0, pagination.pageSize);
                }, 300); // Debounce search
            });
        }
        
        async function loadFeatures(offset = 0, limit = 10) {
            const loadingContainer = document.getElementById('loadingContainer');
            const featuresContainer = document.getElementById('featuresContainer');
            const emptyState = document.getElementById('emptyState');

            try {
                loadingContainer.style.display = 'block';
                featuresContainer.style.display = 'none';
                emptyState.style.display = 'none';

                if (pagination) {
                    pagination.setLoading(true);
                }

                // Build query parameters
                const params = new URLSearchParams({
                    offset: offset.toString(),
                    limit: limit.toString()
                });

                if (currentSearchTerm) {
                    params.append('searchKey', currentSearchTerm);
                }

                const response = await fetch(`/v1/features?${params.toString()}`, {
                    headers: AuthUtils.getAuthHeaders()
                });

                if (response.ok) {
                    const result = await response.json();
                    // Handle different response formats
                    if (result.data) {
                        // Format: { data: { items: [], count: number } }
                        features = result.data.items || [];
                        const totalCount = result.data.count || result.data.totalCount || features.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    } else {
                        // Direct format: { items: [], count: number }
                        features = result.items || result;
                        const totalCount = result.count || features.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    }

                    if (features.length === 0) {
                        emptyState.style.display = 'block';
                    } else {
                        displayFeatures(features);
                        featuresContainer.style.display = 'grid';
                    }
                } else {
                    throw new Error('Failed to load features');
                }
            } catch (error) {
                console.error('Error loading features:', error);
                showMessage('فشل في تحميل الميزات', 'error');
                emptyState.style.display = 'block';

                if (pagination) {
                    pagination.updateData(0);
                }
            } finally {
                loadingContainer.style.display = 'none';
                if (pagination) {
                    pagination.setLoading(false);
                }
            }
        }
        
        function displayFeatures(featuresToShow) {
            const container = document.getElementById('featuresContainer');
            container.innerHTML = '';
            
            featuresToShow.forEach(feature => {
                const featureCard = createFeatureCard(feature);
                container.appendChild(featureCard);
            });
        }
        
        function createFeatureCard(feature) {
            const card = document.createElement('div');
            card.className = 'feature-card';
            
            const statusClass = feature.isActive !== false ? 'active' : 'inactive';
            const statusText = feature.isActive !== false ? 'نشط' : 'غير نشط';
            const statusIcon = feature.isActive !== false ? 'fas fa-check-circle' : 'fas fa-times-circle';
            
            card.innerHTML = `
                <div class="feature-header">
                    <div class="feature-icon">${feature.icon || '⭐'}</div>
                    <div class="feature-info">
                        <h3>${feature.nameAr}</h3>
                        <p>${feature.nameEn || ''}</p>
                    </div>
                    <div class="feature-status ${statusClass}">
                        <i class="${statusIcon}"></i>
                        ${statusText}
                    </div>
                </div>
                
                ${feature.descriptionAr ? `
                    <div class="feature-description">
                        ${feature.descriptionAr}
                    </div>
                ` : ''}
                
                <div class="feature-stats">
                    <div class="stat">
                        <div class="stat-number">${feature.businessCount || 0}</div>
                        <div class="stat-label">الأعمال</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${feature.id}</div>
                        <div class="stat-label">المعرف</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${feature.sortOrder || 0}</div>
                        <div class="stat-label">الترتيب</div>
                    </div>
                </div>
                
                <div class="feature-actions">
                    <a href="edit.html?id=${feature.id}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <button class="btn btn-danger btn-sm" onclick="deleteFeature(${feature.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            `;
            
            return card;
        }
        
        // Remove the old filterFeatures function as search is now handled by loadFeatures
        
        async function deleteFeature(featureId) {
            if (!confirm('هل أنت متأكد من حذف هذه الميزة؟')) {
                return;
            }
            
            try {
                const response = await fetch(`/v1/features/${featureId}`, {
                    method: 'DELETE',
                    headers: AuthUtils.getAuthHeaders()
                });
                
                if (response.ok) {
                    showMessage('تم حذف الميزة بنجاح', 'success');
                    // Reload current page
                    const currentOffset = pagination ? pagination.getOffset() : 0;
                    const currentLimit = pagination ? pagination.pageSize : 10;
                    loadFeatures(currentOffset, currentLimit);
                } else {
                    throw new Error('Failed to delete feature');
                }
            } catch (error) {
                console.error('Error deleting feature:', error);
                showMessage('فشل في حذف الميزة', 'error');
            }
        }
        
        function showMessage(message, type) {
            const responseMessage = document.getElementById('responseMessage');
            responseMessage.textContent = message;
            responseMessage.className = `response-message ${type}`;
            responseMessage.style.display = 'block';
            
            setTimeout(() => {
                responseMessage.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
