import { ConfigurationValidationDto } from '@app/common';
import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtPayload } from '../dtos/auth/jwt-payload.dto';
import { IUserService } from '../interfaces/user/user-service.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService<
      ConfigurationValidationDto,
      true
    >,
    @Inject(IUserService)
    private readonly userService: IUserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET'),
    });
  }

  async validate(payload: JwtPayload) {
    const user = await this.userService.findOneWithDetailsById(
      parseInt(payload.sub),
    );

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return user;
  }
}
