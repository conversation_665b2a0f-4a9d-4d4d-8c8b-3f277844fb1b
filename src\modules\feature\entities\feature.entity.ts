import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { BaseEntity } from '../../../common';

export class Feature extends BaseEntity {
  @IsNotEmpty()
  @IsString()
  nameEn: string;

  @IsNotEmpty()
  @IsString()
  nameAr: string;

  @IsNotEmpty()
  @IsString()
  icon: string;

  // Relations
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Object) // Avoid circular import by using Object type
  businesses?: any[]; // Will be properly typed as Business[] in the schema
}
