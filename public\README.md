# Public Frontend - Qareeb Plus

This directory contains the restructured public frontend files for the Qareeb Plus application with a modern sidebar navigation system.

## 🏗️ New Directory Structure

```
public/
├── assets/
│   ├── css/
│   │   ├── main.css          # Global styles and sidebar navigation
│   │   ├── components.css    # Reusable UI components
│   │   └── forms.css         # Form-specific styles
│   ├── js/
│   │   ├── auth-utils.js     # Authentication utilities
│   │   └── navigation.js     # Modern sidebar navigation system
│   └── images/               # Static images (future use)
├── pages/
│   ├── auth/
│   │   └── login.html        # Login page
│   ├── business/
│   │   ├── list.html         # Business listing page
│   │   ├── form.html         # Business creation form
│   │   └── edit.html         # Business editing form (to be created)
│   ├── categories/
│   │   ├── list.html         # Categories listing page
│   │   ├── form.html         # Category creation form (to be created)
│   │   └── edit.html         # Category editing form (to be created)
│   └── features/
│       ├── list.html         # Features listing page (to be created)
│       ├── form.html         # Feature creation form (to be created)
│       └── edit.html         # Feature editing form (to be created)
├── components/               # Reusable HTML components (future use)
└── index.html               # Main dashboard page
```

## ✨ Key Features

### Modern Sidebar Navigation
- **Collapsible sidebar** with smooth animations
- **Mobile-friendly** with overlay and hamburger menu
- **User profile section** with authentication status
- **Active page highlighting** with visual indicators
- **Keyboard shortcuts** (Ctrl+B to toggle sidebar)
- **Persistent state** (remembers collapsed/expanded state)

### Responsive Design
- **Mobile-first approach** with breakpoints at 768px
- **Touch-friendly** interface for mobile devices
- **Adaptive layouts** that work on all screen sizes
- **Smooth transitions** and animations

### Component-Based CSS
- **CSS Variables** for consistent theming
- **Modular stylesheets** for better maintainability
- **Reusable components** (cards, buttons, forms, alerts)
- **Utility classes** for common patterns

## 🎨 Design System

### Colors
- Primary: `#667eea` to `#764ba2` (gradient)
- Success: `#28a745`
- Danger: `#dc3545`
- Warning: `#ffc107`
- Info: `#17a2b8`

### Typography
- Font Family: 'Cairo' for Arabic support
- RTL support for Arabic text
- LTR support for English inputs

### Components
- **Cards**: Elevated containers with hover effects
- **Buttons**: Multiple variants (primary, secondary, success, danger)
- **Forms**: Styled inputs with validation states
- **Alerts**: Success, error, warning, and info messages
- **Stats**: Dashboard statistics with icons and numbers

## 🚀 Usage

### Navigation System
The new navigation system automatically initializes when pages load:

```html
<!-- Include required CSS and JS -->
<link rel="stylesheet" href="assets/css/main.css">
<link rel="stylesheet" href="assets/css/components.css">
<script src="assets/js/navigation.js"></script>
```

### Creating New Pages
1. Create HTML file in appropriate `pages/` subdirectory
2. Include the required CSS and JS files
3. Wrap content in a container div
4. The navigation system will automatically move content to the main area

### Mobile Responsiveness
- Sidebar automatically becomes an overlay on mobile
- Touch gestures supported for opening/closing
- All components adapt to smaller screens

## 🔧 Technical Details

### Browser Support
- Modern browsers with CSS Grid and Flexbox support
- CSS Variables support required
- ES6+ JavaScript features used

### Performance
- Optimized CSS with minimal redundancy
- Lazy loading of navigation components
- Smooth 60fps animations using CSS transforms

### Accessibility
- Keyboard navigation support
- ARIA labels and roles (to be enhanced)
- High contrast ratios for text
- Focus indicators for interactive elements

## 📱 Mobile Features

### Sidebar Behavior
- **Desktop**: Collapsible sidebar that pushes content
- **Mobile**: Overlay sidebar with backdrop
- **Touch**: Swipe gestures for opening/closing
- **Keyboard**: Escape key to close on mobile

### Responsive Breakpoints
- **Desktop**: > 768px (sidebar visible by default)
- **Mobile**: ≤ 768px (sidebar hidden by default)

## 🔄 Migration Notes

### From Old Structure
- Old navigation files can be safely removed
- Update all href links to use new `pages/` structure
- Replace old CSS includes with new modular system
- Authentication utilities remain compatible

### Future Enhancements
- Add more page templates
- Implement dark mode support
- Add more component variants
- Enhance accessibility features
- Add animation preferences
- Create new business features
- Emoji or image icon support
- Character counters for all fields
- Icon preview functionality
- Auto-save draft functionality
- Quick action buttons

### 5. Login Form (`login.html`)
- User authentication
- JWT token management
- Remember me functionality

## Features

### Authentication
- JWT-based authentication using `auth-utils.js`
- Automatic token refresh
- Secure token storage (localStorage/sessionStorage)
- Auto-logout on token expiration

### Bilingual Support
- Arabic (RTL) and English (LTR) support
- Consistent bilingual labels throughout
- Proper text direction handling

### User Experience
- Responsive design for mobile and desktop
- Loading states and error handling
- Form validation (client and server-side)
- Auto-save functionality (feature form)
- Preview functionality for media
- Character counters for text fields

### API Integration
- RESTful API calls to backend endpoints
- Proper error handling and user feedback
- Authenticated requests using Bearer tokens
- Support for nested object creation (locations)

## API Endpoints Used

### Categories
- `GET /v1/categories` - List categories (public)
- `POST /v1/categories` - Create category (authenticated)

### Features
- `GET /v1/features` - List features (authenticated)
- `POST /v1/features` - Create feature (authenticated)

### Businesses
- `POST /v1/businesses` - Create business (authenticated)

### Locations
- `GET /v1/locations` - List locations (authenticated)
- `POST /v1/locations` - Create location (via business creation)

### Authentication
- `POST /v1/auth/login` - User login
- `POST /v1/auth/refresh` - Token refresh

## File Structure

```
public/
├── index.html              # Dashboard/landing page
├── business-form.html      # Business creation form
├── business-form.js        # Business form logic
├── business-form.css       # Shared styles for all forms
├── category-form.html      # Category creation form
├── category-form.js        # Category form logic
├── feature-form.html       # Feature creation form
├── feature-form.js         # Feature form logic
├── login.html              # Login form
├── login.js                # Login form logic
├── login.css               # Login form styles
├── auth-utils.js           # Authentication utilities
└── README.md               # This file
```

## Usage

1. **Start the backend server:**
   ```bash
   npm run start:dev
   ```

2. **Access the forms:**
   - Dashboard: `http://localhost:3000/`
   - Business Form: `http://localhost:3000/business-form.html`
   - Category Form: `http://localhost:3000/category-form.html`
   - Feature Form: `http://localhost:3000/feature-form.html`
   - Login: `http://localhost:3000/login.html`

3. **Authentication:**
   - Login first using the login form
   - JWT tokens are automatically managed
   - Forms will auto-populate authentication fields

## Form Validation

### Category Form
- **Required:** nameEn, nameAr
- **Optional:** parentId, slug (auto-generated), sortOrder, descriptions, icon, cover
- **Validation:** URL validation for icon/cover, slug format validation

### Feature Form
- **Required:** nameEn, nameAr, icon
- **Validation:** Character limits (100 for names, 200 for icon), icon format validation
- **Features:** Auto-save, icon preview, emoji suggestions

### Business Form
- **Required:** nameEn, nameAr, primaryCategoryId, ownerUserId, location (existing or new)
- **Optional:** All other fields including features, operating hours, media
- **Validation:** Complex validation for nested location object, feature selection

## Styling

- Uses modern CSS with gradients and animations
- Responsive design with mobile-first approach
- Consistent color scheme and typography
- RTL/LTR support for bilingual content
- Hover effects and smooth transitions

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Requires JavaScript enabled
- Uses modern ES6+ features

## Security

- JWT tokens stored securely
- Automatic token refresh
- CSRF protection through proper headers
- Input validation and sanitization
- Secure API communication
