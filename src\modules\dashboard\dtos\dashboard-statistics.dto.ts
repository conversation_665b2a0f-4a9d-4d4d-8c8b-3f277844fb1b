import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

export class DashboardStatisticsDto {
  @ApiProperty({
    description: 'Total number of categories',
    example: 25,
  })
  @IsNumber()
  categoriesCount: number;

  @ApiProperty({
    description: 'Total number of features',
    example: 15,
  })
  @IsNumber()
  featuresCount: number;

  @ApiProperty({
    description: 'Total number of locations',
    example: 100,
  })
  @IsNumber()
  locationsCount: number;

  @ApiProperty({
    description: 'Total number of businesses',
    example: 250,
  })
  @IsNumber()
  businessesCount: number;

  @ApiProperty({
    description: 'Total number of active businesses',
    example: 200,
  })
  @IsNumber()
  activeBusinessesCount: number;

  @ApiProperty({
    description: 'Total number of verified businesses',
    example: 150,
  })
  @IsNumber()
  verifiedBusinessesCount: number;

  @ApiProperty({
    description: 'Total number of premium businesses',
    example: 50,
  })
  @IsNumber()
  premiumBusinessesCount: number;
}
