import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import { transport } from 'winston';

import { ConfigurationValidationDto } from '../dtos';
import {
  CloudLoggingFilter,
  ImmediateGoogleCloudTransport,
} from '../transports';

// Custom colors configuration
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  verbose: 'cyan',
  debug: 'blue',
  silly: 'gray',
};

winston.addColors(colors);

const customFormat = winston.format.printf((info) => {
  // Format metadata (exclude known fields)
  const metadataObj = { ...info };
  const excludeKeys = [
    'level',
    'message',
    'timestamp',
    'ms',
    'context',
    'stack',
  ];
  excludeKeys.forEach((key) => {
    if (key in metadataObj) {
      delete metadataObj[key];
    }
  });

  // Format different parts
  const timestamp = info.timestamp;
  const level = info.level.toUpperCase();
  const context = info.context ? `[${info.context}]` : '';
  const ms = info.ms ? ` ${info.ms}` : '';

  // Format metadata and stack trace
  const metadata = Object.keys(metadataObj).length
    ? `\n${JSON.stringify(metadataObj, null, 2)}`
    : '';

  const stack = info.stack ? `\n${info.stack}` : '';

  return `${timestamp} ${level} ${context}${ms}: ${info.message}${metadata}${stack}`;
});

export function getWinstonLoggerInstance(
  appName: string,
  configService?: ConfigService<ConfigurationValidationDto, true>,
) {
  const transports: transport[] = [
    new winston.transports.Console({
      level: configService?.get('NODE_ENV') === 'production' ? 'info' : 'debug',
      format: winston.format.combine(
        winston.format.errors({ stack: true }),
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss.SSS',
        }),
        winston.format.ms(),
        winston.format.splat(),
        winston.format.metadata({
          fillExcept: ['message', 'level', 'timestamp', 'ms', 'context'],
        }),
        winston.format.colorize({
          all: false,
          level: true,
        }),
        customFormat,
      ),
    }),
  ];

  // Add Google Cloud Logging transport if enabled in configuration
  if (configService?.get('GOOGLE_CLOUD_LOGGING_ENABLED')) {
    const projectId = configService.get('GOOGLE_CLOUD_PROJECT_ID');
    const keyFilename = configService.get('GOOGLE_CLOUD_KEY_FILENAME');
    const logName = configService.get('GOOGLE_CLOUD_LOG_NAME');

    if (!projectId || !keyFilename) {
      console.warn(
        'Google Cloud Logging is enabled but missing required configuration:',
        {
          projectId: !!projectId,
          keyFilename: !!keyFilename,
        },
      );
    } else {
      console.debug('Configuring Google Cloud Logging with:', {
        projectId,
        keyFilename: `./public/${keyFilename}`,
        logName: logName || appName,
      });

      const cloudFilter: CloudLoggingFilter = {
        // Log both HTTP request/response details and exceptions
        contexts: ['ResponseInterceptor', 'AllExceptionsFilter'],
        levels: ['error', 'warn', 'info'], // Only send these levels to cloud
        // Log if it has request/response data or is an exception
        shouldLog: (info) => {
          const shouldLog = !!(
            info.request ||
            info.response ||
            info.correlationId ||
            info.exception ||
            info.level === 'error'
          );
          console.debug('Cloud logging filter check:', {
            level: info.level,
            context: info.context,
            shouldLog,
          });
          return shouldLog;
        },
      };

      try {
        const loggingWinston = new ImmediateGoogleCloudTransport(
          {
            projectId,
            keyFilename: `./public/${keyFilename}`,
            logName: logName || appName,
            labels: {
              appName,
              environment: configService.get('NODE_ENV') || 'development',
            },
            // Configure immediate write options
            writeInterval: 0, // Write immediately
            batchSize: 1, // Process one log at a time
            maxRetries: 3, // Retry failed writes up to 3 times
            flushLevel: 'info', // Flush on info and above
            level: 'info', // Set minimum level for cloud logging
          },
          cloudFilter,
        );

        transports.push(loggingWinston);
        console.debug('Successfully added Google Cloud Transport');
      } catch (error) {
        console.error('Failed to initialize Google Cloud Logging:', error);
      }
    }
  } else {
    console.debug('Google Cloud Logging is disabled');
  }

  const logger = winston.createLogger({
    defaultMeta: {
      service: appName,
      environment: configService?.get('NODE_ENV') || 'development',
    },
    levels: winston.config.npm.levels,
    transports,
    // Enable exception handling
    handleExceptions: true,
    // Enable rejection handling
    handleRejections: true,
    // Exit on error
    exitOnError: false,
  });

  return logger;
}
