import { ApiHideProperty } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNumber, IsOptional } from 'class-validator';
import { DefaultRole, GetAllDto } from '@app/common';
import { UserStatus } from '../../enums/user-status.enum';

export class GetAllUserDto extends GetAllDto {
  @ApiHideProperty()
  @IsOptional()
  @IsEnum(DefaultRole)
  roleName?: DefaultRole;

  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @IsOptional()
  @IsNumber()
  roleId?: number;

  @IsOptional()
  @IsMongoId()
  userId?: number;
}
