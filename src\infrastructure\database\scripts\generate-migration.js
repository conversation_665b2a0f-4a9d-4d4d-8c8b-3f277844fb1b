/**
 * <PERSON><PERSON><PERSON> to generate TypeORM migrations with custom names
 * This script handles the --name parameter correctly on Windows
 */

const { spawnSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the migration name from command line arguments
const args = process.argv.slice(2);
let migrationName = null;

// Parse arguments to find the name parameter
for (const arg of args) {
  if (arg.startsWith('--name=')) {
    migrationName = arg.substring('--name='.length);
    break;
  }
}

// Validate migration name
if (!migrationName) {
  console.error('Error: Migration name is required');
  console.error('Usage: node generate-migration.js --name=migration-name');
  process.exit(1);
}

// Ensure migrations directory exists
const migrationsDir = path.resolve(
  process.cwd(),
  'src',
  'infrastructure',
  'database',
  'migrations'
);

if (!fs.existsSync(migrationsDir)) {
  console.log(`Creating migrations directory: ${migrationsDir}`);
  fs.mkdirSync(migrationsDir, { recursive: true });
}

// Build the migration path (just the name without extension)
const migrationPath = path.join(
  'src/infrastructure/database/migrations',
  migrationName
);

console.log(`Generating migration: ${migrationName}`);
console.log(`Migration path: ${migrationPath}`);
console.log(`Working directory: ${process.cwd()}`);

// Execute the TypeORM CLI command using ts-node with tsconfig-paths
const result = spawnSync(
  'npx', 
  [
    'ts-node',
    '-r',
    'tsconfig-paths/register',
    './node_modules/typeorm/cli.js',
    'migration:generate',
    '-d',
    'src/infrastructure/database/typeorm-config.ts',
    migrationPath
  ], 
  { 
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      TS_NODE_PROJECT: 'tsconfig.json'
    }
  }
);

if (result.status !== 0) {
  console.error(`Command failed with exit code ${result.status}`);
  process.exit(result.status);
}

// Check if migration was created
const files = fs.readdirSync(migrationsDir);
console.log(`\nFiles in migrations directory after command:`);
files.forEach(file => console.log(` - ${file}`));

if (files.length === 0) {
  console.log('\nNo migration files were created. This could mean:');
  console.log('1. There are no schema changes to migrate');
  console.log('2. The database connection failed');
  console.log('3. The entities could not be found');
} else {
  console.log('\nMigration generated successfully');
}
