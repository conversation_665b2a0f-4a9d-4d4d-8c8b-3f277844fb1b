/**
 * React Hooks for Qareeb API Integration
 * 
 * Usage:
 * import { useQareebApi, useAuth, useBusinesses } from './react-hooks';
 * 
 * const MyComponent = () => {
 *   const { login, user, isAuthenticated } = useAuth();
 *   const { businesses, loading, error } = useBusinesses();
 *   // ...
 * };
 */

import { useState, useEffect, useCallback, useContext, createContext, ReactNode } from 'react';
import QareebApiClient, {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  User,
  Business,
  Category,
  Location,
  Feature,
  GetAllBusinessParams,
  GetAllCategoryParams,
  GetAllLocationParams,
  PaginationParams,
} from './api-client';


// ============== CONTEXT ==============
interface QareebApiContextType {
  client: QareebApiClient;
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

const QareebApiContext = createContext<QareebApiContextType | undefined>(undefined);

interface QareebApiProviderProps {
  children: ReactNode;
  baseUrl: string;
  accessToken?: string;
}

export const QareebApiProvider: React.FC<QareebApiProviderProps> = ({
  children,
  baseUrl,
  accessToken,
}) => {
  const [client] = useState(() => new QareebApiClient(baseUrl));
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (accessToken) {
      client.setAccessToken(accessToken);
      setIsAuthenticated(true);
      fetchUserProfile();
    }
  }, [accessToken, client]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const userProfile = await client.users.getProfile();
      setUser(userProfile);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user profile');
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    client,
    isAuthenticated,
    user,
    loading,
    error,
  };

  return (
    <QareebApiContext.Provider value={value}>
      {children}
    </QareebApiContext.Provider>
  );
};

// ============== HOOKS ==============

export const useQareebApi = () => {
  const context = useContext(QareebApiContext);
  if (!context) {
    throw new Error('useQareebApi must be used within a QareebApiProvider');
  }
  return context;
};

// ============== AUTHENTICATION HOOK ==============
export const useAuth = () => {
  const { client } = useQareebApi();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const login = useCallback(async (credentials: LoginRequest): Promise<AuthResponse | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const authResponse = await client.auth.login(credentials);
      client.setAccessToken(authResponse.accessToken);
      setIsAuthenticated(true);
      
      // Fetch user profile after login
      const userProfile = await client.users.getProfile();
      setUser(userProfile);
      
      return authResponse;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
      setIsAuthenticated(false);
      setUser(null);
      return null;
    } finally {
      setLoading(false);
    }
  }, [client]);

  const register = useCallback(async (userData: RegisterRequest): Promise<AuthResponse | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const authResponse = await client.auth.register(userData);
      client.setAccessToken(authResponse.accessToken);
      setIsAuthenticated(true);
      
      // Fetch user profile after registration
      const userProfile = await client.users.getProfile();
      setUser(userProfile);
      
      return authResponse;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [client]);

  const logout = useCallback(() => {
    client.clearAccessToken();
    setIsAuthenticated(false);
    setUser(null);
    setError(null);
  }, [client]);

  const refreshToken = useCallback(async (refreshTokenValue: string): Promise<boolean> => {
    try {
      setLoading(true);
      const authResponse = await client.auth.refresh({ refreshToken: refreshTokenValue });
      client.setAccessToken(authResponse.accessToken);
      setIsAuthenticated(true);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Token refresh failed');
      logout();
      return false;
    } finally {
      setLoading(false);
    }
  }, [client, logout]);

  return {
    login,
    register,
    logout,
    refreshToken,
    user,
    isAuthenticated,
    loading,
    error,
  };
};

// ============== BUSINESSES HOOK ==============
export const useBusinesses = (params?: GetAllBusinessParams) => {
  const { client } = useQareebApi();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBusinesses = useCallback(async (newParams?: GetAllBusinessParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await client.businesses.getAll(newParams || params);
      setBusinesses(response.items || []);
      setTotal(response.count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch businesses');
    } finally {
      setLoading(false);
    }
  }, [client, params]);

  useEffect(() => {
    fetchBusinesses();
  }, [fetchBusinesses]);

  const getById = useCallback(async (id: number): Promise<Business | null> => {
    try {
      return await client.businesses.getById(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch business');
      return null;
    }
  }, [client]);

  const getNearby = useCallback(async (latitude: number, longitude: number, radiusKm?: number): Promise<Business[]> => {
    try {
      setLoading(true);
      const nearbyBusinesses = await client.businesses.getNearby(latitude, longitude, radiusKm);
      return nearbyBusinesses;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch nearby businesses');
      return [];
    } finally {
      setLoading(false);
    }
  }, [client]);

  return {
    businesses,
    total,
    loading,
    error,
    refetch: fetchBusinesses,
    getById,
    getNearby,
  };
};

// ============== CATEGORIES HOOK ==============
export const useCategories = (params?: GetAllCategoryParams) => {
  const { client } = useQareebApi();
  const [categories, setCategories] = useState<Category[]>([]);
  const [hierarchy, setHierarchy] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [categoriesResponse, hierarchyResponse] = await Promise.all([
        client.categories.getAll(params),
        client.categories.getHierarchy(),
      ]);
      
      setCategories(categoriesResponse.items || []);
      setHierarchy(hierarchyResponse);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
    } finally {
      setLoading(false);
    }
  }, [client, params]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const getById = useCallback(async (id: number): Promise<Category | null> => {
    try {
      return await client.categories.getById(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch category');
      return null;
    }
  }, [client]);

  const getByParent = useCallback(async (parentId: number): Promise<Category[]> => {
    try {
      return await client.categories.getByParent(parentId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch child categories');
      return [];
    }
  }, [client]);

  return {
    categories,
    hierarchy,
    loading,
    error,
    refetch: fetchCategories,
    getById,
    getByParent,
  };
};

// ============== LOCATIONS HOOK ==============
export const useLocations = (params?: GetAllLocationParams) => {
  const { client } = useQareebApi();
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLocations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await client.locations.getAll(params);
      setLocations(response.items || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch locations');
    } finally {
      setLoading(false);
    }
  }, [client, params]);

  useEffect(() => {
    fetchLocations();
  }, [fetchLocations]);

  const getById = useCallback(async (id: number): Promise<Location | null> => {
    try {
      return await client.locations.getById(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch location');
      return null;
    }
  }, [client]);

  const getNearby = useCallback(async (latitude: number, longitude: number, limit?: number): Promise<Location[]> => {
    try {
      return await client.locations.getNearest(latitude, longitude, limit);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch nearby locations');
      return [];
    }
  }, [client]);

  const calculateDistance = useCallback(async (lat1: number, lon1: number, lat2: number, lon2: number): Promise<number | null> => {
    try {
      const response = await client.locations.calculateDistance(lat1, lon1, lat2, lon2);
      return response.distance;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to calculate distance');
      return null;
    }
  }, [client]);

  return {
    locations,
    loading,
    error,
    refetch: fetchLocations,
    getById,
    getNearby,
    calculateDistance,
  };
};

// ============== FEATURES HOOK ==============
export const useFeatures = (params?: PaginationParams) => {
  const { client } = useQareebApi();
  const [features, setFeatures] = useState<Feature[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFeatures = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await client.features.getAll(params);
      setFeatures(response.items || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch features');
    } finally {
      setLoading(false);
    }
  }, [client, params]);

  useEffect(() => {
    fetchFeatures();
  }, [fetchFeatures]);

  return {
    features,
    loading,
    error,
    refetch: fetchFeatures,
  };
};

// ============== GEOLOCATION HOOK ==============
export const useGeolocation = () => {
  const [position, setPosition] = useState<GeolocationPosition | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getCurrentPosition = useCallback(() => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    setLoading(true);
    setError(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setPosition(position);
        setLoading(false);
      },
      (error) => {
        setError(error.message);
        setLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  }, []);

  useEffect(() => {
    getCurrentPosition();
  }, [getCurrentPosition]);

  return {
    position,
    latitude: position?.coords.latitude,
    longitude: position?.coords.longitude,
    loading,
    error,
    refetch: getCurrentPosition,
  };
};

export default {
  useQareebApi,
  useAuth,
  useBusinesses,
  useCategories,
  useLocations,
  useFeatures,
  useGeolocation,
};
