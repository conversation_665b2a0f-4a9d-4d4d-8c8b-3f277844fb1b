import { CountryDialCode } from '@app/common';
import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '../../../infrastructure/database/schemas/base.schema';
import { Role } from '../entities/role.entity';
import { User } from '../entities/user.entity';

export const UserSchema = new EntitySchema<User>({
  name: User.name,
  target: User,
  tableName: 'users',
  columns: {
    ...BaseSchemaProperties,
    firstName: {
      type: String,
      nullable: false,
    },
    lastName: {
      type: String,
      nullable: true,
    },
    phoneNumber: {
      type: String,
      nullable: true,
    },
    countryCode: {
      type: 'enum',
      enum: Object.values(CountryDialCode),
      nullable: true,
      enumName: 'dial_code_enum',
    },
    email: {
      type: String,
      nullable: true,
    },
    password: {
      type: String,
      nullable: false,
    },
    age: {
      type: Number,
      nullable: true,
    },
    birthDate: {
      type: Date,
      nullable: true,
    },
    apiKey: {
      type: String,
      nullable: true,
    },
    roleId: {
      type: Number,
      nullable: false,
    },
  },
  relations: {
    role: {
      target: Role.name,
      type: 'many-to-one',
      nullable: false,
      joinColumn: { name: 'roleId' },
    },
  },
  indices: [
    {
      name: 'users_phone_role_unique',
      columns: ['phoneNumber', 'roleId'],
      unique: true,
    },
    {
      name: 'users_email_role_unique',
      columns: ['email', 'roleId'],
      unique: true,
    },
  ],
});
