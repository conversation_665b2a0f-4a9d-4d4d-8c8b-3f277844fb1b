import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessesTable1748961000000 implements MigrationInterface {
  name = 'CreateBusinessesTable1748961000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type for price range
    await queryRunner.query(
      `CREATE TYPE "businesses_pricerange_enum" AS ENUM('₤', '₤₤', '₤₤₤')`,
    );

    // Create businesses table
    await queryRunner.query(
      `CREATE TABLE "businesses" (
        "id" SERIAL NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "nameAr" character varying NOT NULL,
        "nameEn" character varying NOT NULL,
        "phoneNumber" character varying,
        "whatsAppNumber" character varying,
        "priceRange" "businesses_pricerange_enum",
        "locationId" integer NOT NULL,
        "primaryCategoryId" integer NOT NULL,
        "operatingHours" jsonb,
        "isOpen24x7" boolean NOT NULL DEFAULT false,
        "ramadanHours" jsonb,
        "logoUrl" character varying,
        "coverPhotoUrl" character varying,
        "galleryUrls" jsonb DEFAULT '[]',
        "shortDescriptionAr" character varying(160),
        "shortDescriptionEn" character varying(160),
        "fullDescriptionAr" text,
        "fullDescriptionEn" text,
        "isActive" boolean NOT NULL DEFAULT true,
        "isVerified" boolean NOT NULL DEFAULT false,
        "isPremium" boolean NOT NULL DEFAULT false,
        "premiumExpiresAt" TIMESTAMP,
        "averageRating" decimal(3,2) NOT NULL DEFAULT 0,
        "totalReviewsCount" integer NOT NULL DEFAULT 0,
        "totalViewsCount" integer NOT NULL DEFAULT 0,
        "lastMonthViews" integer NOT NULL DEFAULT 0,
        "ownerUserId" integer NOT NULL,
        "paymentMethods" jsonb DEFAULT '[]',
        CONSTRAINT "PK_businesses" PRIMARY KEY ("id")
      )`,
    );

    // Create foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_businesses_location" FOREIGN KEY ("locationId") REFERENCES "locations"("id") ON DELETE CASCADE`,
    );

    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_businesses_category" FOREIGN KEY ("primaryCategoryId") REFERENCES "categories"("id") ON DELETE CASCADE`,
    );

    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_businesses_owner" FOREIGN KEY ("ownerUserId") REFERENCES "users"("id") ON DELETE CASCADE`,
    );

    // Create indices for better performance
    await queryRunner.query(
      `CREATE INDEX "businesses_name_ar_index" ON "businesses" ("nameAr")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_name_en_index" ON "businesses" ("nameEn")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_location_id_index" ON "businesses" ("locationId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_primary_category_id_index" ON "businesses" ("primaryCategoryId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_owner_user_id_index" ON "businesses" ("ownerUserId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_is_active_index" ON "businesses" ("isActive")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_is_verified_index" ON "businesses" ("isVerified")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_is_premium_index" ON "businesses" ("isPremium")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_average_rating_index" ON "businesses" ("averageRating")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_price_range_index" ON "businesses" ("priceRange")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_premium_expires_at_index" ON "businesses" ("premiumExpiresAt")`,
    );

    // Create business_features junction table
    await queryRunner.query(
      `CREATE TABLE "business_features" (
        "businessId" integer NOT NULL,
        "featureId" integer NOT NULL,
        CONSTRAINT "PK_business_features" PRIMARY KEY ("businessId", "featureId")
      )`,
    );

    // Create foreign keys for junction table
    await queryRunner.query(
      `ALTER TABLE "business_features" ADD CONSTRAINT "FK_business_features_business" FOREIGN KEY ("businessId") REFERENCES "businesses"("id") ON DELETE CASCADE`,
    );

    await queryRunner.query(
      `ALTER TABLE "business_features" ADD CONSTRAINT "FK_business_features_feature" FOREIGN KEY ("featureId") REFERENCES "features"("id") ON DELETE CASCADE`,
    );

    // Create indices for junction table
    await queryRunner.query(
      `CREATE INDEX "business_features_business_id_index" ON "business_features" ("businessId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "business_features_feature_id_index" ON "business_features" ("featureId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_payment_methods_gin_index" ON "businesses" USING GIN ("paymentMethods")`,
    );

    // Create composite indices for common queries
    await queryRunner.query(
      `CREATE INDEX "businesses_active_verified_index" ON "businesses" ("isActive", "isVerified")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_active_premium_index" ON "businesses" ("isActive", "isPremium")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_location_active_index" ON "businesses" ("locationId", "isActive")`,
    );

    await queryRunner.query(
      `CREATE INDEX "businesses_category_active_index" ON "businesses" ("primaryCategoryId", "isActive")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop composite indices
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_category_active_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_location_active_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_active_premium_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_active_verified_index"`,
    );

    // Drop junction table indices
    await queryRunner.query(
      `DROP INDEX IF EXISTS "business_features_feature_id_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "business_features_business_id_index"`,
    );

    // Drop junction table foreign keys
    await queryRunner.query(
      `ALTER TABLE "business_features" DROP CONSTRAINT IF EXISTS "FK_business_features_feature"`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" DROP CONSTRAINT IF EXISTS "FK_business_features_business"`,
    );

    // Drop junction table
    await queryRunner.query(`DROP TABLE IF EXISTS "business_features"`);

    // Drop GIN indices
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_payment_methods_gin_index"`,
    );

    // Drop regular indices
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_premium_expires_at_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_price_range_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_average_rating_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_is_premium_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_is_verified_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_is_active_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_owner_user_id_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_primary_category_id_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "businesses_location_id_index"`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS "businesses_name_en_index"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "businesses_name_ar_index"`);

    // Drop foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT IF EXISTS "FK_businesses_owner"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT IF EXISTS "FK_businesses_category"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT IF EXISTS "FK_businesses_location"`,
    );

    // Drop table
    await queryRunner.query(`DROP TABLE "businesses"`);

    // Drop enum type
    await queryRunner.query(`DROP TYPE "businesses_pricerange_enum"`);
  }
}
