import { IGenericService } from '@app/common';
import { CreateUserDto } from '../../dtos/user/create-user.dto';
import { GetAllUserDto } from '../../dtos/user/get-all-user.dto';
import { UpdateUserDto } from '../../dtos/user/update-user.dto';
import { ValidateUserDto } from '../../dtos/user/validate-user.dto';
import { User } from '../../entities/user.entity';
import { UserSchema } from '../../schemes/user.schema';

export interface IUserService
  extends IGenericService<
    User,
    typeof UserSchema,
    GetAllUserDto,
    CreateUserDto,
    UpdateUserDto
  > {
  validateUser(validateUserDto: ValidateUserDto): Promise<User | null>;

  findOneWithDetailsById(
    id: number,
    hideSensitiveInfo?: boolean,
  ): Promise<User | null>;
}

export const IUserService = Symbol('IUserService');
