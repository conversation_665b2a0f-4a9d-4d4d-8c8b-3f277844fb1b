import { NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import {
  Between,
  EntityManager,
  EntitySchema,
  FindManyOptions,
  FindOptionsOrder,
  FindOptionsWhere,
  In,
  Raw,
} from 'typeorm';
import { GetAllDto } from '../dtos';
import { BaseEntity } from '../entities';
import {
  IGenericRepository,
  IGenericService,
  IGetAllResponseInterface,
} from '../interfaces';

export class GenericService<
  Entity extends BaseEntity,
  Schema extends EntitySchema<Entity>,
  GetAllBaseDto extends GetAllDto,
  CreateDto,
  UpdateDto,
> implements IGenericService<Entity, Schema, GetAllDto, CreateDto, UpdateDto>
{
  constructor(
    private readonly repository: IGenericRepository<Entity, Schema>,
  ) {}

  async getAll(
    getAllDto: GetAllBaseDto,
    initialQuery?: FindManyOptions<Entity>,
  ): Promise<IGetAllResponseInterface<Entity>> {
    try {
      // Initialize query with type safety
      const query: FindManyOptions<Entity> = this.initializeQuery(
        getAllDto,
        initialQuery,
      );

      // Handle pagination
      this.applyPagination(query, getAllDto);

      // Handle date filters
      this.applyDateFilters(query, getAllDto);

      // Handle ID filtering
      if (getAllDto.ids) {
        this.applyIdFilter(query, getAllDto.ids);
      }

      // Handle sorting
      if (getAllDto.sortKey) {
        this.applySorting(query, getAllDto.sortKey);
      }

      // Handle search conditions
      if (getAllDto.searchKey) {
        query.where = this.buildSearchConditions(query, getAllDto);
      }

      // Execute query with timeout
      const [items, count] = (await Promise.race([
        this.repository.findAndCountAll(query),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Query timeout')), 30000),
        ),
      ])) as [Entity[], number];

      return { items, count };
    } catch (error) {
      // Log error for monitoring
      console.error('Error in getAll:', error);
      throw error;
    }
  }

  async findOneBy(query: FindManyOptions<Entity>): Promise<Entity | null> {
    return this.repository.findOneBy(query);
  }

  async findOneById(id: number, manager?: EntityManager): Promise<Entity> {
    const entity = manager
      ? await manager.findOne(this.repository.getEntity(), {
          where: {
            id: id as any,
          },
        })
      : await this.repository.findOneById(id);
    if (!entity) {
      throw new NotFoundException('Entity not found');
    }
    return entity;
  }

  async create(
    entityType: new () => Entity,
    createDto: CreateDto,
    entityManager?: EntityManager,
  ): Promise<Entity> {
    const entity = plainToInstance(entityType, createDto);
    if (!entityManager) {
      await this.repository.createOne(entity);
      return entity;
    }
    return entityManager.save(entity);
  }

  async update(
    entityType: new () => Entity,
    updateDto: UpdateDto,
    entityManager?: EntityManager,
  ): Promise<Entity> {
    const entity = plainToInstance(entityType, updateDto);
    const existingEntity = await this.findOneById(entity.id, entityManager);
    if (!entityManager) {
      await this.repository.updateOne(entity);
      return entity;
    }
    return entityManager.save(entity);
  }

  async createMany(
    entityType: new () => Entity,
    createDto: CreateDto[],
    entityManager?: EntityManager,
  ): Promise<Entity[]> {
    const entities = plainToInstance(entityType, createDto);
    if (!entityManager) {
      await this.repository.createMany(entities);
      return entities;
    }
    return entityManager.save(entities);
  }

  async updateMany(
    entityType: new () => Entity,
    updateDto: UpdateDto[],
    entityManager?: EntityManager,
  ): Promise<Entity[]> {
    const entities = plainToInstance(entityType, updateDto);
    if (!entityManager) {
      await this.repository.updateMany(entities);
      return entities;
    }
    return entityManager.save(entities);
  }

  async save(
    entities: Entity[],
    entityManager?: EntityManager,
  ): Promise<Entity[]> {
    if (!entityManager) {
      await this.repository.save(entities);
      return entities;
    }
    return entityManager.save(entities);
  }

  async deleteOne(id: number): Promise<void> {
    const entity = await this.repository.findOneById(id);
    if (!entity) {
      throw new NotFoundException('Entity not found');
    }
    await this.repository.deleteOne(id);
  }

  private initializeQuery(
    getAllDto: GetAllBaseDto,
    initialQuery?: FindManyOptions<Entity>,
  ): FindManyOptions<Entity> {
    return {
      ...initialQuery,
      where: initialQuery?.where || {},
      withDeleted: !!getAllDto.withDeleted,
    };
  }

  private applyPagination(
    query: FindManyOptions<Entity>,
    getAllDto: GetAllBaseDto,
  ): void {
    if (getAllDto.limit) {
      query.take = Math.min(getAllDto.limit, 100); // Prevent large queries
    }
    if (getAllDto.offset) {
      query.skip = getAllDto.offset; // Offset is the actual record offset (0, 1, 2, 3...)
    }
  }

  private applyDateFilters(
    query: FindManyOptions<Entity>,
    getAllDto: GetAllBaseDto,
  ): void {
    if (getAllDto.createdAtFrom && getAllDto.createdAtTo) {
      query.where = {
        ...query.where,
        createdAt: Between(
          new Date(getAllDto.createdAtFrom),
          new Date(getAllDto.createdAtTo),
        ),
      } as FindOptionsWhere<Entity>;
    }

    if (getAllDto.updateAtFrom && getAllDto.updateAtTo) {
      query.where = {
        ...query.where,
        updatedAt: Between(
          new Date(getAllDto.updateAtFrom),
          new Date(getAllDto.updateAtTo),
        ),
      } as FindOptionsWhere<Entity>;
    }
  }

  private applyIdFilter(query: FindManyOptions<Entity>, ids: string): void {
    const parsedIds = ids
      .split(',')
      .map((id) => parseInt(id))
      .filter((id) => !isNaN(id));

    if (parsedIds.length) {
      query.where = {
        ...query.where,
        id: In(parsedIds),
      } as FindOptionsWhere<Entity>;
    }
  }

  private applySorting(query: FindManyOptions<Entity>, sortKey: string): void {
    const [key, direction] = sortKey.split(':');
    if (key) {
      query.order = {
        [key]: direction === '1' ? 'ASC' : 'DESC',
      } as FindOptionsOrder<Entity>;
    }
  }

  private buildSearchConditions(
    query: FindManyOptions<Entity>,
    getAllDto: GetAllBaseDto,
  ): FindOptionsWhere<Entity>[] {
    const whereConditions: FindOptionsWhere<Entity>[] = [];

    if (getAllDto.searchOnJson) {
      this.addJsonSearchConditions(whereConditions, query, getAllDto);
    }

    if (getAllDto.searchOnRelation) {
      this.addRelationSearchConditions(whereConditions, query, getAllDto);
    }

    return whereConditions;
  }

  private addJsonSearchConditions(
    whereConditions: FindOptionsWhere<Entity>[],
    query: FindManyOptions<Entity>,
    getAllDto: GetAllBaseDto,
  ): void {
    if (!getAllDto.searchOnJson) return;

    const searchFields = getAllDto.searchOnJson.split(',');
    const conditions = searchFields.map((field) =>
      this.createJsonSearchCondition(field, query, getAllDto.searchKey),
    );
    whereConditions.push(...conditions);
  }

  private createJsonSearchCondition(
    field: string,
    query: FindManyOptions<Entity>,
    searchKey: string | undefined,
  ): FindOptionsWhere<Entity> {
    if (!searchKey) return {};

    if (field.includes('.')) {
      const [parentField, childField] = field.split('.');
      return {
        ...query.where,
        [parentField]: Raw(
          (alias) =>
            `CAST(${alias} AS JSONB)->>'${childField}' ILIKE :searchValue`,
          { searchValue: `%${searchKey}%` },
        ),
      } as FindOptionsWhere<Entity>;
    }
    return {
      ...query.where,
      [field]: Raw((alias) => `CAST(${alias} AS TEXT) ILIKE :searchValue`, {
        searchValue: `%${searchKey}%`,
      }),
    } as FindOptionsWhere<Entity>;
  }

  private addRelationSearchConditions(
    whereConditions: FindOptionsWhere<Entity>[],
    query: FindManyOptions<Entity>,
    getAllDto: GetAllBaseDto,
  ): void {
    if (!getAllDto.searchOnRelation || !getAllDto.searchKey) return;

    const fields = getAllDto.searchOnRelation.split(',');
    const conditions = fields.map((field) => {
      const parts = field.split('.');
      return parts.reduceRight((acc, part, index) => {
        if (index === parts.length - 1) {
          if (part.includes('_')) {
            const [nestedField, jsonField] = part.split('_');
            return {
              ...query.where,
              [nestedField]: Raw(
                (alias) =>
                  `CAST(${alias} AS JSONB)->>'${jsonField}' ILIKE :searchValue`,
                { searchValue: `%${getAllDto.searchKey}%` },
              ),
            };
          }
          return {
            ...query.where,
            [part]: Raw(
              (alias) => `CAST(${alias} AS TEXT) ILIKE :searchValue`,
              {
                searchValue: `%${getAllDto.searchKey}%`,
              },
            ),
          };
        }
        return { [part]: acc };
      }, {});
    });
    whereConditions.push(...conditions);
  }
}
