import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUrl,
  Min,
  ValidateNested,
} from 'class-validator';
import { BaseEntity } from '../../../common';

export class Category extends BaseEntity {
  @IsNotEmpty()
  @IsString()
  nameEn: string;

  @IsNotEmpty()
  @IsString()
  nameAr: string;

  @IsOptional()
  @IsString()
  descriptionEn?: string;

  @IsOptional()
  @IsString()
  descriptionAr?: string;

  @IsOptional()
  @IsString()
  icon?: string;

  @IsOptional()
  @IsString()
  @IsUrl()
  cover?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  numberOfBusinesses: number = 0;

  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder: number = 0;

  @IsOptional()
  @IsNumber()
  level: number = 0;

  @IsOptional()
  @IsString()
  path?: string;

  @IsOptional()
  @IsNumber()
  parentId?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => Category)
  parent?: Category;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Category)
  children?: Category[];
}
