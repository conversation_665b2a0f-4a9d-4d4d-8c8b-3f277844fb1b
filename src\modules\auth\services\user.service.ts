import { GenericService, IGetAllResponseInterface } from '@app/common';
import {
  BadRequestException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import * as argon2 from 'argon2';
import { CreateUserDto } from '../dtos/user/create-user.dto';
import { GetAllUserDto } from '../dtos/user/get-all-user.dto';
import { UpdateUserDto } from '../dtos/user/update-user.dto';
import { ValidateUserDto } from '../dtos/user/validate-user.dto';
import { Role } from '../entities/role.entity';
import { User } from '../entities/user.entity';
import { UserStatus } from '../enums/user-status.enum';
import { IRoleService } from '../interfaces/role/role-service.interface';
import { IUserRepository } from '../interfaces/user/user-repository.interface';
import { IUserService } from '../interfaces/user/user-service.interface';
import { UserSchema } from '../schemes/user.schema';
import { FindManyOptions } from 'typeorm';

@Injectable()
export class UserService
  extends GenericService<
    User,
    typeof UserSchema,
    GetAllUserDto,
    CreateUserDto,
    UpdateUserDto
  >
  implements IUserService
{
  constructor(
    @Inject(IRoleService) private roleService: IRoleService,
    @Inject(IUserRepository) private readonly userRepository: IUserRepository,
  ) {
    super(userRepository);
  }

  async getAll(
    getAllDto: GetAllUserDto,
    initialQuery?: FindManyOptions<User>,
  ): Promise<IGetAllResponseInterface<User>> {
    initialQuery = initialQuery || {};
    initialQuery['where'] = initialQuery['where'] || {};

    if (getAllDto.roleName) {
      const role = await this.roleService.findByName(getAllDto.roleName);
      getAllDto.roleId = role.id;
    }

    if (getAllDto.roleId) initialQuery.where['roleId'] = getAllDto.roleId;
    if (getAllDto.userId) initialQuery.where['id'] = getAllDto.userId;
    if (getAllDto.status) initialQuery.where['status'] = getAllDto.status;
    if (getAllDto.searchKey)
      getAllDto.searchOnJson = 'firstName,lastName,email,phoneNumber';

    return super.getAll(getAllDto, initialQuery);
  }

  async validateUser(validateUserDto: ValidateUserDto) {
    let role: Role | undefined;
    if (validateUserDto.roleName) {
      role = await this.roleService.findByName(validateUserDto.roleName);
    }

    const user = await this.userRepository.findUserByEmailOrPhone(
      validateUserDto.loginId,
      role ? role.id : undefined,
    );

    if (!user) {
      throw new NotFoundException({
        message: 'Invalid loginId or password',
        statusCode: HttpStatus.BAD_REQUEST,
      });
    }

    if (validateUserDto.passwordValidation) {
      const passwordValid = await argon2.verify(
        user.password,
        validateUserDto.password,
      );

      if (!passwordValid) {
        throw new BadRequestException({
          message: 'Make sure loginId and password are correct.',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }
    }

    if (user.status !== UserStatus.ACTIVE)
      throw new BadRequestException({
        message: 'User is not active, Please contact admin.',
        statusCode: HttpStatus.BAD_REQUEST,
      });

    return user;
  }

  async findOneWithDetailsById(
    id: number,
    hideSensitiveInfo?: boolean,
  ): Promise<User | null> {
    const user = await this.userRepository.findOneWithDetailsById(id);

    return user;
  }
}
