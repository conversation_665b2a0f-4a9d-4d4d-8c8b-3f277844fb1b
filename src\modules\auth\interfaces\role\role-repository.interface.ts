import { IGenericRepository } from '@app/common';
import { Permission } from '../../entities/permission.entity';
import { Role } from '../../entities/role.entity';
import { RoleSchema } from '../../schemes/role.schema';
import { EntityManager } from 'typeorm';

export interface IRoleRepository
  extends IGenericRepository<Role, typeof RoleSchema> {
  findOneWithPermissions(id: number): Promise<Role | null>;
  addNewPermission(
    role: Role,
    permission: Permission,
    manager?: EntityManager,
  ): Promise<Role | null>;
  findByName(name: string): Promise<Role | null>;
}

export const IRoleRepository = Symbol('IRoleRepository');
