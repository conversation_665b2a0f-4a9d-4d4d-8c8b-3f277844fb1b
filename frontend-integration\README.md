# Qareeb Plus Frontend Integration Guide

This guide will help you integrate your frontend application (React, Vue, Angular, or vanilla JavaScript) with the Qareeb Plus Backend API.

## 📋 Table of Contents

1. [Quick Start](#quick-start)
2. [Installation](#installation)
3. [Authentication Setup](#authentication-setup)
4. [API Client Usage](#api-client-usage)
5. [React Integration](#react-integration)
6. [Vue.js Integration](#vuejs-integration)
7. [Angular Integration](#angular-integration)
8. [Vanilla JavaScript Integration](#vanilla-javascript-integration)
9. [Security Considerations](#security-considerations)
10. [Error Handling](#error-handling)
11. [Best Practices](#best-practices)
12. [Troubleshooting](#troubleshooting)

## 🚀 Quick Start

### 1. Copy Integration Files

Copy these files to your frontend project:
- `api-client.ts` - Core API client
- `react-hooks.ts` - React hooks (if using React)
- `examples.tsx` - Implementation examples

### 2. Basic Setup

```typescript
import QareebApiClient from './api-client';

// Initialize the API client
const api = new QareebApiClient('http://localhost:3000/v1');

// Option 1: Guest Access (no authentication required for read operations)
api.enableGuestMode();
const businesses = await api.businesses.getAll();
const categories = await api.categories.getAll();

// Option 2: Authenticated Access
const authResponse = await api.auth.login({
  loginId: '<EMAIL>',
  password: 'password'
});

api.setAccessToken(authResponse.accessToken);

// Now you can access all endpoints including create/update/delete
const newBusiness = await api.businesses.create({...});
```

## 📦 Installation

### Prerequisites

Make sure your project has these dependencies:

```bash
# For TypeScript projects
npm install typescript

# For React projects
npm install react @types/react

# For fetch polyfill (if targeting older browsers)
npm install whatwg-fetch
```

### Backend Configuration

Ensure your backend is configured to allow your frontend domain:

1. **CORS Configuration**: Update `src/common/functions/init-security.function.ts`:

```typescript
const whitelist: RegExp[] = [
  new RegExp('.*localhost.*'),
  new RegExp('.*yourdomain\.com.*'), // Add your domain
];
```

2. **Environment Variables**: Update your `.env` file:

```env
# Add your frontend URLs
FRONTEND_URL=http://localhost:3000
```

## 👤 Guest Access

The API supports guest access for read-only operations. Guest users can browse businesses, categories, locations, and features without authentication.

### Enabling Guest Mode

```typescript
import QareebApiClient from './api-client';

const api = new QareebApiClient('http://localhost:3000/v1');

// Enable guest mode
api.enableGuestMode();

// Check if in guest mode
console.log(api.isGuestMode()); // true
console.log(api.isAuthenticated()); // false

// Guest users can access all read operations
const businesses = await api.businesses.getAll();
const business = await api.businesses.getById(123);
const categories = await api.categories.getHierarchy();
const locations = await api.locations.getAll();
const features = await api.features.getAll();

// Search and filter as guest
const nearbyBusinesses = await api.businesses.getNearby(40.7128, -74.0060, 5);
const verifiedBusinesses = await api.businesses.getVerified();
const categoryBusinesses = await api.businesses.getByCategory(5);
```

### Guest Limitations

Guest users cannot:
- Create, update, or delete businesses
- Access user management endpoints
- Perform admin operations
- Access protected user data

### Switching from Guest to Authenticated

```typescript
// Start as guest
api.enableGuestMode();
const guestBusinesses = await api.businesses.getAll();

// Login later
const authResponse = await api.auth.login({
  loginId: '<EMAIL>',
  password: 'password'
});

api.setAccessToken(authResponse.accessToken);

// Now you have full access
const newBusiness = await api.businesses.create({
  nameEn: 'My Business',
  locationId: 1,
  primaryCategoryId: 5
});
```

## 🔐 Authentication Setup

### Token Management

The API uses JWT tokens for authentication. Here's how to handle them:

```typescript
// Login and store tokens
const login = async (credentials) => {
  const response = await api.auth.login(credentials);
  
  // Store tokens securely
  localStorage.setItem('accessToken', response.accessToken);
  localStorage.setItem('refreshToken', response.refreshToken);
  
  // Set token for API client
  api.setAccessToken(response.accessToken);
  
  return response;
};

// Initialize client with stored token
const initializeApi = () => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    api.setAccessToken(token);
  }
};

// Logout and clear tokens
const logout = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  api.clearAccessToken();
};
```

### Token Refresh

Implement automatic token refresh:

```typescript
const refreshAccessToken = async () => {
  const refreshToken = localStorage.getItem('refreshToken');
  if (!refreshToken) throw new Error('No refresh token available');
  
  const response = await api.auth.refresh({ refreshToken });
  
  localStorage.setItem('accessToken', response.accessToken);
  api.setAccessToken(response.accessToken);
  
  return response;
};

// Intercept API errors and refresh token if needed
api.interceptors = {
  response: async (response) => {
    if (response.status === 401) {
      try {
        await refreshAccessToken();
        // Retry the original request
        return fetch(response.url, response);
      } catch (error) {
        // Redirect to login
        window.location.href = '/login';
      }
    }
    return response;
  }
};
```

## 🛠 API Client Usage

### Basic CRUD Operations

```typescript
// Get all businesses with filtering
const businesses = await api.businesses.getAll({
  limit: 20,
  offset: 0,
  searchKey: 'restaurant',
  isVerified: true,
  minRating: 4.0
});

// Get business by ID
const business = await api.businesses.getById(123);

// Create new business
const newBusiness = await api.businesses.create({
  nameEn: 'My Restaurant',
  nameAr: 'مطعمي',
  locationId: 1,
  primaryCategoryId: 5,
  phoneNumber: '+1234567890'
});

// Update business
const updatedBusiness = await api.businesses.update(123, {
  isActive: true,
  shortDescriptionEn: 'Best restaurant in town'
});
```

### Location-Based Queries

```typescript
// Find nearby businesses
const nearbyBusinesses = await api.businesses.getNearby(
  40.7128, // latitude
  -74.0060, // longitude
  5 // radius in km
);

// Get businesses by city
const cityBusinesses = await api.businesses.getByCity('New York');

// Calculate distance between locations
const distance = await api.locations.calculateDistance(
  40.7128, -74.0060, // NYC
  34.0522, -118.2437  // LA
);
```

### Category Navigation

```typescript
// Get category hierarchy
const categories = await api.categories.getHierarchy();

// Get businesses in a category
const categoryBusinesses = await api.businesses.getByCategory(5);

// Get child categories
const childCategories = await api.categories.getByParent(1);
```

## ⚛️ React Integration

### Setup Provider

```tsx
import { QareebApiProvider } from './react-hooks';

function App() {
  return (
    <QareebApiProvider baseUrl="http://localhost:3000/v1">
      <YourAppComponents />
    </QareebApiProvider>
  );
}
```

### Use Authentication Hook

```tsx
import { useAuth } from './react-hooks';

function LoginComponent() {
  const { login, logout, user, isAuthenticated, loading, error } = useAuth();
  
  const handleLogin = async (credentials) => {
    await login(credentials);
  };
  
  if (isAuthenticated) {
    return (
      <div>
        <h1>Welcome, {user?.firstName}!</h1>
        <button onClick={logout}>Logout</button>
      </div>
    );
  }
  
  return <LoginForm onLogin={handleLogin} />;
}
```

### Use Business Hook

```tsx
import { useBusinesses } from './react-hooks';

function BusinessList() {
  const { businesses, loading, error, refetch } = useBusinesses({
    limit: 20,
    isVerified: true
  });
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      {businesses.map(business => (
        <BusinessCard key={business.id} business={business} />
      ))}
    </div>
  );
}
```

## 🟢 Vue.js Integration

### Create Composables

```typescript
// composables/useQareebApi.ts
import { ref, reactive } from 'vue';
import QareebApiClient from '../api-client';

const api = new QareebApiClient('http://localhost:3000/v1');

export function useAuth() {
  const user = ref(null);
  const isAuthenticated = ref(false);
  const loading = ref(false);
  const error = ref(null);
  
  const login = async (credentials) => {
    try {
      loading.value = true;
      error.value = null;
      
      const response = await api.auth.login(credentials);
      api.setAccessToken(response.accessToken);
      
      const userProfile = await api.users.getProfile();
      user.value = userProfile;
      isAuthenticated.value = true;
      
      return response;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  const logout = () => {
    api.clearAccessToken();
    user.value = null;
    isAuthenticated.value = false;
  };
  
  return {
    user,
    isAuthenticated,
    loading,
    error,
    login,
    logout
  };
}

export function useBusinesses(params = {}) {
  const businesses = ref([]);
  const loading = ref(false);
  const error = ref(null);
  
  const fetchBusinesses = async (newParams = {}) => {
    try {
      loading.value = true;
      error.value = null;
      
      const response = await api.businesses.getAll({ ...params, ...newParams });
      businesses.value = response.items || [];
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };
  
  return {
    businesses,
    loading,
    error,
    fetchBusinesses
  };
}
```

### Use in Components

```vue
<template>
  <div>
    <div v-if="loading">Loading...</div>
    <div v-else-if="error">Error: {{ error }}</div>
    <div v-else>
      <BusinessCard 
        v-for="business in businesses" 
        :key="business.id" 
        :business="business" 
      />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useBusinesses } from '@/composables/useQareebApi';

const { businesses, loading, error, fetchBusinesses } = useBusinesses();

onMounted(() => {
  fetchBusinesses({ isVerified: true });
});
</script>
```

## 🅰️ Angular Integration

### Create Services

```typescript
// services/qareeb-api.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import QareebApiClient, { User, Business } from '../api-client';

@Injectable({
  providedIn: 'root'
})
export class QareebApiService {
  private api = new QareebApiClient('http://localhost:3000/v1');
  private userSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  
  public user$ = this.userSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  
  async login(credentials: any): Promise<void> {
    const response = await this.api.auth.login(credentials);
    this.api.setAccessToken(response.accessToken);
    
    const user = await this.api.users.getProfile();
    this.userSubject.next(user);
    this.isAuthenticatedSubject.next(true);
  }
  
  logout(): void {
    this.api.clearAccessToken();
    this.userSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }
  
  async getBusinesses(params?: any): Promise<Business[]> {
    const response = await this.api.businesses.getAll(params);
    return response.items || [];
  }
}
```

### Use in Components

```typescript
// components/business-list.component.ts
import { Component, OnInit } from '@angular/core';
import { QareebApiService } from '../services/qareeb-api.service';

@Component({
  selector: 'app-business-list',
  template: `
    <div *ngIf="loading">Loading...</div>
    <div *ngFor="let business of businesses">
      <app-business-card [business]="business"></app-business-card>
    </div>
  `
})
export class BusinessListComponent implements OnInit {
  businesses: any[] = [];
  loading = false;
  
  constructor(private qareebApi: QareebApiService) {}
  
  async ngOnInit() {
    this.loading = true;
    try {
      this.businesses = await this.qareebApi.getBusinesses({ isVerified: true });
    } catch (error) {
      console.error('Failed to load businesses:', error);
    } finally {
      this.loading = false;
    }
  }
}
```

## 🟨 Vanilla JavaScript Integration

```javascript
// main.js
import QareebApiClient from './api-client.js';

class QareebApp {
  constructor() {
    this.api = new QareebApiClient('http://localhost:3000/v1');
    this.initializeAuth();
  }
  
  initializeAuth() {
    const token = localStorage.getItem('accessToken');
    if (token) {
      this.api.setAccessToken(token);
      this.loadUserProfile();
    }
  }
  
  async login(credentials) {
    try {
      const response = await this.api.auth.login(credentials);
      localStorage.setItem('accessToken', response.accessToken);
      this.api.setAccessToken(response.accessToken);
      
      await this.loadUserProfile();
      this.showDashboard();
    } catch (error) {
      this.showError('Login failed: ' + error.message);
    }
  }
  
  async loadBusinesses() {
    try {
      const businesses = await this.api.businesses.getAll({
        limit: 20,
        isVerified: true
      });
      
      this.renderBusinesses(businesses.items);
    } catch (error) {
      this.showError('Failed to load businesses: ' + error.message);
    }
  }
  
  renderBusinesses(businesses) {
    const container = document.getElementById('business-list');
    container.innerHTML = businesses.map(business => `
      <div class="business-card">
        <h3>${business.nameEn}</h3>
        <p>${business.shortDescriptionEn || 'No description'}</p>
        <div class="rating">Rating: ${business.averageRating || 'No rating'}</div>
      </div>
    `).join('');
  }
}

// Initialize app
const app = new QareebApp();
```

## 🔒 Security Considerations

### 1. Token Storage

**Recommended:** Use `httpOnly` cookies for production:

```typescript
// Backend: Set httpOnly cookie
res.cookie('accessToken', token, {
  httpOnly: true,
  secure: true,
  sameSite: 'strict'
});

// Frontend: Token is automatically sent with requests
```

**Development:** localStorage is acceptable for development:

```typescript
localStorage.setItem('accessToken', token);
```

### 2. Environment Variables

Use environment variables for configuration:

```typescript
// config.ts
export const API_CONFIG = {
  baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000/v1',
  timeout: 10000,
};
```

### 3. Input Validation

Always validate user inputs:

```typescript
const validateBusinessData = (data) => {
  if (!data.nameEn || data.nameEn.length < 2) {
    throw new Error('Business name must be at least 2 characters');
  }
  if (data.phoneNumber && !/^\+?[\d\s-()]+$/.test(data.phoneNumber)) {
    throw new Error('Invalid phone number format');
  }
};
```

## ❌ Error Handling

### Global Error Handler

```typescript
class ApiErrorHandler {
  static handle(error) {
    if (error.message.includes('401')) {
      // Redirect to login
      window.location.href = '/login';
    } else if (error.message.includes('403')) {
      // Show permission denied message
      this.showError('Permission denied');
    } else if (error.message.includes('500')) {
      // Show server error message
      this.showError('Server error. Please try again later.');
    } else {
      // Show generic error
      this.showError(error.message);
    }
  }
  
  static showError(message) {
    // Implement your error display logic
    console.error(message);
  }
}

// Use in API calls
try {
  const businesses = await api.businesses.getAll();
} catch (error) {
  ApiErrorHandler.handle(error);
}
```

### Retry Logic

```typescript
const retryRequest = async (fn, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)));
    }
  }
};

// Usage
const businesses = await retryRequest(() => api.businesses.getAll());
```

## ✨ Best Practices

### 1. Caching

Implement client-side caching for better performance:

```typescript
class CacheManager {
  private cache = new Map();
  private ttl = 5 * 60 * 1000; // 5 minutes
  
  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
}
```

### 2. Pagination

Implement proper pagination:

```typescript
const usePagination = (fetchFn) => {
  const [items, setItems] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  
  const loadMore = async () => {
    if (loading || !hasMore) return;
    
    setLoading(true);
    try {
      const response = await fetchFn({ 
        offset: items.length,
        limit: 20 
      });
      
      setItems(prev => [...prev, ...response.items]);
      setHasMore(response.items.length === 20);
    } finally {
      setLoading(false);
    }
  };
  
  return { items, loadMore, hasMore, loading };
};
```

### 3. Performance Optimization

Use request debouncing for search:

```typescript
import { debounce } from 'lodash';

const useSearch = () => {
  const [results, setResults] = useState([]);
  
  const debouncedSearch = debounce(async (query) => {
    if (!query) return setResults([]);
    
    const businesses = await api.businesses.getAll({
      searchKey: query,
      limit: 10
    });
    
    setResults(businesses.items);
  }, 300);
  
  return { results, search: debouncedSearch };
};
```

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure your domain is added to the backend whitelist
   - Check that the backend is running on the correct port

2. **Authentication Errors**
   - Verify token is being sent in headers
   - Check token expiration
   - Ensure user has required permissions

3. **Network Errors**
   - Check backend server is running
   - Verify API endpoint URLs
   - Check network connectivity

### Debug Mode

Enable debug logging:

```typescript
const api = new QareebApiClient('http://localhost:3000/v1', {
  debug: true,
  onRequest: (url, options) => console.log('Request:', url, options),
  onResponse: (response) => console.log('Response:', response),
  onError: (error) => console.error('API Error:', error)
});
```

### Testing

Test your integration with sample data:

```typescript
// test-api.js
const testApi = async () => {
  try {
    // Test authentication
    const authResponse = await api.auth.login({
      loginId: '<EMAIL>',
      password: 'testpassword'
    });
    console.log('✅ Authentication successful');
    
    // Test business listing
    const businesses = await api.businesses.getAll({ limit: 5 });
    console.log('✅ Business listing successful:', businesses.items.length);
    
    // Test location search
    const nearby = await api.businesses.getNearby(40.7128, -74.0060, 5);
    console.log('✅ Location search successful:', nearby.length);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

testApi();
```

## 📞 Support

If you encounter issues:

1. Check the [API documentation](http://localhost:3000/v1/api-docs)
2. Review the console for error messages
3. Verify your authentication tokens
4. Ensure proper CORS configuration

For additional help, contact the development team or create an issue in the project repository.