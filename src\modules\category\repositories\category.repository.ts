import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import { GenericRepository } from '@app/common';
import { Category } from '../entities/category.entity';
import { CategorySchema } from '../schemes/category.schema';
import { ICategoryRepository } from '../interfaces/category-repository/category-repository.interface';

@Injectable()
export class CategoryRepository
  extends GenericRepository<Category, typeof CategorySchema>
  implements ICategoryRepository
{
  constructor(
    @InjectRepository(CategorySchema)
    private readonly categoryRepository: Repository<Category>,
  ) {
    super(categoryRepository);
  }

  async findByParentId(parentId: number | null): Promise<Category[]> {
    return this.categoryRepository.find({
      where: { parentId: parentId === null ? IsNull() : parentId },
      order: { sortOrder: 'ASC', nameEn: 'ASC' },
    });
  }

  async findByLevel(level: number): Promise<Category[]> {
    return this.categoryRepository.find({
      where: { level },
      order: { sortOrder: 'ASC', nameEn: 'ASC' },
    });
  }

  async findWithChildren(id: number): Promise<Category | null> {
    return this.categoryRepository.findOne({
      where: { id },
      relations: ['children', 'parent'],
    });
  }

  async findHierarchy(): Promise<Category[]> {
    return this.categoryRepository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.children', 'children')
      .leftJoinAndSelect('category.parent', 'parent')
      .orderBy('category.level', 'ASC')
      .addOrderBy('category.sortOrder', 'ASC')
      .addOrderBy('category.nameEn', 'ASC')
      .getMany();
  }

  async updateNumberOfBusinesses(id: number, count: number): Promise<void> {
    await this.categoryRepository.update(id, { numberOfBusinesses: count });
  }
}
