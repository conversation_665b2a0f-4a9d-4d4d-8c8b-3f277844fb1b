import { ValidationPipe } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';

export function getValidationPipe() {
  return new ValidationPipe({
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
      exposeDefaultValues: true,
      exposeUnsetFields: false,
      enableCircularCheck: true,
    },
    skipMissingProperties: false,
    skipUndefinedProperties: false,
    skipNullProperties: false,
    forbidUnknownValues: true,
    forbidNonWhitelisted: true,
    whitelist: true,
    validationError: {
      target: true,
      value: true,
    },
    enableDebugMessages: true,
    disableErrorMessages: false,
    stopAtFirstError: false,
  });
}

export function initValidationPipeline(app: NestExpressApplication) {
  app.useGlobalPipes(getValidationPipe());
}
