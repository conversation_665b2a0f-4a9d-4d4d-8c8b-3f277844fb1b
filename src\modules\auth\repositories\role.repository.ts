import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GenericRepository } from '@app/common';
import { EntityManager, Repository } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';
import { IRoleRepository } from '../interfaces/role/role-repository.interface';
import { RoleSchema } from '../schemes/role.schema';

@Injectable()
export class RoleRepository
  extends GenericRepository<Role, typeof RoleSchema>
  implements IRoleRepository
{
  constructor(
    @InjectRepository(RoleSchema)
    private readonly roleOrmRepository: Repository<Role>,
  ) {
    super(roleOrmRepository);
  }

  async findOneWithPermissions(id: number): Promise<Role | null> {
    const role = await this.roleOrmRepository.findOne({
      where: { id },
      relations: ['permissions'],
    });

    if (!role) {
      return null;
    }

    // Ensure permissions is always an array
    if (!role.permissions) {
      role.permissions = [];
    }

    return role;
  }

  async addNewPermission(
    role: Role,
    permission: Permission,
    manager?: EntityManager,
  ): Promise<Role | null> {
    if (!role.permissions) {
      role.permissions = [];
    }

    // Check if permission already exists in role's permissions
    const permissionExists = role.permissions.some(
      (p) => p.id === permission.id,
    );

    if (!permissionExists) {
      // If using a transaction manager, use query builder for direct relation mapping
      if (manager) {
        await manager
          .createQueryBuilder()
          .relation(Role, 'permissions')
          .of(role)
          .add(permission);
      } else {
        // For non-transactional operations, use the standard approach
        role.permissions.push(permission);
        await this.roleOrmRepository.save(role);
      }
    }

    // Reload the role to ensure we have the latest data
    return (
      manager?.findOne(Role, {
        where: { id: role.id },
        relations: ['permissions'],
      }) || this.findOneWithPermissions(role.id)
    );
  }

  async findByName(name: string): Promise<Role | null> {
    return await this.roleOrmRepository.findOne({
      where: { name },
      relations: ['permissions'],
    });
  }
}
