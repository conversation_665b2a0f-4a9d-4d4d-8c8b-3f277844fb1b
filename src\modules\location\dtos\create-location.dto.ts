import { PickType } from '@nestjs/swagger';
import { Location } from '../entities/location.entity';

export class CreateLocationDto extends PickType(Location, [
  'nameEn',
  'nameAr',
  'latitude',
  'longitude',
  'streetAddressEn',
  'streetAddressAr',
  'cityEn',
  'cityAr',
  'stateEn',
  'stateAr',
  'countryEn',
  'countryAr',
  'postalCode',
  'nearestLandmarkEn',
  'nearestLandmarkAr',
  'descriptionEn',
  'descriptionAr',
  'mapUrl',
  'placeId',
  'accuracy',
  'altitude',
  'timezone',
  'parentId',
] as const) {}
