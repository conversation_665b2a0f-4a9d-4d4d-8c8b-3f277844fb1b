{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS", "runtimeExecutable": "pnpm", "runtimeArgs": ["run", "start:debug"], "autoAttachChildProcesses": true, "restart": true, "sourceMaps": true, "stopOnEntry": false, "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**", "node_modules/**"], "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"type": "node", "request": "attach", "name": "Attach to NestJS", "port": 9229, "restart": true, "stopOnEntry": false, "sourceMaps": true, "skipFiles": ["<node_internals>/**", "node_modules/**"], "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"type": "node", "request": "launch", "name": "Debug Jest Tests", "runtimeExecutable": "pnpm", "runtimeArgs": ["test", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "--forceExit"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug Current Test File", "runtimeExecutable": "pnpm", "runtimeArgs": ["test", "${relativeFile}", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "--forceExit"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug E2E Tests", "runtimeExecutable": "pnpm", "runtimeArgs": ["test:e2e", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "--forceExit"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**", "node_modules/**"]}]}