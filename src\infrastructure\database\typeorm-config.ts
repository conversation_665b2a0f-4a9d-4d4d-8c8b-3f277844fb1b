import { config as dotenvConfig } from 'dotenv';
import { DataSource } from 'typeorm';
import * as path from 'path';

// Load environment variables
dotenvConfig({ path: `./.env` });

console.log(
  'Database URL:',
  process.env.DATABASE_URL?.substring(0, 20) + '...',
);

// Create a DataSource configuration for migrations
const dataSource = new DataSource({
  type: 'postgres',
  url: process.env.DATABASE_URL,
  entities: [
    path.resolve(__dirname, '../../modules/**/*.entity.ts'),
    path.resolve(__dirname, '../../modules/**/*.schema.ts'),
  ],
  migrations: [path.resolve(__dirname, './migrations/*.ts')],
  migrationsTableName: 'migrations',
  logging: true,
  synchronize: false,
  migrationsTransactionMode: 'all',
});

console.log('TypeORM DataSource configuration:');
console.log('- Entity patterns:', dataSource.options.entities);
console.log('- Migration patterns:', dataSource.options.migrations);

export default dataSource;
