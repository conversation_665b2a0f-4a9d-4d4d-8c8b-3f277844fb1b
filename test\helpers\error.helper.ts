import * as request from 'supertest';

/**
 * Interface for error response structure from the API
 */
export interface ErrorResponse {
  statusCode: number;
  message: string | string[];
  error: string;
  meta?: {
    validationErrors?: Array<{
      property: string;
      constraints: Record<string, string>;
      children?: any[];
    }>;
    requestId?: string;
    path?: string;
    method?: string;
    ip?: string;
    userAgent?: string;
    userId?: string;
    [key: string]: any;
  };
  stack?: string;
}

/**
 * Helper class for handling and displaying error responses in e2e tests
 */
export class ErrorHelper {
  /**
   * Display detailed error information for debugging purposes
   * @param testName - Name of the test case for context
   * @param response - The supertest response object
   */
  static displayErrorDetails(
    testName: string,
    response: request.Response,
  ): void {
    console.log(`\n=== Error Details for: ${testName} ===`);
    console.log(`Status Code: ${response.status}`);
    console.log(`Response Body:`, JSON.stringify(response.body, null, 2));
    console.log(`=== End Error Details ===\n`);
  }

  /**
   * Validate that the response has the expected error structure
   * @param response - The supertest response object
   * @returns The validated error response
   */
  static validateErrorResponse(response: request.Response): ErrorResponse {
    expect(response.body).toHaveProperty('statusCode');
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('error');
    return response.body as ErrorResponse;
  }

  /**
   * Display error details and validate error response structure
   * @param testName - Name of the test case for context
   * @param response - The supertest response object
   * @returns The validated error response
   */
  static displayAndValidateError(
    testName: string,
    response: request.Response,
  ): ErrorResponse {
    return this.validateErrorResponse(response);
  }

  /**
   * Check if the error response contains validation errors
   * @param errorResponse - The error response object
   * @returns True if validation errors are present
   */
  static hasValidationErrors(errorResponse: ErrorResponse): boolean {
    return !!(
      errorResponse.meta?.validationErrors &&
      errorResponse.meta.validationErrors.length > 0
    );
  }

  /**
   * Find a specific validation error by property name
   * @param errorResponse - The error response object
   * @param propertyName - The property name to search for
   * @returns The validation error object if found
   */
  static findValidationError(
    errorResponse: ErrorResponse,
    propertyName: string,
  ):
    | {
        property: string;
        constraints: Record<string, string>;
        children?: any[];
      }
    | undefined {
    if (!this.hasValidationErrors(errorResponse)) {
      return undefined;
    }
    return errorResponse.meta!.validationErrors!.find(
      (error) => error.property === propertyName,
    );
  }

  /**
   * Assert that a validation error exists for a specific property
   * @param errorResponse - The error response object
   * @param propertyName - The property name that should have validation errors
   */
  static expectValidationErrorForProperty(
    errorResponse: ErrorResponse,
    propertyName: string,
  ): void {
    const validationError = this.findValidationError(
      errorResponse,
      propertyName,
    );
    expect(validationError).toBeDefined();
    expect(validationError?.constraints).toBeDefined();
  }

  /**
   * Assert common error response properties
   * @param errorResponse - The error response object
   * @param expectedStatusCode - Expected HTTP status code
   * @param expectedErrorType - Expected error type (e.g., 'Unauthorized', 'Not Found')
   */
  static expectErrorResponse(
    errorResponse: ErrorResponse,
    expectedStatusCode: number,
    expectedErrorType: string,
  ): void {
    expect(errorResponse.statusCode).toBe(expectedStatusCode);
    expect(errorResponse.error).toContain(expectedErrorType);
    expect(errorResponse.message).toBeDefined();
  }

  /**
   * Assert unauthorized error (401)
   * @param errorResponse - The error response object
   */
  static expectUnauthorizedError(errorResponse: ErrorResponse): void {
    this.expectErrorResponse(errorResponse, 401, 'Unauthorized');
  }

  /**
   * Assert not found error (404)
   * @param errorResponse - The error response object
   */
  static expectNotFoundError(errorResponse: ErrorResponse): void {
    this.expectErrorResponse(errorResponse, 404, 'Not Found');
  }

  /**
   * Assert bad request error (400)
   * @param errorResponse - The error response object
   */
  static expectBadRequestError(errorResponse: ErrorResponse): void {
    this.expectErrorResponse(errorResponse, 400, 'Bad Request');
  }

  /**
   * Assert validation error with specific properties
   * @param errorResponse - The error response object
   * @param expectedProperties - Array of property names that should have validation errors
   */
  static expectValidationErrors(
    errorResponse: ErrorResponse,
    expectedProperties: string[],
  ): void {
    this.expectBadRequestError(errorResponse);

    // Check if message is an array (validation errors) or contains validation text
    const messageIsArray = Array.isArray(errorResponse.message);
    const messageContainsValidation =
      typeof errorResponse.message === 'string' &&
      errorResponse.message.toLowerCase().includes('validation');

    expect(messageIsArray || messageContainsValidation).toBe(true);

    // If we have validation errors in meta, check for specific properties
    if (this.hasValidationErrors(errorResponse)) {
      expectedProperties.forEach((property) => {
        this.expectValidationErrorForProperty(errorResponse, property);
      });
    }
  }

  /**
   * Complete error handling for test cases - displays details and validates structure
   * @param testName - Name of the test case for context
   * @param response - The supertest response object
   * @param expectedStatusCode - Expected HTTP status code
   * @param expectedErrorType - Expected error type
   * @returns The validated error response
   */
  static handleTestError(
    testName: string,
    response: request.Response,
    expectedStatusCode: number,
    expectedErrorType: string,
  ): ErrorResponse {
    const errorResponse = this.displayAndValidateError(testName, response);
    this.expectErrorResponse(
      errorResponse,
      expectedStatusCode,
      expectedErrorType,
    );
    return errorResponse;
  }
}
