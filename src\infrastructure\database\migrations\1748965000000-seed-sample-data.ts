import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedSampleData1748965000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert sample features
    const features = [
      { nameEn: 'WiFi', nameAr: 'واي-فاي', icon: 'wifi' },
      { nameEn: 'Parking', nameAr: 'موقف سيارات', icon: 'parking' },
      { nameEn: 'Air Conditioning', nameAr: 'تكييف', icon: 'ac-unit' },
      { nameEn: 'Delivery', nameAr: 'توصيل', icon: 'delivery-dining' },
      { nameEn: 'Outdoor Seating', nameAr: 'جلسة خارجية', icon: 'deck' },
      {
        nameEn: 'Family Friendly',
        nameAr: 'مناسب للعائلات',
        icon: 'family-restroom',
      },
      {
        nameEn: 'Pet Friendly',
        nameAr: 'مناسب للحيوانات الأليفة',
        icon: 'pets',
      },
      { nameEn: '24/7 Service', nameAr: 'خدمة 24 ساعة', icon: 'schedule' },
      { nameEn: 'Credit Cards', nameAr: 'بطاقات ائتمان', icon: 'credit-card' },
      {
        nameEn: 'Wheelchair Access',
        nameAr: 'مناسب للكراسي المتحركة',
        icon: 'accessible',
      },
    ];

    for (const feature of features) {
      await queryRunner.query(
        `
        INSERT INTO "features" ("nameEn", "nameAr", "icon", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT ("nameEn") DO NOTHING
        `,
        [feature.nameEn, feature.nameAr, feature.icon],
      );
    }

    // Insert sample categories with hierarchy
    const categories = [
      // Main categories
      {
        nameEn: 'Restaurants',
        nameAr: 'مطاعم',
        descriptionEn: 'Dining and food establishments',
        descriptionAr: 'مؤسسات الطعام والشراب',
        icon: 'restaurant',
        slug: 'restaurants',
        level: 0,
        sortOrder: 1,
        parentId: null,
      },
      {
        nameEn: 'Shopping',
        nameAr: 'تسوق',
        descriptionEn: 'Retail stores and shopping centers',
        descriptionAr: 'متاجر البيع بالتجزئة ومراكز التسوق',
        icon: 'shopping-bag',
        slug: 'shopping',
        level: 0,
        sortOrder: 2,
        parentId: null,
      },
      {
        nameEn: 'Healthcare',
        nameAr: 'رعاية صحية',
        descriptionEn: 'Medical facilities and healthcare services',
        descriptionAr: 'المرافق الطبية وخدمات الرعاية الصحية',
        icon: 'local-hospital',
        slug: 'healthcare',
        level: 0,
        sortOrder: 3,
        parentId: null,
      },
      {
        nameEn: 'Entertainment',
        nameAr: 'ترفيه',
        descriptionEn: 'Entertainment venues and activities',
        descriptionAr: 'أماكن وأنشطة الترفيه',
        icon: 'movie',
        slug: 'entertainment',
        level: 0,
        sortOrder: 4,
        parentId: null,
      },
      {
        nameEn: 'Services',
        nameAr: 'خدمات',
        descriptionEn: 'Professional and personal services',
        descriptionAr: 'الخدمات المهنية والشخصية',
        icon: 'build',
        slug: 'services',
        level: 0,
        sortOrder: 5,
        parentId: null,
      },
    ];

    // Insert main categories first
    for (const category of categories) {
      await queryRunner.query(
        `
        INSERT INTO "categories" ("nameEn", "nameAr", "descriptionEn", "descriptionAr", "icon", "slug", "level", "sortOrder", "parentId", "numberOfBusinesses", "path", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 0, $6, NOW(), NOW())
        ON CONFLICT ("slug") DO NOTHING
        `,
        [
          category.nameEn,
          category.nameAr,
          category.descriptionEn,
          category.descriptionAr,
          category.icon,
          category.slug,
          category.level,
          category.sortOrder,
          category.parentId,
        ],
      );
    }

    // Get parent category IDs for subcategories
    const restaurantsResult = await queryRunner.query(
      `SELECT id FROM "categories" WHERE slug = 'restaurants' LIMIT 1`,
    );
    const shoppingResult = await queryRunner.query(
      `SELECT id FROM "categories" WHERE slug = 'shopping' LIMIT 1`,
    );
    const healthcareResult = await queryRunner.query(
      `SELECT id FROM "categories" WHERE slug = 'healthcare' LIMIT 1`,
    );

    const restaurantsId = restaurantsResult[0]?.id;
    const shoppingId = shoppingResult[0]?.id;
    const healthcareId = healthcareResult[0]?.id;

    // Insert subcategories
    const subcategories = [
      // Restaurant subcategories
      {
        nameEn: 'Fast Food',
        nameAr: 'وجبات سريعة',
        descriptionEn: 'Quick service restaurants',
        descriptionAr: 'مطاعم الخدمة السريعة',
        icon: 'fastfood',
        slug: 'fast-food',
        level: 1,
        sortOrder: 1,
        parentId: restaurantsId,
        path: 'restaurants/fast-food',
      },
      {
        nameEn: 'Fine Dining',
        nameAr: 'مطاعم راقية',
        descriptionEn: 'Upscale dining restaurants',
        descriptionAr: 'مطاعم فاخرة',
        icon: 'restaurant-menu',
        slug: 'fine-dining',
        level: 1,
        sortOrder: 2,
        parentId: restaurantsId,
        path: 'restaurants/fine-dining',
      },
      {
        nameEn: 'Cafes',
        nameAr: 'مقاهي',
        descriptionEn: 'Coffee shops and cafes',
        descriptionAr: 'محلات القهوة والمقاهي',
        icon: 'coffee',
        slug: 'cafes',
        level: 1,
        sortOrder: 3,
        parentId: restaurantsId,
        path: 'restaurants/cafes',
      },
      // Shopping subcategories
      {
        nameEn: 'Clothing',
        nameAr: 'ملابس',
        descriptionEn: 'Clothing and fashion stores',
        descriptionAr: 'متاجر الملابس والأزياء',
        icon: 'checkroom',
        slug: 'clothing',
        level: 1,
        sortOrder: 1,
        parentId: shoppingId,
        path: 'shopping/clothing',
      },
      {
        nameEn: 'Electronics',
        nameAr: 'إلكترونيات',
        descriptionEn: 'Electronic devices and gadgets',
        descriptionAr: 'الأجهزة الإلكترونية والأدوات',
        icon: 'computer',
        slug: 'electronics',
        level: 1,
        sortOrder: 2,
        parentId: shoppingId,
        path: 'shopping/electronics',
      },
      // Healthcare subcategories
      {
        nameEn: 'Hospitals',
        nameAr: 'مستشفيات',
        descriptionEn: 'General and specialized hospitals',
        descriptionAr: 'مستشفيات عامة ومتخصصة',
        icon: 'local-hospital',
        slug: 'hospitals',
        level: 1,
        sortOrder: 1,
        parentId: healthcareId,
        path: 'healthcare/hospitals',
      },
      {
        nameEn: 'Pharmacies',
        nameAr: 'صيدليات',
        descriptionEn: 'Pharmacies and drug stores',
        descriptionAr: 'الصيدليات ومتاجر الأدوية',
        icon: 'local-pharmacy',
        slug: 'pharmacies',
        level: 1,
        sortOrder: 2,
        parentId: healthcareId,
        path: 'healthcare/pharmacies',
      },
    ];

    for (const subcategory of subcategories.filter((sc) => sc.parentId)) {
      await queryRunner.query(
        `
        INSERT INTO "categories" ("nameEn", "nameAr", "descriptionEn", "descriptionAr", "icon", "slug", "level", "sortOrder", "parentId", "numberOfBusinesses", "path", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 0, $10, NOW(), NOW())
        ON CONFLICT ("slug") DO NOTHING
        `,
        [
          subcategory.nameEn,
          subcategory.nameAr,
          subcategory.descriptionEn,
          subcategory.descriptionAr,
          subcategory.icon,
          subcategory.slug,
          subcategory.level,
          subcategory.sortOrder,
          subcategory.parentId,
          subcategory.path,
        ],
      );
    }

    // Insert sample locations
    const locations = [
      {
        nameEn: 'Downtown Cairo',
        nameAr: 'وسط القاهرة',
        latitude: 30.0444,
        longitude: 31.2357,
        streetAddressEn: 'Tahrir Square',
        streetAddressAr: 'ميدان التحرير',
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        stateEn: 'Cairo Governorate',
        stateAr: 'محافظة القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
        postalCode: '11511',
        nearestLandmarkEn: 'Egyptian Museum',
        nearestLandmarkAr: 'المتحف المصري',
        timezone: 'Africa/Cairo',
      },
      {
        nameEn: 'Zamalek District',
        nameAr: 'حي الزمالك',
        latitude: 30.0626,
        longitude: 31.2197,
        streetAddressEn: '26th of July Street',
        streetAddressAr: 'شارع 26 يوليو',
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        stateEn: 'Cairo Governorate',
        stateAr: 'محافظة القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
        postalCode: '11211',
        nearestLandmarkEn: 'Cairo Opera House',
        nearestLandmarkAr: 'دار الأوبرا المصرية',
        timezone: 'Africa/Cairo',
      },
      {
        nameEn: 'New Cairo',
        nameAr: 'القاهرة الجديدة',
        latitude: 30.0275,
        longitude: 31.4913,
        streetAddressEn: 'First Settlement',
        streetAddressAr: 'التجمع الأول',
        cityEn: 'New Cairo',
        cityAr: 'القاهرة الجديدة',
        stateEn: 'Cairo Governorate',
        stateAr: 'محافظة القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
        postalCode: '11835',
        nearestLandmarkEn: 'American University in Cairo',
        nearestLandmarkAr: 'الجامعة الأمريكية بالقاهرة',
        timezone: 'Africa/Cairo',
      },
      {
        nameEn: 'Maadi',
        nameAr: 'المعادي',
        latitude: 29.9601,
        longitude: 31.2565,
        streetAddressEn: 'Road 9',
        streetAddressAr: 'شارع 9',
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        stateEn: 'Cairo Governorate',
        stateAr: 'محافظة القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
        postalCode: '11431',
        nearestLandmarkEn: 'Maadi Metro Station',
        nearestLandmarkAr: 'محطة مترو المعادي',
        timezone: 'Africa/Cairo',
      },
      {
        nameEn: 'Heliopolis',
        nameAr: 'مصر الجديدة',
        latitude: 30.0808,
        longitude: 31.3241,
        streetAddressEn: 'Salah Salem Street',
        streetAddressAr: 'شارع صلاح سالم',
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        stateEn: 'Cairo Governorate',
        stateAr: 'محافظة القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
        postalCode: '11341',
        nearestLandmarkEn: 'Cairo International Airport',
        nearestLandmarkAr: 'مطار القاهرة الدولي',
        timezone: 'Africa/Cairo',
      },
    ];

    for (const location of locations) {
      await queryRunner.query(
        `
        INSERT INTO "locations" ("nameEn", "nameAr", "latitude", "longitude", "streetAddressEn", "streetAddressAr", "cityEn", "cityAr", "stateEn", "stateAr", "countryEn", "countryAr", "postalCode", "nearestLandmarkEn", "nearestLandmarkAr", "timezone", "accuracy", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, 5, NOW(), NOW())
        `,
        [
          location.nameEn,
          location.nameAr,
          location.latitude,
          location.longitude,
          location.streetAddressEn,
          location.streetAddressAr,
          location.cityEn,
          location.cityAr,
          location.stateEn,
          location.stateAr,
          location.countryEn,
          location.countryAr,
          location.postalCode,
          location.nearestLandmarkEn,
          location.nearestLandmarkAr,
          location.timezone,
        ],
      );
    }

    // Get IDs for business creation
    const cafesCategoryResult = await queryRunner.query(
      `SELECT id FROM "categories" WHERE slug = 'cafes' LIMIT 1`,
    );
    const fastFoodCategoryResult = await queryRunner.query(
      `SELECT id FROM "categories" WHERE slug = 'fast-food' LIMIT 1`,
    );
    const clothingCategoryResult = await queryRunner.query(
      `SELECT id FROM "categories" WHERE slug = 'clothing' LIMIT 1`,
    );

    const downtownLocationResult = await queryRunner.query(
      `SELECT id FROM "locations" WHERE "nameEn" = 'Downtown Cairo' LIMIT 1`,
    );
    const zamalekLocationResult = await queryRunner.query(
      `SELECT id FROM "locations" WHERE "nameEn" = 'Zamalek District' LIMIT 1`,
    );
    const newCairoLocationResult = await queryRunner.query(
      `SELECT id FROM "locations" WHERE "nameEn" = 'New Cairo' LIMIT 1`,
    );

    // Get test user ID for business owner
    const ownerUserResult = await queryRunner.query(
      `SELECT id FROM "users" WHERE email = '<EMAIL>' LIMIT 1`,
    );

    const cafesCategoryId = cafesCategoryResult[0]?.id;
    const fastFoodCategoryId = fastFoodCategoryResult[0]?.id;
    const clothingCategoryId = clothingCategoryResult[0]?.id;
    const downtownLocationId = downtownLocationResult[0]?.id;
    const zamalekLocationId = zamalekLocationResult[0]?.id;
    const newCairoLocationId = newCairoLocationResult[0]?.id;
    const ownerUserId = ownerUserResult[0]?.id;

    if (cafesCategoryId && downtownLocationId && ownerUserId) {
      // Insert sample businesses
      const businesses = [
        {
          nameEn: 'Cairo Coffee House',
          nameAr: 'مقهى القاهرة',
          phoneNumber: '+201234567890',
          whatsAppNumber: '+201234567890',
          priceRange: '₤₤',
          locationId: downtownLocationId,
          primaryCategoryId: cafesCategoryId,
          operatingHours: JSON.stringify({
            monday: { open: '07:00', close: '23:00' },
            tuesday: { open: '07:00', close: '23:00' },
            wednesday: { open: '07:00', close: '23:00' },
            thursday: { open: '07:00', close: '23:00' },
            friday: { open: '09:00', close: '01:00' },
            saturday: { open: '09:00', close: '01:00' },
            sunday: { open: '09:00', close: '23:00' },
          }),
          shortDescriptionEn:
            'Traditional Egyptian coffee house with authentic atmosphere',
          shortDescriptionAr: 'مقهى مصري تقليدي بأجواء أصيلة',
          fullDescriptionEn:
            'Experience the authentic taste of Egyptian coffee in our traditional coffee house located in the heart of downtown Cairo.',
          fullDescriptionAr:
            'استمتع بالطعم الأصيل للقهوة المصرية في مقهانا التقليدي الواقع في قلب وسط القاهرة.',
          isActive: true,
          isVerified: true,
          averageRating: 4.2,
          totalReviewsCount: 85,
          ownerUserId: ownerUserId,
          paymentMethods: JSON.stringify(['cash', 'card']),
        },
        {
          nameEn: 'Zamalek Bistro',
          nameAr: 'بيسترو الزمالك',
          phoneNumber: '+201987654321',
          whatsAppNumber: '+201987654321',
          priceRange: '₤₤₤',
          locationId: zamalekLocationId,
          primaryCategoryId: cafesCategoryId,
          operatingHours: JSON.stringify({
            monday: { open: '08:00', close: '22:00' },
            tuesday: { open: '08:00', close: '22:00' },
            wednesday: { open: '08:00', close: '22:00' },
            thursday: { open: '08:00', close: '22:00' },
            friday: { open: '08:00', close: '24:00' },
            saturday: { open: '08:00', close: '24:00' },
            sunday: { open: '10:00', close: '22:00' },
          }),
          shortDescriptionEn:
            'Modern bistro with international cuisine in trendy Zamalek',
          shortDescriptionAr: 'بيسترو عصري بأطباق عالمية في الزمالك العصري',
          fullDescriptionEn:
            'A contemporary dining experience featuring international cuisine with a modern twist, located in the vibrant Zamalek district.',
          fullDescriptionAr:
            'تجربة طعام معاصرة تتميز بالمأكولات العالمية بلمسة عصرية، تقع في حي الزمالك النابض بالحياة.',
          isActive: true,
          isVerified: true,
          isPremium: true,
          averageRating: 4.5,
          totalReviewsCount: 120,
          ownerUserId: ownerUserId,
          paymentMethods: JSON.stringify(['cash', 'card', 'mobile_wallet']),
        },
      ];

      if (fastFoodCategoryId && newCairoLocationId) {
        businesses.push({
          nameEn: 'Quick Bite',
          nameAr: 'لقمة سريعة',
          phoneNumber: '+201555666777',
          whatsAppNumber: '+201555666777',
          priceRange: '₤',
          locationId: newCairoLocationId,
          primaryCategoryId: fastFoodCategoryId,
          operatingHours: JSON.stringify({
            monday: { open: '10:00', close: '02:00' },
            tuesday: { open: '10:00', close: '02:00' },
            wednesday: { open: '10:00', close: '02:00' },
            thursday: { open: '10:00', close: '02:00' },
            friday: { open: '10:00', close: '03:00' },
            saturday: { open: '10:00', close: '03:00' },
            sunday: { open: '10:00', close: '02:00' },
          }),
          shortDescriptionEn:
            'Fast food restaurant with local and international options',
          shortDescriptionAr: 'مطعم وجبات سريعة بخيارات محلية وعالمية',
          fullDescriptionEn:
            'Serving delicious fast food with both local Egyptian flavors and international favorites in New Cairo.',
          fullDescriptionAr:
            'نقدم وجبات سريعة لذيذة بنكهات مصرية محلية ومفضلات عالمية في القاهرة الجديدة.',
          isActive: true,
          isVerified: false,
          averageRating: 3.8,
          totalReviewsCount: 45,
          ownerUserId: ownerUserId,
          paymentMethods: JSON.stringify(['cash', 'card']),
        });
      }

      for (const business of businesses) {
        await queryRunner.query(
          `
          INSERT INTO "businesses" ("nameEn", "nameAr", "phoneNumber", "whatsAppNumber", "priceRange", "locationId", "primaryCategoryId", "operatingHours", "shortDescriptionEn", "shortDescriptionAr", "fullDescriptionEn", "fullDescriptionAr", "isActive", "isVerified", "isPremium", "averageRating", "totalReviewsCount", "ownerUserId", "paymentMethods", "totalViewsCount", "lastMonthViews", "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, 0, 0, NOW(), NOW())
          `,
          [
            business.nameEn,
            business.nameAr,
            business.phoneNumber,
            business.whatsAppNumber,
            business.priceRange,
            business.locationId,
            business.primaryCategoryId,
            business.operatingHours,
            business.shortDescriptionEn,
            business.shortDescriptionAr,
            business.fullDescriptionEn,
            business.fullDescriptionAr,
            business.isActive,
            business.isVerified,
            business.isPremium || false,
            business.averageRating,
            business.totalReviewsCount,
            business.ownerUserId,
            business.paymentMethods,
          ],
        );
      }

      // Create business-feature relationships
      const wifiFeatureResult = await queryRunner.query(
        `SELECT id FROM "features" WHERE "nameEn" = 'WiFi' LIMIT 1`,
      );
      const parkingFeatureResult = await queryRunner.query(
        `SELECT id FROM "features" WHERE "nameEn" = 'Parking' LIMIT 1`,
      );
      const acFeatureResult = await queryRunner.query(
        `SELECT id FROM "features" WHERE "nameEn" = 'Air Conditioning' LIMIT 1`,
      );

      const cairoBusinessResult = await queryRunner.query(
        `SELECT id FROM "businesses" WHERE "nameEn" = 'Cairo Coffee House' LIMIT 1`,
      );
      const zamalekBusinessResult = await queryRunner.query(
        `SELECT id FROM "businesses" WHERE "nameEn" = 'Zamalek Bistro' LIMIT 1`,
      );

      const wifiFeatureId = wifiFeatureResult[0]?.id;
      const parkingFeatureId = parkingFeatureResult[0]?.id;
      const acFeatureId = acFeatureResult[0]?.id;
      const cairoBusinessId = cairoBusinessResult[0]?.id;
      const zamalekBusinessId = zamalekBusinessResult[0]?.id;

      if (wifiFeatureId && cairoBusinessId) {
        // Add features to businesses
        const businessFeatures = [
          { businessId: cairoBusinessId, featureId: wifiFeatureId },
          { businessId: cairoBusinessId, featureId: acFeatureId },
          { businessId: zamalekBusinessId, featureId: wifiFeatureId },
          { businessId: zamalekBusinessId, featureId: parkingFeatureId },
          { businessId: zamalekBusinessId, featureId: acFeatureId },
        ].filter((bf) => bf.featureId);

        for (const bf of businessFeatures) {
          await queryRunner.query(
            `
            INSERT INTO "business_features" ("businessId", "featureId")
            VALUES ($1, $2)
            ON CONFLICT DO NOTHING
            `,
            [bf.businessId, bf.featureId],
          );
        }
      }
    }

    console.log('Sample data seeded successfully!');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove business-feature relationships
    await queryRunner.query(`DELETE FROM "business_features"`);

    // Remove businesses
    await queryRunner.query(`
      DELETE FROM "businesses" WHERE "nameEn" IN (
        'Cairo Coffee House',
        'Zamalek Bistro',
        'Quick Bite'
      )
    `);

    // Remove locations
    await queryRunner.query(`
      DELETE FROM "locations" WHERE "nameEn" IN (
        'Downtown Cairo',
        'Zamalek District',
        'New Cairo',
        'Maadi',
        'Heliopolis'
      )
    `);

    // Remove categories (children first, then parents)
    await queryRunner.query(`
      DELETE FROM "categories" WHERE "slug" IN (
        'fast-food',
        'fine-dining',
        'cafes',
        'clothing',
        'electronics',
        'hospitals',
        'pharmacies'
      )
    `);

    await queryRunner.query(`
      DELETE FROM "categories" WHERE "slug" IN (
        'restaurants',
        'shopping',
        'healthcare',
        'entertainment',
        'services'
      )
    `);

    // Remove features
    await queryRunner.query(`
      DELETE FROM "features" WHERE "nameEn" IN (
        'WiFi',
        'Parking',
        'Air Conditioning',
        'Delivery',
        'Outdoor Seating',
        'Family Friendly',
        'Pet Friendly',
        '24/7 Service',
        'Credit Cards',
        'Wheelchair Access'
      )
    `);

    console.log('Sample data removed successfully!');
  }
}
