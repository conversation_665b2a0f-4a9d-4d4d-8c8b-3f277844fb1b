import { IGenericService } from '@app/common';
import { Feature } from '../../entities/feature.entity';
import { FeatureSchema } from '../../schemes/feature.schema';
import { CreateFeatureDto } from '../../dtos/create-feature.dto';
import { UpdateFeatureDto } from '../../dtos/update-feature.dto';
import { GetAllFeatureDto } from '../../dtos/get-all-feature.dto';

export interface IFeatureService
  extends IGenericService<
    Feature,
    typeof FeatureSchema,
    GetAllFeatureDto,
    CreateFeatureDto,
    UpdateFeatureDto
  > {}

export const IFeatureService = Symbol('IFeatureService');
