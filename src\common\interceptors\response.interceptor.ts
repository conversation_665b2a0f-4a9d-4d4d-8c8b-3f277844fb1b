import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { performance } from 'perf_hooks';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

export interface ApiResponse<T> {
  statusIndicator: 'SUCCESS' | 'FAILED';
  statusCode: number;
  message?: string;
  data: T;
  metadata: {
    timestamp: string;
    correlationId: string;
    path: string;
    duration: number;
  };
}

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ApiResponse<T>>
{
  private readonly logger = new Logger(ResponseInterceptor.name);

  intercept(
    context: ExecutionContext,
    next: CallHandler<T>,
  ): Observable<ApiResponse<T>> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = performance.now();

    // Get or generate correlation ID
    const correlationId = request.headers['x-correlation-id'] || uuidv4();
    response.setHeader('x-correlation-id', correlationId);

    return next.handle().pipe(
      map((data) => {
        const duration = performance.now() - startTime;

        const responseBody: ApiResponse<T> = {
          statusIndicator: 'SUCCESS',
          statusCode: response.statusCode,
          message:
            response.message ||
            response.statusMessage ||
            'Request processed successfully',
          data,
          metadata: {
            timestamp: new Date().toISOString(),
            correlationId,
            path: request.url,
            duration: Math.round(duration),
          },
        };

        return responseBody;
      }),
    );
  }

  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    const sanitized = { ...headers };
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'set-cookie',
      'x-auth-token',
      'api-key',
      'password',
    ];

    sensitiveHeaders.forEach((header) => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
