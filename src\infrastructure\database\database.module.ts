import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigurationValidationDto } from '../../common';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService<ConfigurationValidationDto, true>],
      useFactory: (
        configService: ConfigService<ConfigurationValidationDto, true>,
      ): TypeOrmModuleOptions => ({
        type: 'postgres',
        url: configService.get('DATABASE_URL'),
        logger: 'simple-console',
        logging: process.env.NODE_ENV === 'test' ? false : 'all',
        autoLoadEntities: true,
        synchronize: false,
      }),
    }),
  ],
})
export class DatabaseModule {}
