<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/forms.css">
    
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background: var(--primary-gradient);
        }
        
        .login-form {
            background: white;
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .login-header {
            margin-bottom: 30px;
        }
        
        .login-logo {
            font-size: 4rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            color: #666;
            font-size: 1rem;
        }
        
        .form-group {
            text-align: right;
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: var(--transition);
            background: #fafbfc;
            font-family: 'Cairo', sans-serif;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .login-btn {
            width: 100%;
            padding: 14px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 20px;
        }
        
        .login-btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .back-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .back-link:hover {
            color: var(--secondary-color);
        }
        
        .response-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
            font-weight: 500;
        }
        
        .response-message.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 2px solid var(--success-color);
        }
        
        .response-message.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 2px solid var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="login-header">
                <div class="login-logo">🏪</div>
                <h1 class="login-title">قريب بلس</h1>
                <p class="login-subtitle">تسجيل الدخول إلى لوحة التحكم</p>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginId">البريد الإلكتروني أو اسم المستخدم</label>
                    <input type="text" id="loginId" name="loginId" required dir="ltr" placeholder="أدخل البريد الإلكتروني أو اسم المستخدم">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required dir="ltr">
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <span id="loginBtnText">تسجيل الدخول</span>
                    <div class="loading-spinner" id="loginSpinner" style="display: none;"></div>
                </button>
                
                <div class="response-message" id="responseMessage"></div>
            </form>
            
            <div style="margin-top: 30px;">
                <a href="../../index.html" class="back-link">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Redirect if already logged in
            if (AuthUtils.isAuthenticated()) {
                window.location.href = '../../index.html';
                return;
            }
            
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginSpinner = document.getElementById('loginSpinner');
            const responseMessage = document.getElementById('responseMessage');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const loginId = document.getElementById('loginId').value;
                const password = document.getElementById('password').value;

                if (!loginId || !password) {
                    showMessage('يرجى ملء جميع الحقول', 'error');
                    return;
                }
                
                // Show loading state
                setLoading(true);
                
                try {
                    const response = await fetch('/v1/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ loginId, password })
                    });
                    
                    const result = await response.json();

                    // Check for success using the new API response structure
                    const isSuccess = (response.ok || response.status === 201) &&
                                     (result.statusIndicator === "SUCCESS" || result.success);

                    if (isSuccess && result.data?.accessToken) {
                        // Store tokens with expiry information
                        AuthUtils.setTokens(
                            result.data.accessToken,
                            result.data.refreshToken,
                            result.data.expiresIn || 3600,
                            true // remember me
                        );

                        showMessage('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');

                        // Redirect after short delay
                        setTimeout(() => {
                            window.location.href = '../../index.html';
                        }, 1500);
                    } else {
                        showMessage(result.message || 'فشل في تسجيل الدخول', 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
                } finally {
                    setLoading(false);
                }
            });
            
            function setLoading(loading) {
                loginBtn.disabled = loading;
                loginBtnText.style.display = loading ? 'none' : 'inline';
                loginSpinner.style.display = loading ? 'inline-block' : 'none';
            }
            
            function showMessage(message, type) {
                responseMessage.textContent = message;
                responseMessage.className = `response-message ${type}`;
                responseMessage.style.display = 'block';
                
                // Auto-hide success messages
                if (type === 'success') {
                    setTimeout(() => {
                        responseMessage.style.display = 'none';
                    }, 3000);
                }
            }
        });
    </script>
</body>
</html>
