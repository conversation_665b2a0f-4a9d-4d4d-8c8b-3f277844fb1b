<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>قريب بلس - لوحة التحكم</title>

    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/components.css" rel="stylesheet">

    <style>
        /* Dashboard specific styles */
        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .auth-status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .auth-status.authenticated {
            border: 2px solid var(--success-color);
        }

        .auth-status.not-authenticated {
            border: 2px solid var(--danger-color);
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 40px;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: 1fr;
            }

            .card-actions {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="dashboard-container">
        <div class="page-header">
            <h1 class="page-title">قريب بلس</h1>
            <p class="page-subtitle">نظام إدارة الأعمال التجارية والمواقع</p>

            <div class="auth-status" id="authStatus">
                <div id="authMessage">جاري التحقق من حالة تسجيل الدخول...</div>
            </div>
        </div>

        <div class="stats-section" id="statsSection" style="display: none;">
            <h3 style="text-align: center; margin-bottom: 20px; color: #2c3e50;">إحصائيات سريعة</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="categoriesCount">-</div>
                    <div class="stat-label">الفئات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="featuresCount">-</div>
                    <div class="stat-label">الميزات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="locationsCount">-</div>
                    <div class="stat-label">المواقع</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="businessesCount">-</div>
                    <div class="stat-label">الأعمال التجارية</div>
                </div>
            </div>
        </div>

        <div class="cards-grid">
            <div class="card">
                <span class="card-icon">🏪</span>
                <h3>إدارة الأعمال التجارية</h3>
                <p>أضف وأدر الأعمال التجارية مع جميع التفاصيل والمعلومات المطلوبة</p>
                <div class="card-actions">
                    <a class="btn btn-primary" href="pages/business/form.html">إنشاء عمل تجاري</a>
                    <a class="btn btn-secondary" href="pages/business/list.html">عرض جميع الأعمال</a>
                </div>
            </div>

            <div class="card">
                <span class="card-icon">📂</span>
                <h3>إدارة الفئات</h3>
                <p>أنشئ وأدر فئات الأعمال التجارية لتنظيم أفضل</p>
                <div class="card-actions">
                    <a class="btn btn-primary" href="pages/categories/form.html">إنشاء فئة جديدة</a>
                    <a class="btn btn-secondary" href="pages/categories/list.html">عرض جميع الفئات</a>
                </div>
            </div>

            <div class="card">
                <span class="card-icon">⭐</span>
                <h3>إدارة الميزات</h3>
                <p>أضف ميزات جديدة يمكن للأعمال التجارية استخدامها</p>
                <div class="card-actions">
                    <a class="btn btn-primary" href="pages/features/form.html">إنشاء ميزة جديدة</a>
                    <a class="btn btn-secondary" href="pages/features/list.html">عرض جميع الميزات</a>
                </div>
            </div>

            <div class="card">
                <span class="card-icon">📍</span>
                <h3>إدارة المواقع</h3>
                <p>أضف وأدر المواقع الجغرافية للأعمال التجارية</p>
                <div class="card-actions">
                    <a class="btn btn-primary" href="pages/locations/form.html">إنشاء موقع جديد</a>
                    <a class="btn btn-secondary" href="pages/locations/list.html">عرض جميع المواقع</a>
                </div>
            </div>

            <div class="card">
                <span class="card-icon">🔐</span>
                <h3>إدارة الحساب</h3>
                <p>تسجيل الدخول أو إدارة إعدادات حسابك</p>
                <div class="card-actions">
                    <a class="btn btn-secondary" href="pages/auth/login.html" id="authAction">تسجيل الدخول</a>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 قريب بلس. جميع الحقوق محفوظة.</p>
            <p>نظام إدارة شامل للأعمال التجارية والمواقع</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/auth-utils.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            loadStats();
        });

        function checkAuthStatus() {
            const authStatus = document.getElementById('authStatus');
            const authMessage = document.getElementById('authMessage');
            const authAction = document.getElementById('authAction');
            const statsSection = document.getElementById('statsSection');

            // Navigation elements
            const navUserInfo = document.getElementById('navUserInfo');
            const navAuthAction = document.getElementById('navAuthAction');

            if (AuthUtils.isAuthenticated()) {
                const userData = AuthUtils.getUserData();

                // Update main auth section
                authStatus.className = 'auth-status authenticated';
                authMessage.innerHTML = `
                    <strong>✅ مسجل الدخول</strong><br>
                    ${userData?.email || userData?.sub || 'مستخدم'}
                    (${userData?.role?.name || 'User'})
                `;
                authAction.textContent = 'تسجيل الخروج';
                authAction.href = '#';
                authAction.onclick = function(e) {
                    e.preventDefault();
                    AuthUtils.clearTokens();
                    window.location.reload();
                };

                // Update navigation
                navUserInfo.textContent = `${userData?.email || userData?.sub || 'مستخدم'}`;
                navUserInfo.style.display = 'block';
                navAuthAction.textContent = 'تسجيل الخروج';
                navAuthAction.href = '#';
                navAuthAction.onclick = function(e) {
                    e.preventDefault();
                    AuthUtils.clearTokens();
                    window.location.reload();
                };
            } else {
                // Update main auth section
                authStatus.className = 'auth-status not-authenticated';
                authMessage.innerHTML = '<strong>❌ غير مسجل الدخول</strong><br>يرجى تسجيل الدخول للوصول إلى جميع الميزات';
                authAction.textContent = 'تسجيل الدخول';
                authAction.href = 'login.html';
                authAction.onclick = null;

                // Update navigation
                navUserInfo.style.display = 'none';
                navAuthAction.textContent = 'تسجيل الدخول';
                navAuthAction.href = 'login.html';
                navAuthAction.onclick = null;
            }

            // Always show stats since dashboard endpoint is public
            statsSection.style.display = 'block';
        }

        async function loadStats() {
            try {
                // Load all statistics from the new dashboard API
                const response = await fetch('/v1/dashboard/statistics', {
                    headers: AuthUtils.getAuthHeaders()
                });
                if (response.ok) {
                    const result = await response.json();

                    // Handle wrapped response format
                    const stats = result.data || result;

                    // Update all counters
                    document.getElementById('categoriesCount').textContent = stats.categoriesCount || 0;
                    document.getElementById('featuresCount').textContent = stats.featuresCount || 0;
                    document.getElementById('locationsCount').textContent = stats.locationsCount || 0;
                    document.getElementById('businessesCount').textContent = stats.businessesCount || 0;
                } else {
                    console.error('Failed to load dashboard statistics:', response.status);
                    // Set default values on error
                    document.getElementById('categoriesCount').textContent = '-';
                    document.getElementById('featuresCount').textContent = '-';
                    document.getElementById('locationsCount').textContent = '-';
                    document.getElementById('businessesCount').textContent = '-';
                }
            } catch (error) {
                console.error('Error loading dashboard statistics:', error);
                // Set default values on error
                document.getElementById('categoriesCount').textContent = '-';
                document.getElementById('featuresCount').textContent = '-';
                document.getElementById('locationsCount').textContent = '-';
                document.getElementById('businessesCount').textContent = '-';
            }
        }
    </script>
</body>
</html>
