import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { User } from '../entities/user.entity';
import { IPermissionService } from '../interfaces/permission/permission-service.interface';
import { IS_PUBLIC_KEY } from '@app/common';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject(IPermissionService) private permissionService: IPermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const className = context.getClass().name;
    const methodName = context.getHandler().name;

    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest<{ user: User }>();

    if (!user) {
      return false;
    }

    if (!user.role || !user.role.permissions) {
      return false;
    }
    const permission = await this.permissionService.findOrCreate({
      action: `${className}.${methodName}`,
      manuallyAdded: false,
    });

    return user.role?.permissions?.some((p) => p.id == permission.id);
  }
}
