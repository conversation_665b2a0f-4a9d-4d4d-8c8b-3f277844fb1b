import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ValidationError } from 'class-validator';
import { Request, Response } from 'express';
import { ConfigurationValidationDto } from '@app/common';

// Define error response envelope shape
export interface ErrorResponseEnvelope {
  statusCode: number;
  message: string;
  error: string;
  meta?: Record<string, any>;
  stack?: string;
}

// Define custom domain error to HTTP status code mapping
export const DomainErrorsMap = {
  // Example mappings - extend as needed
  OrderAlreadyPaidError: HttpStatus.CONFLICT,
  ResourceNotFoundError: HttpStatus.NOT_FOUND,
  UnauthorizedActionError: HttpStatus.FORBIDDEN,
  ValidationFailedError: HttpStatus.BAD_REQUEST,
  // Add more domain-specific errors here
};

@Injectable()
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(
    private readonly configService: ConfigService<
      ConfigurationValidationDto,
      true
    >,
  ) {}

  catch(exception: any, host: ArgumentsHost): void {
    // Handle different contexts (HTTP, RPC, WebSocket)
    if (host.getType() === 'http') {
      this.handleHttpException(exception, host);
    } else if (host.getType() === 'rpc') {
      this.handleRpcException(exception, host);
    } else if (host.getType() === 'ws') {
      this.handleWsException(exception, host);
    }
  }

  private handleHttpException(exception: any, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Create the error envelope
    const envelope = this.createErrorEnvelope(exception, request);

    // Log the error with structured data
    this.logError(envelope, request, exception);

    // Check for critical error that needs alerting
    this.checkForCriticalError(envelope, request);

    // Increment metrics counter (placeholder - implement with your metrics library)
    this.incrementErrorMetrics(envelope.statusCode, request.path);

    // Send the response
    response.status(envelope.statusCode).json(envelope);
  }

  private handleRpcException(exception: any, host: ArgumentsHost): void {
    const ctx = host.switchToRpc();
    const data = ctx.getData();

    // Create error envelope for RPC
    const envelope = this.createErrorEnvelope(exception);

    // Log the error
    this.logger.error(`RPC Exception: ${exception.message}`, {
      data,
      envelope,
      stack: exception.stack,
    });

    // Implementation depends on your RPC framework
    // For example with Microservices pattern:
    // return { error: envelope };
  }

  private handleWsException(exception: any, host: ArgumentsHost): void {
    const ctx = host.switchToWs();
    const client = ctx.getClient();
    const data = ctx.getData();

    // Create error envelope for WebSocket
    const envelope = this.createErrorEnvelope(exception);

    // Log the error
    this.logger.error(`WebSocket Exception: ${exception.message}`, {
      data,
      envelope,
      stack: exception.stack,
    });

    // Implementation depends on your WebSocket framework
    // For example:
    // client.emit('error', envelope);
  }

  private createErrorEnvelope(
    exception: any,
    request?: Request,
  ): ErrorResponseEnvelope {
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'Internal Server Error';
    const meta: Record<string, any> = {};

    // Add request context to meta if available
    if (request) {
      meta.requestId = request.headers['x-request-id'] || 'unknown';
      meta.path = request.path;
      meta.method = request.method;
      meta.ip = request.ip;
      meta.userAgent = request.headers['user-agent'];

      // Add user info if available
      if (request['user']) {
        meta.userId = (request['user'] as any).id;
      }
    }

    // Handle HttpExceptions
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || exception.message;
        error = (exceptionResponse as any).error || exception.name;
      } else {
        message = exceptionResponse;
        error = exception.name;
      }
    }
    // Handle validation errors
    else if (
      exception?.name === 'ValidationError' ||
      exception instanceof ValidationError ||
      (Array.isArray(exception) && exception[0] instanceof ValidationError)
    ) {
      statusCode = HttpStatus.BAD_REQUEST;
      error = 'Validation Error';

      if (Array.isArray(exception)) {
        // Format class-validator errors
        const validationErrors = this.formatValidationErrors(exception);
        message = 'Validation failed';
        meta.validationErrors = validationErrors;
      } else {
        message = exception.message;
      }
    }
    // Map domain exceptions to HTTP status codes
    else {
      const errorName = exception?.name || exception?.constructor?.name;
      if (errorName && DomainErrorsMap[errorName]) {
        statusCode = DomainErrorsMap[errorName];
        message = exception.message || 'Domain error occurred';
        error = errorName;
      } else {
        // Unknown errors
        message = exception?.message || 'An unexpected error occurred';
        error = exception?.name || 'UnknownError';
      }
    }

    // Create response envelope
    const envelope: ErrorResponseEnvelope = {
      statusCode,
      message,
      error,
      meta,
    };

    // Add stack trace in non-production environments
    const isProduction =
      this.configService.get<string>('NODE_ENV') === 'production';
    if (!isProduction && exception?.stack) {
      envelope.stack = exception.stack;
    }

    return envelope;
  }

  private formatValidationErrors(
    validationErrors: ValidationError[],
  ): Record<string, any>[] {
    return validationErrors.map((error) => {
      const result: Record<string, any> = {
        property: error.property,
        constraints: error.constraints,
      };

      if (error.children?.length) {
        result.children = this.formatValidationErrors(error.children);
      }

      return result;
    });
  }

  private logError(
    envelope: ErrorResponseEnvelope,
    request?: Request,
    exception?: any,
  ): void {
    const logContext = {
      ...envelope,
      request: request
        ? {
            path: request.path,
            method: request.method,
            ip: request.ip,
            userAgent: request.headers['user-agent'],
          }
        : undefined,
    };

    // Log different levels based on status code
    if (envelope.statusCode >= 500) {
      this.logger.error(`[${envelope.statusCode}] ${envelope.message}`, {
        ...logContext,
        stack: exception?.stack,
      });
    } else if (envelope.statusCode >= 400) {
      this.logger.warn(
        `[${envelope.statusCode}] ${envelope.message}`,
        logContext,
      );
    } else {
      this.logger.debug(
        `[${envelope.statusCode}] ${envelope.message}`,
        logContext,
      );
    }
  }

  private checkForCriticalError(
    envelope: ErrorResponseEnvelope,
    request?: Request,
  ): void {
    // Example of dead-letter queue / alert logic for critical errors
    if (envelope.statusCode >= 500) {
      // Check for critical path
      const criticalPaths = ['/payments', '/orders', '/auth'];
      const isOnCriticalPath =
        request && criticalPaths.some((path) => request.path.startsWith(path));

      if (isOnCriticalPath) {
        // Here you would implement the alert logic:
        // - Send to SQS/SNS
        // - Trigger PagerDuty
        // - Send alert email
        // Example:
        this.logger.error(`CRITICAL ERROR on ${request?.path}`, {
          alertType: 'critical',
          ...envelope,
        });

        // Placeholder for actual alert implementation
        // this.alertService.sendCriticalAlert({
        //   title: `Critical error on ${request?.path}`,
        //   details: envelope,
        // });
      }
    }
  }

  private incrementErrorMetrics(statusCode: number, path: string): void {
    // Placeholder for metrics implementation
    // Example with Prometheus:
    // this.prometheusService.increment('http_exceptions_total', {
    //   statusCode: statusCode.toString(),
    //   route: path,
    // });
  }
}
