import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IGetAllResponseInterface } from '../../../common/interfaces';
import { CreateUserDto } from '../dtos/user/create-user.dto';
import { GetAllUserDto } from '../dtos/user/get-all-user.dto';
import { UpdateUserDto } from '../dtos/user/update-user.dto';
import { User } from '../entities/user.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { IUserService } from '../interfaces/user/user-service.interface';
import { CurrentUser } from '../decorators/current-user.decorator';

@Controller('users')
@ApiTags('Users')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class UserController {
  constructor(
    @Inject(IUserService) private readonly userService: IUserService,
  ) {}

  @Get('profile')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiOkResponse({ description: 'User profile', type: User })
  async getProfile(@CurrentUser() user: any): Promise<User | null> {
    return this.userService.findOneWithDetailsById(user.id, true);
  }

  /**
   * Get all users with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all users with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The users have been successfully retrieved.',
    type: User,
    isArray: true,
  })
  async getAll(
    @Query() getAllDto: GetAllUserDto,
  ): Promise<IGetAllResponseInterface<User>> {
    return this.userService.getAll(getAllDto);
  }

  /**
   * Get user by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiParam({ name: 'id', description: 'User ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The user has been successfully retrieved.',
    type: User,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  async getById(@Param('id', ParseIntPipe) id: number): Promise<User> {
    return this.userService.findOneById(id);
  }

  /**
   * Create new user
   */
  @Post()
  @ApiOperation({ summary: 'Create new user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The user has been successfully created.',
    type: User,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDto: CreateUserDto): Promise<User> {
    return this.userService.create(User, createDto);
  }

  /**
   * Update an existing user
   */
  @Put(':id')
  @ApiOperation({ summary: 'Update an existing user' })
  @ApiParam({ name: 'id', description: 'User ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The user has been successfully updated.',
    type: User,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateUserDto,
  ): Promise<User> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.userService.update(User, updateDtoWithId);
  }

  /**
   * Delete a user
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user' })
  @ApiParam({ name: 'id', description: 'User ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The user has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.userService.deleteOne(id);
  }
}
