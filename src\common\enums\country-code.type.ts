import { countries } from '../constants/country-codes.constant';

export enum CountryDialCode {
  '+93' = '+93',
  '+358' = '+358',
  '+355' = '+355',
  '+213' = '+213',
  '+1684' = '+1684',
  '+376' = '+376',
  '+244' = '+244',
  '+1264' = '+1264',
  '+1268' = '+1268',
  '+54' = '+54',
  '+374' = '+374',
  '+297' = '+297',
  '+247' = '+247',
  '+61' = '+61',
  '+43' = '+43',
  '+994' = '+994',
  '+1242' = '+1242',
  '+973' = '+973',
  '+880' = '+880',
  '+1246' = '+1246',
  '+375' = '+375',
  '+32' = '+32',
  '+501' = '+501',
  '+229' = '+229',
  '+1441' = '+1441',
  '+975' = '+975',
  '+591' = '+591',
  '+387' = '+387',
  '+267' = '+267',
  '+55' = '+55',
  '+246' = '+246',
  '+673' = '+673',
  '+359' = '+359',
  '+226' = '+226',
  '+257' = '+257',
  '+855' = '+855',
  '+237' = '+237',
  '+1' = '+1',
  '+238' = '+238',
  '+1345' = '+1345',
  '+236' = '+236',
  '+235' = '+235',
  '+56' = '+56',
  '+86' = '+86',
  '+57' = '+57',
  '+269' = '+269',
  '+242' = '+242',
  '+682' = '+682',
  '+506' = '+506',
  '+385' = '+385',
  '+53' = '+53',
  '+357' = '+357',
  '+420' = '+420',
  '+243' = '+243',
  '+45' = '+45',
  '+253' = '+253',
  '+1767' = '+1767',
  '+1849' = '+1849',
  '+593' = '+593',
  '+20' = '+20',
  '+503' = '+503',
  '+240' = '+240',
  '+291' = '+291',
  '+372' = '+372',
  '+268' = '+268',
  '+251' = '+251',
  '+500' = '+500',
  '+298' = '+298',
  '+679' = '+679',

  '+33' = '+33',
  '+594' = '+594',
  '+689' = '+689',
  '+241' = '+241',
  '+220' = '+220',
  '+995' = '+995',
  '+49' = '+49',
  '+233' = '+233',
  '+350' = '+350',
  '+30' = '+30',
  '+299' = '+299',
  '+1473' = '+1473',
  '+590' = '+590',
  '+1671' = '+1671',
  '+502' = '+502',
  '+44-1481' = '+44-1481',
  '+224' = '+224',
  '+245' = '+245',
  '+592' = '+592',
  '+509' = '+509',
  '+379' = '+379',
  '+504' = '+504',
  '+852' = '+852',
  '+36' = '+36',
  '+354' = '+354',
  '+91' = '+91',
  '+62' = '+62',
  '+98' = '+98',
  '+964' = '+964',
  '+353' = '+353',
  '+44-1624' = '+44-1624',
  '+972' = '+972',
  '+39' = '+39',
  '+225' = '+225',
  '+1876' = '+1876',
  '+81' = '+81',
  '+44-1534' = '+44-1534',
  '+962' = '+962',
  '+77' = '+77',
  '+254' = '+254',
  '+686' = '+686',
  '+850' = '+850',
  '+82' = '+82',
  '+383' = '+383',
  '+965' = '+965',
  '+996' = '+996',
  '+856' = '+856',
  '+371' = '+371',
  '+961' = '+961',
  '+266' = '+266',
  '+231' = '+231',
  '+218' = '+218',
  '+423' = '+423',
  '+370' = '+370',
  '+352' = '+352',
  '+853' = '+853',
  '+261' = '+261',
  '+265' = '+265',
  '+60' = '+60',
  '+960' = '+960',
  '+223' = '+223',
  '+356' = '+356',
  '+692' = '+692',
  '+596' = '+596',
  '+222' = '+222',
  '+230' = '+230',
  '+262' = '+262',
  '+52' = '+52',
  '+691' = '+691',
  '+373' = '+373',
  '+377' = '+377',
  '+976' = '+976',
  '+382' = '+382',
  '+1664' = '+1664',
  '+212' = '+212',
  '+258' = '+258',
  '+95' = '+95',
  '+264' = '+264',
  '+674' = '+674',
  '+977' = '+977',
  '+31' = '+31',
  '+599' = '+599',
  '+687' = '+687',
  '+64' = '+64',
  '+505' = '+505',
  '+227' = '+227',
  '+234' = '+234',
  '+683' = '+683',
  '+672' = '+672',
  '+389' = '+389',
  '+1670' = '+1670',
  '+47' = '+47',
  '+968' = '+968',
  '+92' = '+92',
  '+680' = '+680',
  '+970' = '+970',
  '+507' = '+507',
  '+675' = '+675',
  '+595' = '+595',
  '+51' = '+51',
  '+63' = '+63',
  '+872' = '+872',
  '+48' = '+48',
  '+351' = '+351',
  '+1939' = '+1939',
  '+974' = '+974',

  '+40' = '+40',
  '+7' = '+7',
  '+250' = '+250',

  '+290' = '+290',
  '+1869' = '+1869',
  '+1758' = '+1758',

  '+508' = '+508',
  '+1784' = '+1784',
  '+685' = '+685',
  '+378' = '+378',
  '+239' = '+239',
  '+966' = '+966',
  '+221' = '+221',
  '+381' = '+381',
  '+248' = '+248',
  '+232' = '+232',
  '+65' = '+65',
  '+1721' = '+1721',
  '+421' = '+421',
  '+386' = '+386',
  '+677' = '+677',
  '+252' = '+252',
  '+27' = '+27',

  '+211' = '+211',
  '+34' = '+34',
  '+94' = '+94',
  '+249' = '+249',
  '+597' = '+597',

  '+46' = '+46',
  '+41' = '+41',
  '+963' = '+963',
  '+886' = '+886',
  '+992' = '+992',
  '+255' = '+255',
  '+66' = '+66',
  '+670' = '+670',
  '+228' = '+228',
  '+690' = '+690',
  '+676' = '+676',
  '+1868' = '+1868',
  '+216' = '+216',
  '+90' = '+90',
  '+993' = '+993',
  '+1649' = '+1649',
  '+688' = '+688',
  '+256' = '+256',
  '+380' = '+380',
  '+971' = '+971',
  '+44' = '+44',
  '+598' = '+598',
  '+998' = '+998',
  '+678' = '+678',
  '+58' = '+58',
  '+84' = '+84',
  '+1284' = '+1284',
  '+1340' = '+1340',
  '+681' = '+681',
  '+967' = '+967',
  '+260' = '+260',
  '+263' = '+263',
}

type CountryIsoCodeMappingType = {
  [dailCode in CountryDialCode]: (typeof countries)[number];
};

export const countryCodeMapping: Partial<CountryIsoCodeMappingType> =
  countries.reduce<Partial<CountryIsoCodeMappingType>>(
    ((prevValue: any, country: any) => {
      prevValue[country.dialCode] = country;
      return prevValue;
    }) as any,
    {},
  );
