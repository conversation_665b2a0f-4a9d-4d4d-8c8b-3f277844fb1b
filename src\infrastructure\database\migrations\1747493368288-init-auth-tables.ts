import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitAuthTables1747493368288 implements MigrationInterface {
  name = 'InitAuthTables1747493368288';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."dial_code_enum" AS ENUM('+93', '+358', '+355', '+213', '+1684', '+376', '+244', '+1264', '+1268', '+54', '+374', '+297', '+247', '+61', '+43', '+994', '+1242', '+973', '+880', '+1246', '+375', '+32', '+501', '+229', '+1441', '+975', '+591', '+387', '+267', '+55', '+246', '+673', '+359', '+226', '+257', '+855', '+237', '+1', '+238', '+1345', '+236', '+235', '+56', '+86', '+57', '+269', '+242', '+682', '+506', '+385', '+53', '+357', '+420', '+243', '+45', '+253', '+1767', '+1849', '+593', '+20', '+503', '+240', '+291', '+372', '+268', '+251', '+500', '+298', '+679', '+33', '+594', '+689', '+241', '+220', '+995', '+49', '+233', '+350', '+30', '+299', '+1473', '+590', '+1671', '+502', '+44-1481', '+224', '+245', '+592', '+509', '+379', '+504', '+852', '+36', '+354', '+91', '+62', '+98', '+964', '+353', '+44-1624', '+972', '+39', '+225', '+1876', '+81', '+44-1534', '+962', '+77', '+254', '+686', '+850', '+82', '+383', '+965', '+996', '+856', '+371', '+961', '+266', '+231', '+218', '+423', '+370', '+352', '+853', '+261', '+265', '+60', '+960', '+223', '+356', '+692', '+596', '+222', '+230', '+262', '+52', '+691', '+373', '+377', '+976', '+382', '+1664', '+212', '+258', '+95', '+264', '+674', '+977', '+31', '+599', '+687', '+64', '+505', '+227', '+234', '+683', '+672', '+389', '+1670', '+47', '+968', '+92', '+680', '+970', '+507', '+675', '+595', '+51', '+63', '+872', '+48', '+351', '+1939', '+974', '+40', '+7', '+250', '+290', '+1869', '+1758', '+508', '+1784', '+685', '+378', '+239', '+966', '+221', '+381', '+248', '+232', '+65', '+1721', '+421', '+386', '+677', '+252', '+27', '+211', '+34', '+94', '+249', '+597', '+46', '+41', '+963', '+886', '+992', '+255', '+66', '+670', '+228', '+690', '+676', '+1868', '+216', '+90', '+993', '+1649', '+688', '+256', '+380', '+971', '+44', '+598', '+998', '+678', '+58', '+84', '+1284', '+1340', '+681', '+967', '+260', '+263')`,
    );
    await queryRunner.query(`CREATE TABLE "users"
                             (
                               "id"          SERIAL            NOT NULL,
                               "createdAt"   TIMESTAMP         NOT NULL DEFAULT now(),
                               "updateAt"    TIMESTAMP         NOT NULL DEFAULT now(),
                               "deletedAt"   TIMESTAMP,
                               "firstName"   character varying NOT NULL,
                               "lastName"    character varying,
                               "phoneNumber" character varying,
                               "countryCode" "public"."dial_code_enum",
                               "email"       character varying,
                               "password"    character varying NOT NULL,
                               "age"         integer,
                               "birthDate"   TIMESTAMP,
                               "apiKey"      character varying,
                               "roleId"      integer           NOT NULL,
                               CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
                             )`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "users_phone_role_unique" ON "users" ("phoneNumber", "roleId") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "users_email_role_unique" ON "users" ("email", "roleId") `,
    );
    await queryRunner.query(`CREATE TABLE "roles"
                             (
                               "id"        SERIAL            NOT NULL,
                               "createdAt" TIMESTAMP         NOT NULL DEFAULT now(),
                               "updateAt"  TIMESTAMP         NOT NULL DEFAULT now(),
                               "deletedAt" TIMESTAMP,
                               "name"      character varying NOT NULL,
                               CONSTRAINT "UQ_648e3f5447f725579d7d4ffdfb7" UNIQUE ("name"),
                               CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id")
                             )`);
    await queryRunner.query(`CREATE TABLE "permissions"
                             (
                               "id"            SERIAL            NOT NULL,
                               "createdAt"     TIMESTAMP         NOT NULL DEFAULT now(),
                               "updateAt"      TIMESTAMP         NOT NULL DEFAULT now(),
                               "deletedAt"     TIMESTAMP,
                               "action"        character varying NOT NULL,
                               "manuallyAdded" boolean           NOT NULL,
                               CONSTRAINT "UQ_1c1e0637ecf1f6401beb9a68abe" UNIQUE ("action"),
                               CONSTRAINT "PK_920331560282b8bd21bb02290df" PRIMARY KEY ("id")
                             )`);
    await queryRunner.query(`CREATE TABLE "roles_permissions_mapping"
                             (
                               "roleId"       integer NOT NULL,
                               "permissionId" integer NOT NULL,
                               CONSTRAINT "PK_102d2982949434a746e62e5461b" PRIMARY KEY ("roleId", "permissionId")
                             )`);
    await queryRunner.query(
      `CREATE INDEX "IDX_d0971eab999ee6b6b2efca2566" ON "roles_permissions_mapping" ("roleId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_487ee530fba51f4dd49edfd9c0" ON "roles_permissions_mapping" ("permissionId") `,
    );
    await queryRunner.query(`ALTER TABLE "users"
      ADD CONSTRAINT "FK_368e146b785b574f42ae9e53d5e" FOREIGN KEY ("roleId") REFERENCES "roles" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "roles_permissions_mapping"
      ADD CONSTRAINT "FK_d0971eab999ee6b6b2efca25667" FOREIGN KEY ("roleId") REFERENCES "roles" ("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    await queryRunner.query(`ALTER TABLE "roles_permissions_mapping"
      ADD CONSTRAINT "FK_487ee530fba51f4dd49edfd9c0c" FOREIGN KEY ("permissionId") REFERENCES "permissions" ("id") ON DELETE CASCADE ON UPDATE CASCADE`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "roles_permissions_mapping" DROP CONSTRAINT "FK_487ee530fba51f4dd49edfd9c0c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles_permissions_mapping" DROP CONSTRAINT "FK_d0971eab999ee6b6b2efca25667"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_368e146b785b574f42ae9e53d5e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_487ee530fba51f4dd49edfd9c0"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d0971eab999ee6b6b2efca2566"`,
    );
    await queryRunner.query(`DROP TABLE "roles_permissions_mapping"`);
    await queryRunner.query(`DROP TABLE "permissions"`);
    await queryRunner.query(`DROP TABLE "roles"`);
    await queryRunner.query(`DROP INDEX "public"."users_email_role_unique"`);
    await queryRunner.query(`DROP INDEX "public"."users_phone_role_unique"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TYPE "public"."dial_code_enum"`);
  }
}
