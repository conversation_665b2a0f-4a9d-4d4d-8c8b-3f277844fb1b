import { BooleanTransform } from '@app/common';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class ConfigurationValidationDto {
  @IsNotEmpty()
  @IsNumber()
  PORT: number;

  @IsNotEmpty()
  @IsString()
  HOST_URL: string;

  @IsNotEmpty()
  @IsString()
  NODE_ENV: string;

  @IsNotEmpty()
  @IsString()
  DATABASE_URL: string;

  @IsNotEmpty()
  @IsString()
  SWAGGER_PATH: string;

  @IsNotEmpty()
  @IsString()
  SWAGGER_VERSION: string;

  @IsNotEmpty()
  @IsString()
  SWAGGER_TITLE: string;

  @IsNotEmpty()
  @IsString()
  SWAGGER_DESCRIPTION: string;

  // Google Cloud Logging Configuration
  @BooleanTransform()
  @IsOptional()
  GOOGLE_CLOUD_LOGGING_ENABLED?: boolean;

  @IsOptional()
  @IsString()
  GOOGLE_CLOUD_PROJECT_ID?: string;

  @IsOptional()
  @IsString()
  GOOGLE_CLOUD_KEY_FILENAME?: string;

  @IsOptional()
  @IsString()
  GOOGLE_CLOUD_LOG_NAME?: string;

  // Google Places API Configuration
  @IsOptional()
  @IsString()
  GOOGLE_PLACES_API_KEY?: string;

  @IsOptional()
  @IsString()
  GOOGLE_PLACES_API_BASE_URL?: string;

  // JWT Configuration
  @IsNotEmpty()
  @IsString()
  JWT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  JWT_REFRESH_SECRET: string;

  @IsNotEmpty()
  @IsString()
  JWT_ACCESS_TOKEN_EXPIRATION: string;

  @IsNotEmpty()
  @IsString()
  JWT_REFRESH_TOKEN_EXPIRATION: string;
}
