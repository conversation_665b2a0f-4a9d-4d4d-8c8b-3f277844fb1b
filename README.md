src/
├─ modules/ ← One folder per **domain** (bounded context)
│ └─ user/ ← Example "User" domain
│ ├─ controllers/
│ ├─ services/
│ ├─ entities/ ← TypeORM entity or Prisma model
│ ├─ dto/
│ ├─ guards/
│ ├─ interceptors/
│ ├─ user.module.ts
│ └─ index.ts ← Re‑exports for cleaner imports
│ └─ order/
│ └─ ...
│
├─ common/ ← Re‑usable pieces shared across domains
│ ├─ decorators/
│ ├─ filters/
│ ├─ pipes/
│ ├─ interfaces/
│ └─ constants/
│
├─ infrastructure/ ← All technology‑specific plumbing
│ ├─ database/
│ │ ├─ typeorm.service.ts or typeorm.config.ts
│ │ └─ migrations/
│ ├─ cache/
│ ├─ messaging/ ← RabbitMQ, Kafka, etc. if needed
│ └─ storage/
│
├─ config/ ← Central configuration parsing/validation
│ └─ configuration.ts
│
├─ app.module.ts
├─ main.ts

## Docker Setup

### Prerequisites

- Docker and Docker Compose installed on your machine

### Running with Docker Compose

1. Clone the repository
2. Create a `.env` file in the root directory with required environment variables (or they will be set from docker-compose.yml)
3. Build and start the containers:
   ```bash
   docker-compose up -d
   ```
4. Access the application at http://localhost:3000
5. Access Swagger API documentation at http://localhost:3000/docs

### Running Database Migrations

To run database migrations inside the Docker container:

```bash
docker-compose exec api pnpm run migration:run
```

### Building Docker Image Manually

If you need to build the Docker image manually:

```bash
docker build -t qareeb-backend .
```

### Environment Variables

Key environment variables to configure:

- `PORT`: Application port (default: 3000)
- `DATABASE_URL`: PostgreSQL connection string
- `NODE_ENV`: Environment (development, production)
- `SWAGGER_PATH`: Path for API documentation
- `HOST_URL`: Base URL for the application
