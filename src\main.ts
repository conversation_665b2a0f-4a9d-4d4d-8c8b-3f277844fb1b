import { Logger, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

import {
  APP_NAME,
  ConfigurationValidationDto,
  initFilters,
  initInterceptors,
  InitSwagger,
  initValidationPipeline,
} from '@app/common';
import { initSecurity } from '@app/common/functions/init-security.function';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { AppModule } from './app.module';

(async () => {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  const logger = new Logger(APP_NAME);
  const configService: ConfigService<ConfigurationValidationDto, true> =
    app.get(ConfigService);

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));

  initValidationPipeline(app);

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  await InitSwagger({
    app,
    title: configService.get('SWAGGER_TITLE'),
    description: configService.get('SWAGGER_DESCRIPTION'),
    version: configService.get('SWAGGER_VERSION'),
    path: configService.get('SWAGGER_PATH'),
    hostURL: configService.get('HOST_URL'),
  });

  initFilters(app);

  initInterceptors(app);

  initSecurity(app);

  app.useStaticAssets(join(__dirname, '..', 'public'));

  await app.listen(configService.get<number>('PORT') ?? 3000).then(() => {
    logger.log(`Start Listening On Port ${configService.get('PORT')}`);
  });
})();
