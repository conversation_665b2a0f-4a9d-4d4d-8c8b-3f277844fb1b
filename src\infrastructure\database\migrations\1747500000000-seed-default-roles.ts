import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedDefaultRoles1747500000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create default roles
    const roles = [
      {
        name: 'Super Admin',
        description: 'Administrator role with full access',
      },
      {
        name: 'Customer',
        description: 'Regular user role with limited access',
      },
    ];

    // Insert roles
    for (const role of roles) {
      await queryRunner.query(
        `
        INSERT INTO "roles" ("name", "createdAt", "updateAt")
        VALUES ($1, NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING
      `,
        [role.name],
      );
    }

    // Assign all permissions to Super Admin role
    await queryRunner.query(`
      INSERT INTO "roles_permissions_mapping" ("roleId", "permissionId")
      SELECT r.id, p.id
      FROM "roles" r
      CROSS JOIN "permissions" p
      WHERE r.name = 'Super Admin'
      ON CONFLICT DO NOTHING
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove role-permission relationships
    await queryRunner.query(`
      DELETE FROM "roles_permissions_mapping"
      WHERE "roleId" IN (
        SELECT id FROM "roles" WHERE name IN ('Super Admin', 'Customer')
      )
    `);

    // Remove default roles
    await queryRunner.query(`
      DELETE FROM "roles" WHERE name IN ('Super Admin', 'Customer')
    `);
  }
}
