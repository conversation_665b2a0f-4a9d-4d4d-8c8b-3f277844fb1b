import { Controller, Get, HttpStatus, Inject } from '@nestjs/common';
import {
  ApiBasic<PERSON>uth,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { DashboardStatisticsDto } from '../dtos/dashboard-statistics.dto';
import { IDashboardService } from '../interfaces/dashboard-service.interface';
import { Public } from '@app/common';

@Controller('dashboard')
@ApiTags('Dashboard')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
export class DashboardController {
  constructor(
    @Inject(IDashboardService)
    private readonly dashboardService: IDashboardService,
  ) {}

  @Get('statistics')
  @ApiOperation({
    summary: 'Get dashboard statistics',
    description:
      'Returns aggregated counts for categories, features, locations, and businesses',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard statistics retrieved successfully.',
    type: DashboardStatisticsDto,
  })
  @Public() // Allow public access for dashboard statistics
  async getStatistics(): Promise<DashboardStatisticsDto> {
    return this.dashboardService.getStatistics();
  }
}
