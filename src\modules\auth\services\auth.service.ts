import { ConfigurationValidationDto } from '@app/common';
import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { AuthResponseDto } from '../dtos/auth/auth-response.dto';
import { JwtPayload } from '../dtos/auth/jwt-payload.dto';
import { LoginDto } from '../dtos/auth/login.dto';
import { RefreshTokenDto } from '../dtos/auth/refresh-token.dto';
import { RegisterDto } from '../dtos/auth/register.dto';
import { User } from '../entities/user.entity';
import { IAuthService } from '../interfaces/auth/auth-service.interface';
import { IRoleService } from '../interfaces/role/role-service.interface';
import { IUserService } from '../interfaces/user/user-service.interface';

@Injectable()
export class AuthService implements IAuthService {
  constructor(
    @Inject(IUserService)
    private userService: IUserService,
    @Inject(IRoleService)
    private roleService: IRoleService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService<
      ConfigurationValidationDto,
      true
    >,
  ) {}

  async register(dto: RegisterDto): Promise<AuthResponseDto> {
    const userWithRelations = await this.userService.findOneWithDetailsById(1);
    if (!userWithRelations) {
      throw new UnauthorizedException('User not found');
    }
    return this.generateTokens(userWithRelations);
  }

  async login({
    loginId,
    password,
    roleName,
  }: LoginDto): Promise<AuthResponseDto> {
    const user = await this.userService.validateUser({
      loginId,
      password,
      roleName,
      passwordValidation: true,
    });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return this.generateTokens(user);
  }

  async refreshToken(dto: RefreshTokenDto): Promise<AuthResponseDto> {
    try {
      const payload = this.jwtService.verify<JwtPayload>(dto.refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      const user = await this.userService.findOneWithDetailsById(
        parseInt(payload.sub),
      );

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return this.generateTokens(user);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async generateTokens(user: User): Promise<AuthResponseDto> {
    const role = await this.roleService.findOneById(user.roleId);
    const payload: JwtPayload = {
      sub: user.id.toString(),
      email: user.email,
      roleId: user.roleId,
      countryCode: user.countryCode,
      phoneNumber: user.phoneNumber,
      role: role,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('JWT_ACCESS_TOKEN_EXPIRATION'),
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get('JWT_REFRESH_TOKEN_EXPIRATION'),
    });

    // Remove password from user object
    const { password, ...userWithoutPassword } = user;

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: 3600, // 1 hour in seconds
      // user: userWithoutPassword as User,
    };
  }
}
