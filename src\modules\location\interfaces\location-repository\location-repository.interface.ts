import { IGenericRepository } from '@app/common';
import { Location } from '../../entities/location.entity';
import { LocationSchema } from '../../schemes/location.schema';

export interface ILocationRepository
  extends IGenericRepository<Location, typeof LocationSchema> {
  findByCoordinates(
    latitude: number,
    longitude: number,
    radiusKm?: number,
  ): Promise<Location[]>;
  findWithinBounds(
    minLatitude: number,
    maxLatitude: number,
    minLongitude: number,
    maxLongitude: number,
  ): Promise<Location[]>;
  findByCity(cityEn?: string, cityAr?: string): Promise<Location[]>;
  findByCountry(countryEn?: string, countryAr?: string): Promise<Location[]>;
  findByParentId(parentId: number | null): Promise<Location[]>;
  findWithChildren(id: number): Promise<Location | null>;
  findNearest(
    latitude: number,
    longitude: number,
    limit?: number,
  ): Promise<Location[]>;
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number;
}

export const ILocationRepository = Symbol('ILocationRepository');
