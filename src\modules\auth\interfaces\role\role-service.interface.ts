import { IGenericService } from '@app/common';
import { CheckRoleContainsPermissionDto } from '../../dtos/role/check-role-contains-permission.dto';
import { CreateRoleDto } from '../../dtos/role/create-role.dto';
import { GetAllRoleDto } from '../../dtos/role/get-all-role.dto';
import { UpdateRoleDto } from '../../dtos/role/update-role.dto';
import { Permission } from '../../entities/permission.entity';
import { Role } from '../../entities/role.entity';
import { RoleSchema } from '../../schemes/role.schema';
import { EntityManager } from 'typeorm';

export interface IRoleService
  extends IGenericService<
    Role,
    typeof RoleSchema,
    GetAllRoleDto,
    CreateRoleDto,
    UpdateRoleDto
  > {
  checkIfContainsPermission(
    checkRoleContainsPermissionDto: CheckRoleContainsPermissionDto,
  ): Promise<boolean>;

  addNewPermission(
    role: Role,
    permission: Permission,
    manager?: EntityManager,
  ): Promise<Role | null>;

  findByName(name: string): Promise<Role>;

  findOneWithPermissions(id: number): Promise<Role>;
}

export const IRoleService = Symbol('IRoleService');
