import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddArabicFeatures1750601793000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const features = [
      { nameEn: 'Freezers', nameAr: 'مجمدات', icon: '🧊' },
      { nameEn: 'Bread', nameAr: 'عيش', icon: '🍞' },
      { nameEn: 'Animal Feed', nameAr: 'علافه', icon: '🌾' },
      { nameEn: 'Dairy Products', nameAr: 'البان', icon: '🥛' },
      { nameEn: 'Spices', nameAr: 'عطاره', icon: '🌶️' },
      { nameEn: 'Vegetables', nameAr: 'خضار', icon: '🥬' },
      { nameEn: 'Fruits', nameAr: 'فاكهه', icon: '🍎' },
      { nameEn: 'Electrical', nameAr: 'كهربائي', icon: '⚡' },
      { nameEn: 'Kitchenware', nameAr: 'موان', icon: '🍴' },
      { nameEn: 'Coffee', nameAr: 'قهوه', icon: '☕' },
      { nameEn: 'Fish', nameAr: 'اسماك', icon: '🐟' },
      { nameEn: 'Poultry', nameAr: 'طيور', icon: '🐔' },
      { nameEn: 'Meat', nameAr: 'لحوم', icon: '🥩' },
      { nameEn: 'Pharmacy', nameAr: 'صيدليه', icon: '💊' },
      { nameEn: 'Cleaning Products', nameAr: 'منظفات', icon: '🧹' },
      { nameEn: 'Koshary', nameAr: 'كشري', icon: '🍛' },
      { nameEn: 'Fast Food', nameAr: 'أكل سريع', icon: '🍔' },
      { nameEn: 'Liver', nameAr: 'كبده', icon: '🍖' },
      { nameEn: 'Ladies Hair Salon', nameAr: 'كوافير حريمي', icon: '💇‍♀️' },
      { nameEn: 'Men Hair Salon', nameAr: 'كوافير رجالي', icon: '💇‍♂️' },
      { nameEn: 'Bookstore', nameAr: 'مكتبه', icon: '📚' },
      { nameEn: 'Kids Clothing', nameAr: 'ملابس اطفال', icon: '👶' },
      { nameEn: 'Adult Clothing', nameAr: 'ملابس كبار', icon: '👔' },
      {
        nameEn: 'Mobile Accessories',
        nameAr: 'اكسسوارات موبايلات',
        icon: '📱',
      },
      {
        nameEn: 'Computer Accessories',
        nameAr: 'اكسسوارات كومبيوتر',
        icon: '💻',
      },
      { nameEn: 'Bakery', nameAr: 'مخبوزات', icon: '🥐' },
      { nameEn: 'ATM', nameAr: 'ATM', icon: '🏧' },
      { nameEn: 'Sweets', nameAr: 'حلويات', icon: '🍰' },
      { nameEn: 'Laundry', nameAr: 'غسيل هدوم', icon: '🧺' },
      { nameEn: 'Clothes Ironing', nameAr: 'مكواه هدوم', icon: '👕' },
      { nameEn: 'Clothes Repair', nameAr: 'تصليح الملابس', icon: '🧵' },
      { nameEn: 'Carpet Cleaning', nameAr: 'غسيل سجاد', icon: '🧽' },
      { nameEn: 'Plastics', nameAr: 'بلاستيكات', icon: '🛍️' },
      { nameEn: 'Home Tools', nameAr: 'أدوات منزل', icon: '🔨' },
    ];

    for (const feature of features) {
      await queryRunner.query(
        `
        INSERT INTO "features" ("nameEn", "nameAr", "icon", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT ("nameEn") DO NOTHING
        `,
        [feature.nameEn, feature.nameAr, feature.icon],
      );
    }

    console.log('Arabic features added successfully!');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM "features" WHERE "nameEn" IN (
        'Freezers',
        'Bread',
        'Animal Feed',
        'Dairy Products',
        'Spices',
        'Vegetables',
        'Fruits',
        'Electrical',
        'Kitchenware',
        'Coffee',
        'Fish',
        'Poultry',
        'Meat',
        'Pharmacy',
        'Cleaning Products',
        'Koshary',
        'Fast Food',
        'Liver',
        'Ladies Hair Salon',
        'Men Hair Salon',
        'Bookstore',
        'Kids Clothing',
        'Adult Clothing',
        'Mobile Accessories',
        'Computer Accessories',
        'Bakery',
        'ATM',
        'Sweets',
        'Laundry',
        'Clothes Ironing',
        'Clothes Repair',
        'Carpet Cleaning',
        'Plastics',
        'Home Tools'
      )
    `);

    console.log('Arabic features removed successfully!');
  }
}
