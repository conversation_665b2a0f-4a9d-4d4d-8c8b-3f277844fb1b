import { ConfigurationValidationDto } from '@app/common';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './controllers/auth.controller';
import { PermissionController } from './controllers/permission.controller';
import { RoleController } from './controllers/role.controller';
import { UserController } from './controllers/user.controller';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { RolesGuard } from './guards/roles.guard';
import { IPermissionRepository } from './interfaces/permission/permission-repository.interface';
import { IPermissionService } from './interfaces/permission/permission-service.interface';
import { IRoleRepository } from './interfaces/role/role-repository.interface';
import { IRoleService } from './interfaces/role/role-service.interface';
import { IUserRepository } from './interfaces/user/user-repository.interface';
import { IUserService } from './interfaces/user/user-service.interface';
import { PermissionDataListener } from './listeners/permission-data.listener';
import { UserDatabaseListener } from './listeners/user-database.listener';
import { PermissionRepository } from './repositories/permission.repository';
import { RoleRepository } from './repositories/role.repository';
import { UserRepository } from './repositories/user.repository';
import { PermissionSchema } from './schemes/permission.schema';
import { RoleSchema } from './schemes/role.schema';
import { UserSchema } from './schemes/user.schema';
import { AuthService } from './services/auth.service';
import { PermissionService } from './services/permission.service';
import { RoleService } from './services/role.service';
import { UserService } from './services/user.service';
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserSchema, RoleSchema, PermissionSchema]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<ConfigurationValidationDto>,
      ) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_ACCESS_TOKEN_EXPIRATION'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [
    UserController,
    PermissionController,
    RoleController,
    AuthController,
  ],
  providers: [
    {
      provide: IUserRepository,
      useClass: UserRepository,
    },
    {
      provide: IRoleRepository,
      useClass: RoleRepository,
    },
    {
      provide: IRoleService,
      useClass: RoleService,
    },
    {
      provide: IPermissionRepository,
      useClass: PermissionRepository,
    },
    {
      provide: IUserService,
      useClass: UserService,
    },
    {
      provide: IPermissionService,
      useClass: PermissionService,
    },

    UserDatabaseListener,
    PermissionDataListener,
    AuthService,
    JwtStrategy,
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
  exports: [
    IUserService,
    IRoleService,
    IPermissionService,
    AuthService,
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
})
export class AuthModule {}
