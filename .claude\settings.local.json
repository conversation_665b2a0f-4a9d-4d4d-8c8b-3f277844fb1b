{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "Bash(NODE_ENV=test pnpm migration:run)", "Bash(NODE_ENV=test npx jest --config ./test/jest-e2e.json role.e2e-spec.ts --testNamePattern=\"should create a new role with permissions\" --verbose)", "Bash(NODE_ENV=test pnpm test:e2e role.e2e-spec.ts)", "Bash(NODE_ENV=test npx ts-node -r tsconfig-paths/register create-test-user.ts)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm run test:e2e:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm run:*)", "Bash(pnpm lint:*)", "Bash(pnpm format:*)", "<PERSON><PERSON>(mv:*)", "Bash(dpkg:*)", "<PERSON><PERSON>(chmod:*)", "Bash(pnpm build)", "Bash(rg:*)"]}, "enableAllProjectMcpServers": false}