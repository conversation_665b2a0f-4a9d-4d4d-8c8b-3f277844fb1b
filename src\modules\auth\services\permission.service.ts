import { Inject, Injectable } from '@nestjs/common';
import { GenericService, IGetAllResponseInterface } from '@app/common';
import { CreatePermissionsDto } from '../dtos/permission/create-permissions.dto';
import { GetAllPermissionsDto } from '../dtos/permission/get-all-permissions.dto';
import { Permission } from '../entities/permission.entity';
import { IPermissionRepository } from '../interfaces/permission/permission-repository.interface';
import { IPermissionService } from '../interfaces/permission/permission-service.interface';
import { PermissionSchema } from '../schemes/permission.schema';
import { FindManyOptions } from 'typeorm';

/**
 * Service responsible for managing permission entities
 */
@Injectable()
export class PermissionService
  extends GenericService<
    Permission,
    typeof PermissionSchema,
    GetAllPermissionsDto,
    CreatePermissionsDto,
    CreatePermissionsDto
  >
  implements IPermissionService
{
  constructor(
    @Inject(IPermissionRepository)
    private readonly permissionRepository: IPermissionRepository,
  ) {
    super(permissionRepository);
  }

  async getAll(
    getAllDto: GetAllPermissionsDto,
    initialQuery?: FindManyOptions<Permission>,
  ): Promise<IGetAllResponseInterface<Permission>> {
    if (getAllDto.searchKey) getAllDto.searchOnJson = 'action';
    return super.getAll(getAllDto, initialQuery);
  }

  /**
   * Finds a permission by action or creates a new one if it doesn't exist
   * @param createDto The permission data to find or create
   * @returns The found or created permission
   */
  async findOrCreate(createDto: CreatePermissionsDto): Promise<Permission> {
    const existingPermission = await this.permissionRepository.findOneBy({
      where: { action: createDto.action },
    });

    if (existingPermission) {
      return existingPermission;
    }

    return this.create(Permission, createDto);
  }
}
