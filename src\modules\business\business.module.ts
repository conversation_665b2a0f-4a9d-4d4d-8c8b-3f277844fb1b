import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { CategoryModule } from '../category/category.module';
import { FeatureModule } from '../feature/feature.module';
import { LocationModule } from '../location/location.module';
import { BusinessController } from './controllers/business.controller';
import { IBusinessRepository } from './interfaces/business-repository/business-repository.interface';
import { IBusinessService } from './interfaces/business-service/business-service.interface';
import { BusinessRepository } from './repositories/business.repository';
import { BusinessSchema } from './schemes/business.schema';
import { BusinessService } from './services/business.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([BusinessSchema]),
    AuthModule,
    LocationModule,
    CategoryModule,
    FeatureModule,
  ],
  controllers: [BusinessController],
  providers: [
    {
      provide: IBusinessRepository,
      useClass: BusinessRepository,
    },
    {
      provide: IBusinessService,
      useClass: BusinessService,
    },
  ],
  exports: [IBusinessService, IBusinessRepository],
})
export class BusinessModule {}
