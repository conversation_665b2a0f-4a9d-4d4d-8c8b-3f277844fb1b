import { IsBoolean, Is<PERSON>num, <PERSON>N<PERSON><PERSON><PERSON>y, IsOptional } from 'class-validator';

import { PickType } from '@nestjs/swagger';
import { User } from '../../entities/user.entity';
import { DefaultRole } from '@app/common';

export class ValidateUserDto extends PickType(User, ['password'] as const) {
  @IsNotEmpty()
  loginId: string;

  @IsNotEmpty()
  @IsBoolean()
  passwordValidation: boolean;

  @IsEnum(DefaultRole)
  @IsOptional()
  roleName?: DefaultRole;
}
