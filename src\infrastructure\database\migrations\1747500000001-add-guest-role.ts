import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGuestRole1747500000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create Guest role
    await queryRunner.query(
      `
      INSERT INTO "roles" ("name", "createdAt", "updateAt")
      VALUES ('Guest', NOW(), NOW())
      ON CONFLICT ("name") DO NOTHING
    `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove guest role-permission relationships
    await queryRunner.query(`
      DELETE FROM "roles_permissions_mapping"
      WHERE "roleId" IN (
        SELECT id FROM "roles" WHERE name = 'Guest'
      )
    `);

    // Remove Guest role
    await queryRunner.query(`
      DELETE FROM "roles" WHERE name = 'Guest'
    `);
  }
}
