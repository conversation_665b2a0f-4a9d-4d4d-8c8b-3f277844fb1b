import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFeaturesTable1748958500000 implements MigrationInterface {
  name = 'CreateFeaturesTable1748958500000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create features table
    await queryRunner.query(
      `CREATE TABLE "features" (
        "id" SERIAL NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "nameEn" character varying NOT NULL,
        "nameAr" character varying NOT NULL,
        "icon" character varying NOT NULL,
        CONSTRAINT "PK_features" PRIMARY KEY ("id")
      )`,
    );

    // Create unique indices
    await queryRunner.query(
      `CREATE UNIQUE INDEX "features_name_en_unique" ON "features" ("nameEn")`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX "features_name_ar_unique" ON "features" ("nameAr")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indices
    await queryRunner.query(`DROP INDEX IF EXISTS "features_name_ar_unique"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "features_name_en_unique"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "features"`);
  }
}
