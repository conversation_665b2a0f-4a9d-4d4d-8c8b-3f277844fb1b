import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOwnerBussniessRelation1750788437584 implements MigrationInterface {
    name = 'UpdateOwnerBussniessRelation1750788437584'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "businesses" DROP CONSTRAINT "FK_cc99d9f12bfc4456c2a7e8b45f7"`);
        await queryRunner.query(`ALTER TABLE "businesses" ALTER COLUMN "ownerUserId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "businesses" ADD CONSTRAINT "FK_cc99d9f12bfc4456c2a7e8b45f7" FOREIGN KEY ("ownerUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "businesses" DROP CONSTRAINT "FK_cc99d9f12bfc4456c2a7e8b45f7"`);
        await queryRunner.query(`ALTER TABLE "businesses" ALTER COLUMN "ownerUserId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "businesses" ADD CONSTRAINT "FK_cc99d9f12bfc4456c2a7e8b45f7" FOREIGN KEY ("ownerUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
