import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLocationsTable1748960000000 implements MigrationInterface {
  name = 'CreateLocationsTable1748960000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create locations table
    await queryRunner.query(
      `CREATE TABLE "locations" (
        "id" SERIAL NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "nameEn" character varying NOT NULL,
        "nameAr" character varying NOT NULL,
        "latitude" decimal(10,8) NOT NULL,
        "longitude" decimal(11,8) NOT NULL,
        "streetAddressEn" character varying,
        "streetAddressAr" character varying,
        "cityEn" character varying,
        "cityAr" character varying,
        "stateEn" character varying,
        "stateAr" character varying,
        "countryEn" character varying,
        "countryAr" character varying,
        "postalCode" character varying,
        "nearestLandmarkEn" character varying,
        "nearestLandmarkAr" character varying,
        "descriptionEn" text,
        "descriptionAr" text,
        "mapUrl" character varying,
        "placeId" character varying,
        "accuracy" decimal(3,2),
        "altitude" decimal(8,2),
        "timezone" character varying,
        "parentId" integer,
        CONSTRAINT "PK_locations" PRIMARY KEY ("id")
      )`,
    );

    // Create foreign key constraint for parent relationship
    await queryRunner.query(
      `ALTER TABLE "locations" ADD CONSTRAINT "FK_locations_parent" FOREIGN KEY ("parentId") REFERENCES "locations"("id") ON DELETE CASCADE`,
    );

    // Create indices for better performance
    await queryRunner.query(
      `CREATE INDEX "locations_coordinates_index" ON "locations" ("latitude", "longitude")`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX "locations_place_id_unique" ON "locations" ("placeId") WHERE "placeId" IS NOT NULL`,
    );

    await queryRunner.query(
      `CREATE INDEX "locations_parent_id_index" ON "locations" ("parentId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "locations_city_en_index" ON "locations" ("cityEn")`,
    );

    await queryRunner.query(
      `CREATE INDEX "locations_city_ar_index" ON "locations" ("cityAr")`,
    );

    await queryRunner.query(
      `CREATE INDEX "locations_country_en_index" ON "locations" ("countryEn")`,
    );

    await queryRunner.query(
      `CREATE INDEX "locations_country_ar_index" ON "locations" ("countryAr")`,
    );

    // Create spatial index for coordinates (if PostGIS is available)
    // Note: This might fail if PostGIS extension is not installed
    try {
      // First check if PostGIS extension exists
      const postgisCheck = await queryRunner.query(
        `SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'postgis')`,
      );

      if (postgisCheck[0].exists) {
        await queryRunner.query(
          `CREATE INDEX "locations_spatial_index" ON "locations" USING GIST (ST_Point("longitude", "latitude"))`,
        );
        console.log('PostGIS spatial index created successfully');
      } else {
        console.warn(
          'PostGIS extension not installed, skipping spatial index creation',
        );
      }
    } catch (error) {
      // PostGIS not available, spatial queries will use regular coordinate indices
      console.warn(
        'PostGIS extension not available, using regular coordinate indices:',
        error.message,
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop spatial index if it exists
    await queryRunner.query(`DROP INDEX IF EXISTS "locations_spatial_index"`);

    // Drop regular indices
    await queryRunner.query(
      `DROP INDEX IF EXISTS "locations_country_ar_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "locations_country_en_index"`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS "locations_city_ar_index"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "locations_city_en_index"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "locations_parent_id_index"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "locations_place_id_unique"`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS "locations_coordinates_index"`,
    );

    // Drop foreign key constraint
    await queryRunner.query(
      `ALTER TABLE "locations" DROP CONSTRAINT IF EXISTS "FK_locations_parent"`,
    );

    // Drop table
    await queryRunner.query(`DROP TABLE "locations"`);
  }
}
