import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IGetAllResponseInterface } from '../../../common/interfaces';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { CreateCategoryDto } from '../dtos/create-category.dto';
import { GetAllCategoryDto } from '../dtos/get-all-category.dto';
import { UpdateCategoryDto } from '../dtos/update-category.dto';
import { Category } from '../entities/category.entity';
import { ICategoryService } from '../interfaces/category-service/category-service.interface';
import { Public } from '@app/common';

@Controller('categories')
@ApiTags('Categories')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class CategoryController {
  constructor(
    @Inject(ICategoryService)
    private readonly categoryService: ICategoryService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all categories with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The categories have been successfully retrieved.',
    type: Category,
    isArray: true,
  })
  @Public()
  async getAll(
    @Query() getAllDto: GetAllCategoryDto,
  ): Promise<IGetAllResponseInterface<Category>> {
    return this.categoryService.getAll(getAllDto);
  }

  @Get('hierarchy')
  @ApiOperation({ summary: 'Get category hierarchy' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The category hierarchy has been successfully retrieved.',
    type: Category,
    isArray: true,
  })
  @Public()
  async getHierarchy(): Promise<Category[]> {
    return this.categoryService.findHierarchy();
  }

  @Get('parent/:parentId')
  @ApiOperation({ summary: 'Get categories by parent ID' })
  @ApiParam({
    name: 'parentId',
    description: 'Parent Category ID',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The categories have been successfully retrieved.',
    type: Category,
    isArray: true,
  })
  async getByParentId(
    @Param('parentId', ParseIntPipe) parentId: number,
  ): Promise<Category[]> {
    return this.categoryService.findByParentId(parentId);
  }

  @Get('level/:level')
  @ApiOperation({ summary: 'Get categories by level' })
  @ApiParam({ name: 'level', description: 'Category level', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The categories have been successfully retrieved.',
    type: Category,
    isArray: true,
  })
  async getByLevel(
    @Param('level', ParseIntPipe) level: number,
  ): Promise<Category[]> {
    return this.categoryService.findByLevel(level);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get category by ID' })
  @ApiParam({ name: 'id', description: 'Category ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The category has been successfully retrieved.',
    type: Category,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found.',
  })
  async getById(@Param('id', ParseIntPipe) id: number): Promise<Category> {
    return this.categoryService.findOneById(id);
  }

  @Get(':id/with-children')
  @ApiOperation({ summary: 'Get category with children' })
  @ApiParam({ name: 'id', description: 'Category ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The category with children has been successfully retrieved.',
    type: Category,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found.',
  })
  async getWithChildren(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Category | null> {
    return this.categoryService.findWithChildren(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create new category' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The category has been successfully created.',
    type: Category,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDto: CreateCategoryDto): Promise<Category> {
    return this.categoryService.create(Category, createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an existing category' })
  @ApiParam({ name: 'id', description: 'Category ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The category has been successfully updated.',
    type: Category,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found.',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCategoryDto,
  ): Promise<Category> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.categoryService.update(Category, updateDtoWithId);
  }

  @Put(':id/business-count/:count')
  @ApiOperation({ summary: 'Update number of businesses for a category' })
  @ApiParam({ name: 'id', description: 'Category ID', type: Number })
  @ApiParam({
    name: 'count',
    description: 'Number of businesses',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business count has been successfully updated.',
  })
  async updateBusinessCount(
    @Param('id', ParseIntPipe) id: number,
    @Param('count', ParseIntPipe) count: number,
  ): Promise<void> {
    return this.categoryService.updateNumberOfBusinesses(id, count);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a category' })
  @ApiParam({ name: 'id', description: 'Category ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The category has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.categoryService.deleteOne(id);
  }
}
