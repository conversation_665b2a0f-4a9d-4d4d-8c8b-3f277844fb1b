<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأعمال التجارية - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pagination.css">
    
    <style>
        .businesses-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .search-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            padding: 10px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            min-width: 250px;
            transition: var(--transition);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            min-width: 150px;
        }
        
        .businesses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .business-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .business-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .business-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .business-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-gradient);
            border-radius: 12px;
            color: white;
        }
        
        .business-info h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .business-category {
            color: #666;
            font-size: 0.9rem;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        
        .business-details {
            margin-bottom: 20px;
        }
        
        .business-detail {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .business-detail i {
            width: 16px;
            color: var(--primary-color);
        }
        
        .business-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }

        .badge {
            display: inline-block;
            padding: 2px 6px;
            font-size: 0.7rem;
            font-weight: 600;
            border-radius: 3px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-right: 4px;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .badge-warning {
            background: var(--warning-color);
            color: #212529;
        }

        .badge-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }
        
        .loading-container .loading-spinner {
            width: 40px;
            height: 40px;
            border-width: 4px;
            border-color: rgba(102, 126, 234, 0.3);
            border-top-color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
            display: block;
        }
        
        @media (max-width: 768px) {
            .businesses-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-filters {
                flex-direction: column;
            }
            
            .search-input,
            .filter-select {
                min-width: 100%;
            }
            
            .businesses-grid {
                grid-template-columns: 1fr;
            }
            
            .business-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">الأعمال التجارية</h1>
            <p class="page-subtitle">إدارة وعرض جميع الأعمال التجارية المسجلة</p>
        </div>
        
        <div class="businesses-header">
            <div class="search-filters">
                <input type="text" id="searchInput" class="search-input" placeholder="البحث في الأعمال التجارية..." dir="rtl">
                <select id="categoryFilter" class="filter-select">
                    <option value="">جميع الفئات</option>
                </select>
                <button class="btn btn-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                    مسح الفلاتر
                </button>
            </div>
            
            <a href="form.html" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة عمل تجاري جديد
            </a>
        </div>
        
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p>جاري تحميل الأعمال التجارية...</p>
        </div>
        
        <div id="businessesContainer" class="businesses-grid" style="display: none;">
            <!-- Businesses will be loaded here -->
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-store-slash"></i>
            <h3>لا توجد أعمال تجارية</h3>
            <p>لم يتم العثور على أي أعمال تجارية مطابقة للبحث</p>
            <a href="form.html" class="btn btn-primary" style="margin-top: 20px;">
                <i class="fas fa-plus"></i>
                إضافة أول عمل تجاري
            </a>
        </div>
        
        <div class="response-message" id="responseMessage"></div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script src="../../assets/js/pagination.js"></script>
    <script>
        let businesses = [];
        let categories = [];
        let currentSearchTerm = '';
        let currentCategoryFilter = '';
        let pagination = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePagination();
            loadCategories();
            loadBusinesses();
            setupEventListeners();
        });

        function initializePagination() {
            pagination = new PaginationManager({
                containerId: 'paginationContainer',
                pageSize: 8,
                onPageChange: (page, offset) => {
                    loadBusinesses(offset, pagination.pageSize);
                },
                onPageSizeChange: (pageSize) => {
                    loadBusinesses(0, pageSize);
                },
                showPageSizeSelector: true,
                pageSizeOptions: [4, 8, 16, 32]
            });
        }

        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearchTerm = this.value.trim();
                    loadBusinesses(0, pagination.pageSize);
                }, 300);
            });

            categoryFilter.addEventListener('change', function() {
                currentCategoryFilter = this.value;
                loadBusinesses(0, pagination.pageSize);
            });
        }
        
        async function loadCategories() {
            try {
                const response = await fetch('/v1/categories', {
                    headers: AuthUtils.getAuthHeaders()
                });
                if (response.ok) {
                    const result = await response.json();
                    categories = result.data?.items || result.data || result;
                    populateCategoryFilter();
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }
        
        function populateCategoryFilter() {
            const categoryFilter = document.getElementById('categoryFilter');
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.nameAr;
                categoryFilter.appendChild(option);
            });
        }
        
        async function loadBusinesses(offset = 0, limit = 8) {
            const loadingContainer = document.getElementById('loadingContainer');
            const businessesContainer = document.getElementById('businessesContainer');
            const emptyState = document.getElementById('emptyState');

            try {
                loadingContainer.style.display = 'block';
                businessesContainer.style.display = 'none';
                emptyState.style.display = 'none';

                if (pagination) {
                    pagination.setLoading(true);
                }

                // Build query parameters
                const params = new URLSearchParams({
                    offset: offset.toString(),
                    limit: limit.toString()
                });

                if (currentSearchTerm) {
                    params.append('searchKey', currentSearchTerm);
                }

                if (currentCategoryFilter) {
                    params.append('primaryCategoryId', currentCategoryFilter);
                }

                const response = await fetch(`/v1/businesses?${params.toString()}`, {
                    headers: AuthUtils.getAuthHeaders()
                });

                if (response.ok) {
                    const result = await response.json();
                    // Handle different response formats
                    if (result.data) {
                        // Format: { data: { items: [], count: number } }
                        businesses = result.data.items || [];
                        const totalCount = result.data.count || result.data.totalCount || businesses.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    } else {
                        // Direct format: { items: [], count: number }
                        businesses = result.items || result;
                        const totalCount = result.count || businesses.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    }

                    if (businesses.length === 0) {
                        emptyState.style.display = 'block';
                    } else {
                        displayBusinesses(businesses);
                        businessesContainer.style.display = 'grid';
                    }
                } else {
                    throw new Error('Failed to load businesses');
                }
            } catch (error) {
                console.error('Error loading businesses:', error);
                showMessage('فشل في تحميل الأعمال التجارية', 'error');
                emptyState.style.display = 'block';

                if (pagination) {
                    pagination.updateData(0);
                }
            } finally {
                loadingContainer.style.display = 'none';
                if (pagination) {
                    pagination.setLoading(false);
                }
            }
        }
        
        function displayBusinesses(businessesToShow) {
            const container = document.getElementById('businessesContainer');
            container.innerHTML = '';
            
            businessesToShow.forEach(business => {
                const businessCard = createBusinessCard(business);
                container.appendChild(businessCard);
            });
        }
        
        function createBusinessCard(business) {
            const card = document.createElement('div');
            card.className = 'business-card';

            const category = categories.find(cat => cat.id === business.primaryCategoryId);
            const categoryName = category ? category.nameAr : 'غير محدد';

            // Status indicators
            const statusBadges = [];
            if (business.isVerified) statusBadges.push('<span class="badge badge-success">موثق</span>');
            if (business.isPremium) statusBadges.push('<span class="badge badge-warning">مميز</span>');
            if (!business.isActive) statusBadges.push('<span class="badge badge-danger">غير نشط</span>');

            card.innerHTML = `
                <div class="business-header">
                    <div class="business-icon">🏪</div>
                    <div class="business-info">
                        <h3>${business.nameAr}</h3>
                        <span class="business-category">${categoryName}</span>
                        ${statusBadges.length > 0 ? `<div style="margin-top: 5px;">${statusBadges.join(' ')}</div>` : ''}
                    </div>
                </div>

                <div class="business-details">
                    ${business.phoneNumber ? `
                        <div class="business-detail">
                            <i class="fas fa-phone"></i>
                            <span>${business.phoneNumber}</span>
                        </div>
                    ` : ''}

                    ${business.whatsAppNumber && business.whatsAppNumber !== business.phoneNumber ? `
                        <div class="business-detail">
                            <i class="fab fa-whatsapp"></i>
                            <span>${business.whatsAppNumber}</span>
                        </div>
                    ` : ''}

                    ${business.priceRange ? `
                        <div class="business-detail">
                            <i class="fas fa-dollar-sign"></i>
                            <span>${business.priceRange}</span>
                        </div>
                    ` : ''}

                    ${business.averageRating && parseFloat(business.averageRating) > 0 ? `
                        <div class="business-detail">
                            <i class="fas fa-star"></i>
                            <span>${business.averageRating} (${business.totalReviewsCount} تقييم)</span>
                        </div>
                    ` : ''}

                    ${business.shortDescriptionAr ? `
                        <div class="business-detail">
                            <i class="fas fa-info-circle"></i>
                            <span>${business.shortDescriptionAr.substring(0, 100)}${business.shortDescriptionAr.length > 100 ? '...' : ''}</span>
                        </div>
                    ` : ''}
                </div>

                <div class="business-actions">
                    <a href="edit.html?id=${business.id}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <button class="btn btn-danger btn-sm" onclick="deleteBusiness(${business.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            `;

            return card;
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            currentSearchTerm = '';
            currentCategoryFilter = '';
            loadBusinesses(0, pagination.pageSize);
        }
        
        async function deleteBusiness(businessId) {
            if (!confirm('هل أنت متأكد من حذف هذا العمل التجاري؟')) {
                return;
            }
            
            try {
                const response = await fetch(`/v1/businesses/${businessId}`, {
                    method: 'DELETE',
                    headers: AuthUtils.getAuthHeaders()
                });
                
                if (response.ok) {
                    showMessage('تم حذف العمل التجاري بنجاح', 'success');
                    // Reload current page
                    const currentOffset = pagination ? pagination.getOffset() : 0;
                    const currentLimit = pagination ? pagination.pageSize : 8;
                    loadBusinesses(currentOffset, currentLimit);
                } else {
                    throw new Error('Failed to delete business');
                }
            } catch (error) {
                console.error('Error deleting business:', error);
                showMessage('فشل في حذف العمل التجاري', 'error');
            }
        }
        
        function showMessage(message, type) {
            const responseMessage = document.getElementById('responseMessage');
            responseMessage.textContent = message;
            responseMessage.className = `response-message ${type}`;
            responseMessage.style.display = 'block';
            
            setTimeout(() => {
                responseMessage.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
