import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { AuthHelper } from '../helpers/auth.helper';

describe('Business (e2e)', () => {
  let app: INestApplication;
  let accessToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    const authTokens = await AuthHelper.login(app);
    accessToken = authTokens.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/businesses (POST)', () => {
    it('should create a business with location and features', async () => {
      // First, create a location
      const locationData = {
        nameEn: 'Test Location',
        nameAr: 'موقع تجريبي',
        latitude: 31.2001,
        longitude: 29.9187,
        cityEn: 'Alexandria',
        cityAr: 'الإسكندرية',
        countryEn: 'Egypt',
        countryAr: 'مصر',
      };

      const locationResponse = await request(app.getHttpServer())
        .post('/locations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(locationData)
        .expect(HttpStatus.CREATED);

      const locationId = locationResponse.body.id;

      // Create a category
      const categoryData = {
        nameEn: 'Test Category',
        nameAr: 'فئة تجريبية',
        descriptionEn: 'Test category description',
        descriptionAr: 'وصف الفئة التجريبية',
      };

      const categoryResponse = await request(app.getHttpServer())
        .post('/categories')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(categoryData)
        .expect(HttpStatus.CREATED);

      const categoryId = categoryResponse.body.id;

      // Create features
      const feature1Data = {
        nameEn: 'WiFi',
        nameAr: 'واي فاي',
        icon: 'wifi-icon',
      };

      const feature2Data = {
        nameEn: 'Parking',
        nameAr: 'موقف سيارات',
        icon: 'parking-icon',
      };

      const feature1Response = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(feature1Data)
        .expect(HttpStatus.CREATED);

      const feature2Response = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(feature2Data)
        .expect(HttpStatus.CREATED);

      const featureIds = [feature1Response.body.id, feature2Response.body.id];

      // Get initial category business count
      const initialCategoryResponse = await request(app.getHttpServer())
        .get(`/categories/${categoryId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(HttpStatus.OK);

      const initialBusinessCount =
        initialCategoryResponse.body.numberOfBusinesses;

      // Create a user for business owner
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Business',
        lastName: 'Owner',
      };

      const userResponse = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userData)
        .expect(HttpStatus.CREATED);

      const ownerId = userResponse.body.user.id;

      // Create business with location and features
      const businessData = {
        nameEn: 'Test Business',
        nameAr: 'عمل تجريبي',
        phoneNumber: '+201234567890',
        whatsAppNumber: '+201234567890',
        locationId,
        primaryCategoryId: categoryId,
        ownerUserId: ownerId,
        shortDescriptionEn: 'A test business',
        shortDescriptionAr: 'عمل تجريبي',
        featureIds,
      };

      const businessResponse = await request(app.getHttpServer())
        .post('/businesses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(businessData)
        .expect(HttpStatus.CREATED);

      const business = businessResponse.body;

      // Verify business was created with correct data
      expect(business.nameEn).toBe(businessData.nameEn);
      expect(business.nameAr).toBe(businessData.nameAr);
      expect(business.locationId).toBe(locationId);
      expect(business.primaryCategoryId).toBe(categoryId);
      expect(business.ownerUserId).toBe(ownerId);

      // Verify business has features
      const businessWithFeaturesResponse = await request(app.getHttpServer())
        .get(`/businesses/${business.id}?includeFeatures=true`)
        .expect(HttpStatus.OK);

      const businessWithFeatures = businessWithFeaturesResponse.body;
      expect(businessWithFeatures.features).toHaveLength(2);
      expect(businessWithFeatures.features.map((f: any) => f.id)).toEqual(
        expect.arrayContaining(featureIds),
      );

      // Verify category business count was incremented
      const updatedCategoryResponse = await request(app.getHttpServer())
        .get(`/categories/${categoryId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(HttpStatus.OK);

      const updatedBusinessCount =
        updatedCategoryResponse.body.numberOfBusinesses;
      expect(updatedBusinessCount).toBe(initialBusinessCount + 1);
    });

    it('should create a business without features', async () => {
      // Create minimal business data without features
      const locationData = {
        nameEn: 'Simple Location',
        nameAr: 'موقع بسيط',
        latitude: 30.0444,
        longitude: 31.2357,
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
      };

      const locationResponse = await request(app.getHttpServer())
        .post('/locations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(locationData)
        .expect(HttpStatus.CREATED);

      const categoryData = {
        nameEn: 'Simple Category',
        nameAr: 'فئة بسيطة',
      };

      const categoryResponse = await request(app.getHttpServer())
        .post('/categories')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(categoryData)
        .expect(HttpStatus.CREATED);

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Simple',
        lastName: 'Owner',
      };

      const userResponse = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userData)
        .expect(HttpStatus.CREATED);

      const businessData = {
        nameEn: 'Simple Business',
        nameAr: 'عمل بسيط',
        locationId: locationResponse.body.id,
        primaryCategoryId: categoryResponse.body.id,
        ownerUserId: userResponse.body.user.id,
      };

      const businessResponse = await request(app.getHttpServer())
        .post('/businesses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(businessData)
        .expect(HttpStatus.CREATED);

      const business = businessResponse.body;
      expect(business.nameEn).toBe(businessData.nameEn);
      expect(business.locationId).toBe(businessData.locationId);
      expect(business.primaryCategoryId).toBe(businessData.primaryCategoryId);
    });

    it('should fail to create business with invalid location', async () => {
      const businessData = {
        nameEn: 'Invalid Business',
        nameAr: 'عمل غير صحيح',
        locationId: 99999, // Non-existent location
        primaryCategoryId: 1,
        ownerUserId: 1,
      };

      await request(app.getHttpServer())
        .post('/businesses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(businessData)
        .expect(HttpStatus.NOT_FOUND);
    });

    it('should fail to create business with invalid features', async () => {
      // Create valid location and category first
      const locationData = {
        nameEn: 'Valid Location',
        nameAr: 'موقع صحيح',
        latitude: 30.0444,
        longitude: 31.2357,
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
      };

      const locationResponse = await request(app.getHttpServer())
        .post('/locations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(locationData)
        .expect(HttpStatus.CREATED);

      const categoryData = {
        nameEn: 'Valid Category',
        nameAr: 'فئة صحيحة',
      };

      const categoryResponse = await request(app.getHttpServer())
        .post('/categories')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(categoryData)
        .expect(HttpStatus.CREATED);

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Invalid',
        lastName: 'Owner',
      };

      const userResponse = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userData)
        .expect(HttpStatus.CREATED);

      const businessData = {
        nameEn: 'Business with Invalid Features',
        nameAr: 'عمل بميزات غير صحيحة',
        locationId: locationResponse.body.id,
        primaryCategoryId: categoryResponse.body.id,
        ownerUserId: userResponse.body.user.id,
        featureIds: [99999, 99998], // Non-existent features
      };

      await request(app.getHttpServer())
        .post('/businesses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(businessData)
        .expect(HttpStatus.NOT_FOUND);
    });
  });

  describe('/businesses/:id (PUT)', () => {
    it('should update a business with new features', async () => {
      // First, create a business with initial features
      const locationData = {
        nameEn: 'Update Test Location',
        nameAr: 'موقع اختبار التحديث',
        latitude: 31.2001,
        longitude: 29.9187,
        cityEn: 'Alexandria',
        cityAr: 'الإسكندرية',
        countryEn: 'Egypt',
        countryAr: 'مصر',
      };

      const locationResponse = await request(app.getHttpServer())
        .post('/locations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(locationData)
        .expect(HttpStatus.CREATED);

      const categoryData = {
        nameEn: 'Update Test Category',
        nameAr: 'فئة اختبار التحديث',
      };

      const categoryResponse = await request(app.getHttpServer())
        .post('/categories')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(categoryData)
        .expect(HttpStatus.CREATED);

      // Create initial features
      const initialFeature1 = {
        nameEn: 'Initial WiFi',
        nameAr: 'واي فاي أولي',
        icon: 'wifi-icon',
      };

      const initialFeature2 = {
        nameEn: 'Initial Parking',
        nameAr: 'موقف سيارات أولي',
        icon: 'parking-icon',
      };

      const initialFeature1Response = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(initialFeature1)
        .expect(HttpStatus.CREATED);

      const initialFeature2Response = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(initialFeature2)
        .expect(HttpStatus.CREATED);

      const initialFeatureIds = [
        initialFeature1Response.body.id,
        initialFeature2Response.body.id,
      ];

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Update',
        lastName: 'Owner',
      };

      const userResponse = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userData)
        .expect(HttpStatus.CREATED);

      // Create business with initial features
      const initialBusinessData = {
        nameEn: 'Business to Update',
        nameAr: 'عمل للتحديث',
        locationId: locationResponse.body.id,
        primaryCategoryId: categoryResponse.body.id,
        ownerUserId: userResponse.body.user.id,
        featureIds: initialFeatureIds,
      };

      const businessResponse = await request(app.getHttpServer())
        .post('/businesses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(initialBusinessData)
        .expect(HttpStatus.CREATED);

      const businessId = businessResponse.body.id;

      // Create new features for update
      const newFeature1 = {
        nameEn: 'New Air Conditioning',
        nameAr: 'تكييف هواء جديد',
        icon: 'ac-icon',
      };

      const newFeature2 = {
        nameEn: 'New Restaurant',
        nameAr: 'مطعم جديد',
        icon: 'restaurant-icon',
      };

      const newFeature1Response = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(newFeature1)
        .expect(HttpStatus.CREATED);

      const newFeature2Response = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(newFeature2)
        .expect(HttpStatus.CREATED);

      const newFeatureIds = [
        newFeature1Response.body.id,
        newFeature2Response.body.id,
      ];

      // Update business with new features
      const updateData = {
        nameEn: 'Updated Business Name',
        nameAr: 'اسم العمل المحدث',
        featureIds: newFeatureIds,
      };

      const updateResponse = await request(app.getHttpServer())
        .put(`/businesses/${businessId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(HttpStatus.OK);

      const updatedBusiness = updateResponse.body;

      // Verify business was updated
      expect(updatedBusiness.nameEn).toBe(updateData.nameEn);
      expect(updatedBusiness.nameAr).toBe(updateData.nameAr);

      // Verify business has new features and not the old ones
      const businessWithFeaturesResponse = await request(app.getHttpServer())
        .get(`/businesses/${businessId}`)
        .expect(HttpStatus.OK);

      const businessWithFeatures = businessWithFeaturesResponse.body;
      expect(businessWithFeatures.features).toHaveLength(2);

      const actualFeatureIds = businessWithFeatures.features.map(
        (f: any) => f.id,
      );
      expect(actualFeatureIds).toEqual(expect.arrayContaining(newFeatureIds));
      expect(actualFeatureIds).not.toEqual(
        expect.arrayContaining(initialFeatureIds),
      );
    });

    it('should update a business and remove all features', async () => {
      // Create a business with features first
      const locationData = {
        nameEn: 'Remove Features Location',
        nameAr: 'موقع إزالة الميزات',
        latitude: 30.0444,
        longitude: 31.2357,
        cityEn: 'Cairo',
        cityAr: 'القاهرة',
        countryEn: 'Egypt',
        countryAr: 'مصر',
      };

      const locationResponse = await request(app.getHttpServer())
        .post('/locations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(locationData)
        .expect(HttpStatus.CREATED);

      const categoryData = {
        nameEn: 'Remove Features Category',
        nameAr: 'فئة إزالة الميزات',
      };

      const categoryResponse = await request(app.getHttpServer())
        .post('/categories')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(categoryData)
        .expect(HttpStatus.CREATED);

      const featureData = {
        nameEn: 'Feature to Remove',
        nameAr: 'ميزة للإزالة',
        icon: 'remove-icon',
      };

      const featureResponse = await request(app.getHttpServer())
        .post('/features')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(featureData)
        .expect(HttpStatus.CREATED);

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Remove',
        lastName: 'Owner',
      };

      const userResponse = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userData)
        .expect(HttpStatus.CREATED);

      const businessData = {
        nameEn: 'Business with Features to Remove',
        nameAr: 'عمل بميزات للإزالة',
        locationId: locationResponse.body.id,
        primaryCategoryId: categoryResponse.body.id,
        ownerUserId: userResponse.body.user.id,
        featureIds: [featureResponse.body.id],
      };

      const businessResponse = await request(app.getHttpServer())
        .post('/businesses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(businessData)
        .expect(HttpStatus.CREATED);

      const businessId = businessResponse.body.id;

      // Update business without features (empty array)
      const updateData = {
        nameEn: 'Business without Features',
        featureIds: [],
      };

      await request(app.getHttpServer())
        .put(`/businesses/${businessId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(HttpStatus.OK);

      // Verify business has no features
      const businessWithoutFeaturesResponse = await request(app.getHttpServer())
        .get(`/businesses/${businessId}`)
        .expect(HttpStatus.OK);

      const businessWithoutFeatures = businessWithoutFeaturesResponse.body;
      expect(businessWithoutFeatures.features).toHaveLength(0);
    });
  });
});
