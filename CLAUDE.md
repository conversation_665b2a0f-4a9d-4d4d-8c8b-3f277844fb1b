# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `pnpm start:dev` - Start development server with hot reload
- `pnpm build` - Build the application
- `pnpm start:prod` - Start production server

### Testing
- `pnpm test` - Run unit tests
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:e2e` - Run end-to-end tests
- `pnpm test:cov` - Run tests with coverage

### Code Quality
- `pnpm lint` - Lint and fix TypeScript files
- `pnpm format` - Format code with Prettier

### Database Operations
- `pnpm run migration:generate` - Generate new migration based on entity changes
- `pnpm run migration:run` - Run pending migrations
- `pnpm run migration:revert` - Revert last migration
- `pnpm run migration:create --name=<migration-name>` - Create new empty migration

### Docker
- `docker-compose up -d` - Start all services
- `docker-compose exec api pnpm run migration:run` - Run migrations in container

## Architecture Overview

### Domain-Driven Structure
The codebase follows a modular architecture with domain-bounded contexts:

- `src/modules/` - Domain modules (one per bounded context)
  - Each module contains: controllers, services, entities, DTOs, guards, interceptors
  - Currently implements: `auth` module with user management, roles, and permissions
  - Standard module structure (example: `category` module):
    - `controllers/` - REST API endpoints with Swagger documentation
    - `services/` - Business logic extending GenericService
    - `entities/` - Domain entities with validation decorators
    - `repositories/` - Data access layer extending GenericRepository
    - `interfaces/` - Service and repository interfaces for dependency injection
    - `schemes/` - TypeORM schemas for database mapping
    - `dtos/` - Data transfer objects for API requests/responses
    - `module.ts` - NestJS module configuration with dependency injection
- `src/common/` - Shared utilities, decorators, filters, pipes, constants
- `src/infrastructure/` - Technology-specific implementations (database, cache, storage)
- `src/config/` - Configuration management with validation

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (RBAC) with granular permissions
- Three-tier security: JWT validation → Role checking → Permission verification
- Custom decorators: `@Roles()`, `@Permissions()`, `@CurrentUser()`
- Password hashing with Argon2

### Database Layer
- TypeORM with PostgreSQL
- Repository pattern with interface abstractions
- Database migrations in `src/infrastructure/database/migrations/`
- Entities use schema files (e.g., `UserSchema`, `RoleSchema`)
- Connection configuration in `src/infrastructure/database/services/datasource.ts`

### Key Patterns
- Interface-based dependency injection (e.g., `IUserService`, `IUserRepository`)
- Generic services and repositories for common CRUD operations with inheritance
- Service layer extends `GenericService` for common CRUD with custom business logic
- Repository layer extends `GenericRepository` for data access with custom queries
- Entity-based domain modeling with class-validator decorators
- TypeORM EntitySchema for database mapping (not decorators on entities)
- Pagination uses `offset` and `limit` (not page-based)
- GetAll operations return `{count: number, items: T[]}` interface
- Generic service handles search (`searchOnJson`, `searchOnRelation`), sorting, date filters
- DTOs extend base `GetAllDto` for consistent query parameters
- Global validation pipeline with class-validator
- Response interceptor for consistent API responses
- Event listeners for cross-cutting concerns

## Code Standards

### TypeScript Guidelines
- Always declare types for variables and function parameters/returns
- Avoid `any` type - create specific types instead
- Use PascalCase for classes, camelCase for variables/functions, kebab-case for files
- Functions should be short (<20 lines) with single purpose
- Use JSDoc for public methods
- Prefer composition over inheritance

### NestJS Patterns
- One module per domain with modular architecture
- Controllers handle HTTP concerns, services contain business logic
- Use class-validator DTOs for input validation
- Implement repository pattern with interfaces
- Use global filters, guards, and interceptors appropriately

### Testing Standards
- Follow Arrange-Act-Assert pattern
- Write unit tests for services and controllers
- Write E2E tests for complete API flows
- Use clear naming: `inputX`, `mockX`, `actualX`, `expectedX`
- E2E tests located in `test/` directory

## Environment Setup

### Required Environment Variables
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Secret for JWT token signing
- `JWT_ACCESS_TOKEN_EXPIRATION` - Token expiration time
- `PORT` - Application port (default: 3000)
- `NODE_ENV` - Environment (development/production)
- `SWAGGER_PATH` - API documentation path
- `HOST_URL` - Base application URL

### Development Flow
1. Set up environment variables in `.env`
2. Run database migrations: `pnpm run migration:run`
3. Start development: `pnpm start:dev`
4. Access Swagger docs at `http://localhost:3000/docs`