/* Form Styles */

.form-container {
    background: white;
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    max-width: 800px;
    margin: 0 auto;
}

.form-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.form-section {
    margin-bottom: 35px;
    padding-bottom: 35px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.form-section::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 50px;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 1px;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h2 {
    color: var(--dark-color);
    margin-bottom: 25px;
    font-size: 1.6rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h2::before {
    content: '';
    width: 4px;
    height: 25px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"],
.form-group input[type="number"],
.form-group input[type="url"],
.form-group input[type="time"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    transition: var(--transition);
    background: #fafbfc;
    font-family: 'Cairo', sans-serif;
    color: var(--dark-color);
}

/* RTL text direction for Arabic inputs */
input[dir="rtl"], 
textarea[dir="rtl"] {
    text-align: right;
}

/* LTR text direction for English inputs */
input[dir="ltr"], 
textarea[dir="ltr"] {
    text-align: left;
    direction: ltr;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.form-group input:invalid {
    border-color: var(--danger-color);
}

.form-group input:valid {
    border-color: var(--success-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.875rem;
}

.form-group .error-message {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 5px;
    display: none;
}

.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: var(--danger-color);
    background-color: #fff5f5;
}

.form-group.has-error .error-message {
    display: block;
}

/* Checkbox and Radio Groups */
.checkbox-group,
.radio-group {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.checkbox-group label,
.radio-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 8px;
    transition: var(--transition);
    border: 2px solid #e1e8ed;
    background: #fafbfc;
    margin-bottom: 0;
}

.checkbox-group label:hover,
.radio-group label:hover {
    background-color: #f8f9fa;
    border-color: var(--primary-color);
}

.checkbox-group input[type="checkbox"],
.radio-group input[type="radio"] {
    margin-left: 8px;
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

.checkbox-group label.checked,
.radio-group label.checked {
    background: rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Special Form Components */
.day-hours {
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid #e1e8ed;
    transition: var(--transition);
}

.day-hours:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateX(5px);
}

.day-hours h3 {
    margin-bottom: 12px;
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.day-hours h3::before {
    content: '📅';
    font-size: 1rem;
}

.hours-inputs {
    display: flex;
    gap: 15px;
    margin-top: 12px;
    align-items: center;
}

.hours-inputs::before {
    content: '🕐';
    font-size: 1.1rem;
    color: var(--primary-color);
}

.hours-inputs input[type="time"] {
    flex: 1;
}

/* Location Toggle */
.location-toggle {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.location-toggle label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
    padding: 10px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    transition: var(--transition);
    background: #fafbfc;
}

.location-toggle label:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.location-toggle input[type="radio"] {
    margin-right: 8px;
    accent-color: var(--primary-color);
}

.location-toggle label.selected {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.location-fields {
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin-top: 15px;
    border: 1px solid #e1e8ed;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { 
        opacity: 0; 
        transform: translateY(-10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.location-fields h4 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.location-fields h4::before {
    content: '📍';
    font-size: 1.1rem;
}

/* Form Row and Columns */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.form-col {
    flex: 1;
}

.form-col-2 {
    flex: 2;
}

.form-col-3 {
    flex: 3;
}

/* Bilingual Labels */
.bilingual-label {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 8px;
}

.bilingual-label .ar {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 15px;
    text-align: right;
}

.bilingual-label .en {
    font-weight: 400;
    color: #6c757d;
    font-size: 13px;
    text-align: right;
    font-style: italic;
}

/* Features Container */
.features-container {
    max-height: 300px;
    overflow-y: auto;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    padding: 10px;
    background: #fafbfc;
}

.features-container::-webkit-scrollbar {
    width: 8px;
}

.features-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.features-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    transition: var(--transition);
    cursor: pointer;
}

.feature-item:hover {
    background: #f8f9fa;
    border-color: var(--primary-color);
    transform: translateX(3px);
}

.feature-item:last-child {
    margin-bottom: 0;
}

.feature-item input[type="checkbox"] {
    margin-left: 12px;
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

.feature-item label {
    cursor: pointer;
    flex-grow: 1;
    margin-bottom: 0;
    font-weight: normal;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e8ed;
}

/* Gallery URLs */
.gallery-url {
    margin-bottom: 10px;
}

#galleryUrlsContainer {
    margin-bottom: 10px;
}

.gallery-url-item {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.gallery-url-item input {
    flex: 1;
}

.remove-url-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.remove-url-btn:hover {
    background: #c82333;
}

/* Responsive Form Design */
@media (max-width: 768px) {
    .form-container {
        padding: 15px !important;
        margin: 0 !important;
        border-radius: 8px !important;
        max-width: 100% !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .checkbox-group,
    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .location-toggle {
        flex-direction: column;
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .hours-inputs {
        flex-direction: column;
        gap: 10px;
    }

    /* Mobile Form Elements */
    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px !important; /* Prevent zoom on iOS */
        padding: 14px 16px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .form-section {
        margin-bottom: 25px !important;
        padding-bottom: 25px !important;
    }

    .form-section h2 {
        font-size: 1.3rem !important;
        margin-bottom: 15px !important;
    }

    /* Mobile Phone Input */
    .phone-input-container {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .country-code-select {
        min-width: 100% !important;
        margin-bottom: 5px !important;
    }

    /* Mobile Search Container */
    .search-container {
        margin-bottom: 10px !important;
    }

    .search-input {
        padding: 14px 45px 14px 16px !important;
        font-size: 16px !important;
    }
}
