<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إضافة عمل تجاري - قريب بلس</title>

    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css" />
    <link rel="stylesheet" href="../../assets/css/components.css" />
    <link rel="stylesheet" href="../../assets/css/forms.css" />

    <style>
      /* Enhanced Phone Input Styling */
      .phone-input-container {
        display: flex;
        gap: 10px;
        align-items: stretch;
      }

      .country-code-select {
        min-width: 120px;
        flex-shrink: 0;
        background: #fafbfc;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 12px 8px;
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        transition: all 0.3s ease;
      }

      .country-code-select:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .phone-number-input {
        flex: 1;
        direction: ltr;
        text-align: left;
        font-family: 'Courier New', monospace;
        letter-spacing: 1px;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        background: #fafbfc;
        transition: all 0.3s ease;
      }

      .phone-input-container .phone-number-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      /* Search Input Styling */
      .search-container {
        position: relative;
        margin-bottom: 15px;
      }

      .search-input {
        width: 100%;
        padding: 12px 45px 12px 16px;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        font-size: 14px;
        background: #fafbfc;
        transition: all 0.3s ease;
      }

      .search-input:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 16px;
        pointer-events: none;
      }

      .clear-search {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        display: none;
      }

      .clear-search:hover {
        color: #667eea;
      }

      /* Enhanced Features Container */
      .features-section {
        margin-bottom: 20px;
      }

      .features-container {
        max-height: 350px;
        overflow-y: auto;
        border: 2px solid #e1e8ed;
        border-radius: 12px;
        padding: 20px;
        background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .features-container::-webkit-scrollbar {
        width: 8px;
      }

      .features-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .features-container::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 4px;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 12px;
      }

      .feature-item {
        display: flex;
        align-items: center;
        padding: 15px;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .feature-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: #667eea;
        transform: scaleY(0);
        transition: transform 0.3s ease;
      }

      .feature-item:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
      }

      .feature-item:hover::before {
        transform: scaleY(1);
      }

      .feature-item.selected {
        border-color: #667eea;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.1) 0%,
          rgba(118, 75, 162, 0.05) 100%
        );
      }

      .feature-item.selected::before {
        transform: scaleY(1);
      }

      .feature-item input[type='checkbox'] {
        margin-left: 12px;
        transform: scale(1.3);
        accent-color: #667eea;
      }

      .feature-item label {
        cursor: pointer;
        flex-grow: 1;
        margin-bottom: 0;
        font-weight: 500;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .feature-icon {
        font-size: 18px;
        min-width: 24px;
        text-align: center;
      }

      /* Enhanced Category Select */
      .category-select-container {
        position: relative;
      }

      .category-search {
        margin-bottom: 10px;
      }

      /* No results message */
      .no-results {
        text-align: center;
        padding: 30px;
        color: #666;
        font-style: italic;
        background: white;
        border-radius: 8px;
        border: 2px dashed #e1e8ed;
      }

      /* Operating Hours Styling */
      .operating-hours-container {
        display: grid;
        gap: 15px;
        margin-top: 15px;
      }

      .day-hours {
        display: grid;
        grid-template-columns: 100px 1fr 1fr auto;
        gap: 10px;
        align-items: center;
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        border: 1px solid #e1e8ed;
        transition: all 0.3s ease;
      }

      .day-hours:hover {
        transform: translateX(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .day-hours label:first-child {
        font-weight: 600;
        margin: 0;
        color: #2c3e50;
      }

      .day-hours input[type='time'] {
        padding: 10px;
        border: 2px solid #e1e8ed;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .day-hours input[type='time']:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .day-hours label:last-child {
        display: flex;
        align-items: center;
        gap: 5px;
        margin: 0;
        font-size: 0.9rem;
        font-weight: 500;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e1e8ed;
      }

      .feature-item input[type='checkbox'] {
        margin: 0;
      }

      .feature-item label {
        margin: 0;
        cursor: pointer;
        font-size: 0.9rem;
      }

      .checkbox-group {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
      }

      .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        cursor: pointer;
      }

      .checkbox-group input[type='checkbox'] {
        margin: 0;
      }

      /* Enhanced form help text styling */
      .form-help {
        display: block;
        margin-top: 5px;
        font-size: 12px;
        color: #666;
        transition: color 0.2s ease;
      }

      .form-help.success {
        color: #28a745;
      }

      .form-help.error {
        color: #dc3545;
      }

      .form-help.warning {
        color: #ffc107;
      }

      /* Mobile-specific styles */
      @media (max-width: 768px) {
        .features-grid {
          grid-template-columns: 1fr !important;
          gap: 8px !important;
        }

        .feature-item {
          padding: 12px !important;
          font-size: 14px !important;
        }

        .features-container {
          max-height: 300px !important;
          padding: 15px !important;
        }

        .day-hours {
          grid-template-columns: 1fr !important;
          gap: 8px !important;
          padding: 12px !important;
        }

        .day-hours label:first-child {
          font-size: 14px !important;
          margin-bottom: 5px !important;
        }

        .day-hours input[type='time'] {
          width: 100% !important;
          margin-bottom: 5px !important;
        }

        .bilingual-label .ar {
          font-size: 15px !important;
        }

        .bilingual-label .en {
          font-size: 13px !important;
        }

        .form-actions {
          margin-top: 30px !important;
        }

        .btn {
          padding: 16px 24px !important;
          font-size: 16px !important;
          width: 100% !important;
          margin-bottom: 10px !important;
        }
      }
    </style>
  </head>
  <body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">إضافة عمل تجاري جديد</h1>
        <p class="page-subtitle">
          أضف عملك التجاري مع جميع التفاصيل والمعلومات المطلوبة
        </p>
      </div>

      <div class="form-container">
        <form id="businessForm">
          <div class="form-section">
            <h2>🏪 المعلومات الأساسية</h2>

            <div class="form-group">
              <div class="bilingual-label">
                <span class="ar">اسم العمل التجاري (عربي)</span>
                <span class="en">Business Name (Arabic)</span>
              </div>
              <input
                type="text"
                id="nameAr"
                name="nameAr"
                required
                dir="rtl"
                placeholder="أدخل اسم العمل التجاري بالعربية"
              />
            </div>

            <div class="form-group">
              <div class="bilingual-label">
                <span class="ar">اسم العمل التجاري (إنجليزي)</span>
                <span class="en">Business Name (English)</span>
              </div>
              <input
                type="text"
                id="nameEn"
                name="nameEn"
                required
                dir="ltr"
                placeholder="Enter business name in English"
              />
            </div>

            <div class="form-group">
              <label for="primaryCategoryId">الفئة الأساسية</label>
              <div class="category-select-container">
                <div class="search-container category-search">
                  <input
                    type="text"
                    id="categorySearch"
                    class="search-input"
                    placeholder="ابحث في الفئات..."
                    dir="rtl"
                  />
                  <i class="fas fa-search search-icon"></i>
                  <button
                    type="button"
                    class="clear-search"
                    id="clearCategorySearch"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <select
                  id="primaryCategoryId"
                  name="primaryCategoryId"
                  required
                >
                  <option value="">اختر الفئة الأساسية</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="ownerUserId">مالك العمل التجاري</label>
              <select id="ownerUserId" name="ownerUserId" required>
                <option value="">اختر المالك</option>
              </select>
            </div>

            <div class="form-group">
              <label for="priceRange">نطاق الأسعار</label>
              <select id="priceRange" name="priceRange">
                <option value="">اختر نطاق الأسعار</option>
                <option value="₤">₤ - منخفض</option>
                <option value="₤₤">₤₤ - متوسط</option>
                <option value="₤₤₤">₤₤₤ - مرتفع</option>
              </select>
            </div>
          </div>

          <div class="form-section">
            <h2>📝 الأوصاف</h2>

            <div class="form-group">
              <label for="shortDescriptionAr">الوصف المختصر (عربي)</label>
              <textarea
                id="shortDescriptionAr"
                name="shortDescriptionAr"
                rows="3"
                maxlength="160"
                dir="rtl"
                placeholder="وصف مختصر عن العمل التجاري (حد أقصى 160 حرف)"
              ></textarea>
              <small>الحد الأقصى: 160 حرف</small>
            </div>

            <div class="form-group">
              <label for="shortDescriptionEn">الوصف المختصر (إنجليزي)</label>
              <textarea
                id="shortDescriptionEn"
                name="shortDescriptionEn"
                rows="3"
                maxlength="160"
                dir="ltr"
                placeholder="Brief description of the business (max 160 characters)"
              ></textarea>
              <small>Maximum: 160 characters</small>
            </div>

            <div class="form-group">
              <label for="fullDescriptionAr">الوصف الكامل (عربي)</label>
              <textarea
                id="fullDescriptionAr"
                name="fullDescriptionAr"
                rows="5"
                dir="rtl"
                placeholder="وصف تفصيلي عن العمل التجاري"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="fullDescriptionEn">الوصف الكامل (إنجليزي)</label>
              <textarea
                id="fullDescriptionEn"
                name="fullDescriptionEn"
                rows="5"
                dir="ltr"
                placeholder="Detailed description of the business"
              ></textarea>
            </div>
          </div>

          <div class="form-section">
            <h2>📞 معلومات الاتصال</h2>

            <div class="form-row">
              <div class="form-col">
                <label for="phoneNumber">رقم الهاتف</label>
                <div class="phone-input-container">
                  <select class="country-code-select" id="phoneCountryCode">
                    <option value="+20" selected>🇪🇬 +20</option>
                    <option value="+966">🇸🇦 +966</option>
                    <option value="+971">🇦🇪 +971</option>
                    <option value="+965">🇰🇼 +965</option>
                    <option value="+973">🇧🇭 +973</option>
                    <option value="+974">🇶🇦 +974</option>
                    <option value="+968">🇴🇲 +968</option>
                    <option value="+962">🇯🇴 +962</option>
                    <option value="+961">🇱🇧 +961</option>
                    </select>
                  <input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    class="phone-number-input"
                    placeholder="501234567"
                    pattern="[0-9]{9,10}"
                  />
                </div>
                <small id="phoneNumberHelp" class="form-help"
                  >أدخل رقم الهاتف بدون رمز الدولة (9-10 أرقام)</small>
              </div>

              <div class="form-col">
                <label for="whatsAppNumber">رقم الواتساب</label>
                <div class="phone-input-container">
                  <select class="country-code-select" id="whatsappCountryCode">
                    <option value="+20" selected>🇪🇬 +20</option>
                    <option value="+966">🇸🇦 +966</option>
                    <option value="+971">🇦🇪 +971</option>
                    <option value="+965">🇰🇼 +965</option>
                    <option value="+973">🇧🇭 +973</option>
                    <option value="+974">🇶🇦 +974</option>
                    <option value="+968">🇴🇲 +968</option>
                    <option value="+962">🇯🇴 +962</option>
                    <option value="+961">🇱🇧 +961</option>
                    </select>
                  <input
                    type="tel"
                    id="whatsAppNumber"
                    name="whatsAppNumber"
                    class="phone-number-input"
                    placeholder="501234567"
                    pattern="[0-9]{9,10}"
                  />
                </div>
                <small id="whatsAppNumberHelp" class="form-help"
                  >أدخل رقم الواتساب بدون رمز الدولة (9-10 أرقام)</small>
              </div>
            </div>
          </div>

          <div class="form-section">
            <h2>🖼️ الوسائط</h2>

            <div class="form-group">
              <label for="logoUrl">رابط الشعار</label>
              <input
                type="url"
                id="logoUrl"
                name="logoUrl"
                dir="ltr"
                placeholder="https://example.com/logo.png"
              />
            </div>

            <div class="form-group">
              <label for="coverPhotoUrl">رابط صورة الغلاف</label>
              <input
                type="url"
                id="coverPhotoUrl"
                name="coverPhotoUrl"
                dir="ltr"
                placeholder="https://example.com/cover.jpg"
              />
            </div>

            <div class="form-group">
              <label for="galleryUrls"
                >روابط معرض الصور (كل رابط في سطر منفصل)</label
              >
              <textarea
                id="galleryUrls"
                name="galleryUrls"
                rows="4"
                dir="ltr"
                placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"
              ></textarea>
              <small>الحد الأقصى: 10 صور</small>
            </div>
          </div>

          <div class="form-section">
            <h2>💳 طرق الدفع</h2>

            <div class="form-group">
              <label>طرق الدفع المقبولة:</label>
              <div class="checkbox-group">
                <label>
                  <input type="checkbox" name="paymentMethods" value="cash" />
                  نقدي
                </label>
                <label>
                  <input type="checkbox" name="paymentMethods" value="card" />
                  بطاقة ائتمانية
                </label>
                <label>
                  <input
                    type="checkbox"
                    name="paymentMethods"
                    value="mobile_wallet"
                  />
                  محفظة إلكترونية
                </label>
              </div>
            </div>
          </div>

          <div class="form-section">
            <h2>⚙️ الإعدادات</h2>

            <div class="form-group">
              <div class="checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked
                  />
                  عمل تجاري نشط
                </label>
                <label>
                  <input type="checkbox" id="isVerified" name="isVerified" />
                  عمل تجاري موثق
                </label>
                <label>
                  <input type="checkbox" id="isPremium" name="isPremium" />
                  عمل تجاري مميز
                </label>
              </div>
              <small
                >الأعمال غير النشطة لن تظهر في نتائج البحث. الأعمال الموثقة تحصل
                على علامة التوثيق. الأعمال المميزة تظهر في المقدمة.</small
              >
            </div>
          </div>

          <div class="form-section">
            <h2>📍 الموقع</h2>

            <div class="location-toggle">
              <label>
                <input
                  type="radio"
                  name="locationType"
                  value="existing"
                  checked
                />
                اختيار موقع موجود
              </label>
              <label>
                <input type="radio" name="locationType" value="new" />
                إضافة موقع جديد
              </label>
            </div>

            <div id="existingLocationFields">
              <div class="form-group">
                <label for="locationId">الموقع</label>
                <select id="locationId" name="locationId">
                  <option value="">اختر الموقع</option>
                </select>
              </div>
            </div>

            <div
              id="newLocationFields"
              class="location-fields"
              style="display: none"
            >
              <h4>إضافة موقع جديد</h4>

              <div class="form-row">
                <div class="form-col">
                  <label for="newLocationNameAr">اسم الموقع (عربي) *</label>
                  <input
                    type="text"
                    id="newLocationNameAr"
                    name="newLocationNameAr"
                    dir="rtl"
                    placeholder="أدخل اسم الموقع بالعربية"
                  />
                </div>

                <div class="form-col">
                  <label for="newLocationNameEn">اسم الموقع (إنجليزي) *</label>
                  <input
                    type="text"
                    id="newLocationNameEn"
                    name="newLocationNameEn"
                    dir="ltr"
                    placeholder="Enter location name in English"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="newLocationMapUrl"
                  >رابط الخريطة (Google Maps)</label
                >
                <input
                  type="url"
                  id="newLocationMapUrl"
                  name="newLocationMapUrl"
                  dir="ltr"
                  placeholder="https://maps.google.com/..."
                />
                <small
                  >سيتم استخراج خط العرض والطول تلقائياً من رابط الخريطة</small
                >
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="newLocationLatitude">خط العرض *</label>
                  <input
                    type="number"
                    id="newLocationLatitude"
                    name="newLocationLatitude"
                    step="any"
                    dir="ltr"
                    readonly
                  />
                </div>

                <div class="form-col">
                  <label for="newLocationLongitude">خط الطول *</label>
                  <input
                    type="number"
                    id="newLocationLongitude"
                    name="newLocationLongitude"
                    step="any"
                    dir="ltr"
                    readonly
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="newLocationStreetAddressAr"
                    >عنوان الشارع (عربي)</label
                  >
                  <input
                    type="text"
                    id="newLocationStreetAddressAr"
                    name="newLocationStreetAddressAr"
                    dir="rtl"
                  />
                </div>

                <div class="form-col">
                  <label for="newLocationStreetAddressEn"
                    >عنوان الشارع (إنجليزي)</label
                  >
                  <input
                    type="text"
                    id="newLocationStreetAddressEn"
                    name="newLocationStreetAddressEn"
                    dir="ltr"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="newLocationCityAr">المدينة (عربي)</label>
                  <input
                    type="text"
                    id="newLocationCityAr"
                    name="newLocationCityAr"
                    dir="rtl"
                  />
                </div>

                <div class="form-col">
                  <label for="newLocationCityEn">المدينة (إنجليزي)</label>
                  <input
                    type="text"
                    id="newLocationCityEn"
                    name="newLocationCityEn"
                    dir="ltr"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="newLocationStateAr">المنطقة/الولاية (عربي)</label>
                  <input
                    type="text"
                    id="newLocationStateAr"
                    name="newLocationStateAr"
                    dir="rtl"
                  />
                </div>

                <div class="form-col">
                  <label for="newLocationStateEn"
                    >المنطقة/الولاية (إنجليزي)</label
                  >
                  <input
                    type="text"
                    id="newLocationStateEn"
                    name="newLocationStateEn"
                    dir="ltr"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="newLocationCountryAr">البلد (عربي)</label>
                  <input
                    type="text"
                    id="newLocationCountryAr"
                    name="newLocationCountryAr"
                    dir="rtl"
                  />
                </div>

                <div class="form-col">
                  <label for="newLocationCountryEn">البلد (إنجليزي)</label>
                  <input
                    type="text"
                    id="newLocationCountryEn"
                    name="newLocationCountryEn"
                    dir="ltr"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="newLocationPostalCode">الرمز البريدي</label>
                <input
                  type="text"
                  id="newLocationPostalCode"
                  name="newLocationPostalCode"
                  dir="ltr"
                />
              </div>
            </div>
          </div>

          <div class="form-section">
            <h2>⭐ الميزات</h2>
            <div class="form-group features-section">
              <label>اختر الميزات المتوفرة:</label>
              <div class="search-container">
                <input
                  type="text"
                  id="featuresSearch"
                  class="search-input"
                  placeholder="ابحث في الميزات..."
                  dir="rtl"
                />
                <i class="fas fa-search search-icon"></i>
                <button
                  type="button"
                  class="clear-search"
                  id="clearFeaturesSearch"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="features-container">
                <div id="featuresGrid" class="features-grid">
                  <!-- Features will be loaded here -->
                </div>
                <div
                  id="noFeaturesResults"
                  class="no-results"
                  style="display: none"
                >
                  <i
                    class="fas fa-search"
                    style="font-size: 2rem; margin-bottom: 10px; color: #ccc"
                  ></i>
                  <p>لم يتم العثور على ميزات تطابق البحث</p>
                </div>
              </div>
            </div>
          </div>

          <div class="form-section">
            <h2>🕒 ساعات العمل</h2>

            <div class="form-group">
              <label>
                <input type="checkbox" id="isOpen24x7" name="isOpen24x7" />
                مفتوح 24/7
              </label>
            </div>

            <div id="operatingHoursContainer" class="operating-hours-container">
              <div class="day-hours">
                <label>الأحد:</label>
                <input type="time" name="sunday_open" placeholder="فتح" />
                <input type="time" name="sunday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="sunday_closed" /> مغلق</label
                >
              </div>
              <div class="day-hours">
                <label>الاثنين:</label>
                <input type="time" name="monday_open" placeholder="فتح" />
                <input type="time" name="monday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="monday_closed" /> مغلق</label
                >
              </div>
              <div class="day-hours">
                <label>الثلاثاء:</label>
                <input type="time" name="tuesday_open" placeholder="فتح" />
                <input type="time" name="tuesday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="tuesday_closed" /> مغلق</label
                >
              </div>
              <div class="day-hours">
                <label>الأربعاء:</label>
                <input type="time" name="wednesday_open" placeholder="فتح" />
                <input type="time" name="wednesday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="wednesday_closed" /> مغلق</label
                >
              </div>
              <div class="day-hours">
                <label>الخميس:</label>
                <input type="time" name="thursday_open" placeholder="فتح" />
                <input type="time" name="thursday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="thursday_closed" /> مغلق</label
                >
              </div>
              <div class="day-hours">
                <label>الجمعة:</label>
                <input type="time" name="friday_open" placeholder="فتح" />
                <input type="time" name="friday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="friday_closed" /> مغلق</label
                >
              </div>
              <div class="day-hours">
                <label>السبت:</label>
                <input type="time" name="saturday_open" placeholder="فتح" />
                <input type="time" name="saturday_close" placeholder="إغلاق" />
                <label
                  ><input type="checkbox" name="saturday_closed" /> مغلق</label
                >
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary" id="submitBtn">
              <span id="submitBtnText">إنشاء العمل التجاري</span>
              <div
                class="loading-spinner"
                id="submitSpinner"
                style="display: none"
              ></div>
            </button>
            <a href="list.html" class="btn btn-secondary">إلغاء</a>
          </div>

          <div class="response-message" id="responseMessage"></div>
        </form>
      </div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script>
      let categories = [];
      let locations = [];
      let features = [];
      let users = [];

      document.addEventListener('DOMContentLoaded', function () {
        // Check authentication
        if (!AuthUtils.isAuthenticated()) {
          window.location.href = '../auth/login.html';
          return;
        }

        loadCategories();
        loadLocations();
        loadFeatures();
        loadUsers();
        setupEventListeners();
        setupCategorySearch();
        setupFeaturesSearch();
        setupPhoneNumberValidation();
      });

      function setupEventListeners() {
        const form = document.getElementById('businessForm');
        const locationTypeRadios = document.querySelectorAll(
          'input[name="locationType"]',
        );
        const mapUrlInput = document.getElementById('newLocationMapUrl');
        const isOpen24x7 = document.getElementById('isOpen24x7');

        form.addEventListener('submit', handleSubmit);

        locationTypeRadios.forEach((radio) => {
          radio.addEventListener('change', toggleLocationFields);
        });

        if (mapUrlInput) {
          mapUrlInput.addEventListener('input', extractCoordinatesFromMapUrl);
        }

        if (isOpen24x7) {
          isOpen24x7.addEventListener('change', toggleOperatingHours);
        }
      }

      function toggleLocationFields() {
        const locationType = document.querySelector(
          'input[name="locationType"]:checked',
        ).value;
        const existingFields = document.getElementById(
          'existingLocationFields',
        );
        const newFields = document.getElementById('newLocationFields');

        if (locationType === 'existing') {
          existingFields.style.display = 'block';
          newFields.style.display = 'none';
        } else {
          existingFields.style.display = 'none';
          newFields.style.display = 'block';
        }
      }

      function extractCoordinatesFromMapUrl() {
        const mapUrl = document.getElementById('newLocationMapUrl').value;
        const latInput = document.getElementById('newLocationLatitude');
        const lngInput = document.getElementById('newLocationLongitude');

        if (!mapUrl) {
          latInput.value = '';
          lngInput.value = '';
          return;
        }

        // Extract coordinates from Google Maps URL
        // Format: https://maps.google.com/?q=24.7136,46.6753
        // Format: https://www.google.com/maps/@24.7136,46.6753,15z
        // Format: https://maps.google.com/maps?q=24.7136,46.6753

        let lat = null,
          lng = null;

        // Try different URL patterns
        const patterns = [
          /[?&]q=(-?\d+\.?\d*),(-?\d+\.?\d*)/,
          /@(-?\d+\.?\d*),(-?\d+\.?\d*)/,
          /ll=(-?\d+\.?\d*),(-?\d+\.?\d*)/,
          /center=(-?\d+\.?\d*),(-?\d+\.?\d*)/,
        ];

        for (const pattern of patterns) {
          const match = mapUrl.match(pattern);
          if (match) {
            lat = parseFloat(match[1]);
            lng = parseFloat(match[2]);
            break;
          }
        }

        if (lat !== null && lng !== null) {
          latInput.value = lat;
          lngInput.value = lng;
        }
      }

      function toggleOperatingHours() {
        const isOpen24x7 = document.getElementById('isOpen24x7').checked;
        const container = document.getElementById('operatingHoursContainer');

        if (isOpen24x7) {
          container.style.display = 'none';
        } else {
          container.style.display = 'block';
        }
      }

      async function loadCategories() {
        try {
          // Use large limit to fetch all categories for business form
          const response = await fetch('/v1/categories?limit=1000&offset=0', {
            headers: AuthUtils.getAuthHeaders(),
          });
          if (response.ok) {
            const result = await response.json();
            // Handle different response formats
            if (result.data) {
              categories = result.data.items || [];
            } else {
              categories = result.items || result;
            }
            populateCategories();
          }
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      function populateCategories() {
        const select = document.getElementById('primaryCategoryId');
        // Clear existing options except the first one
        while (select.children.length > 1) {
          select.removeChild(select.lastChild);
        }

        categories.forEach((category) => {
          const option = document.createElement('option');
          option.value = category.id;
          option.textContent = `${category.icon || '📂'} ${category.nameAr}`;
          option.dataset.nameAr = category.nameAr.toLowerCase();
          option.dataset.nameEn = (category.nameEn || '').toLowerCase();
          select.appendChild(option);
        });
      }

      function setupCategorySearch() {
        const searchInput = document.getElementById('categorySearch');
        const clearBtn = document.getElementById('clearCategorySearch');
        const select = document.getElementById('primaryCategoryId');

        searchInput.addEventListener('input', function () {
          const searchTerm = this.value.toLowerCase().trim();
          clearBtn.style.display = searchTerm ? 'block' : 'none';

          Array.from(select.options).forEach((option, index) => {
            if (index === 0) return; // Skip the first "choose" option

            const nameAr = option.dataset.nameAr || '';
            const nameEn = option.dataset.nameEn || '';
            const matches =
              nameAr.includes(searchTerm) || nameEn.includes(searchTerm);
            option.style.display = matches ? 'block' : 'none';
          });
        });

        clearBtn.addEventListener('click', function () {
          searchInput.value = '';
          this.style.display = 'none';
          Array.from(select.options).forEach((option) => {
            option.style.display = 'block';
          });
          searchInput.focus();
        });
      }

      async function loadLocations() {
        try {
          const response = await fetch('/v1/locations', {
            headers: AuthUtils.getAuthHeaders(),
          });
          if (response.ok) {
            const result = await response.json();
            locations = result.data?.items || result.data || result;
            populateLocations();
          }
        } catch (error) {
          console.error('Error loading locations:', error);
        }
      }

      function populateLocations() {
        const select = document.getElementById('locationId');
        locations.forEach((location) => {
          const option = document.createElement('option');
          option.value = location.id;
          option.textContent = location.nameAr;
          select.appendChild(option);
        });
      }

      async function loadFeatures() {
        try {
          // Use large limit to fetch all features for business form
          const response = await fetch('/v1/features?limit=1000&offset=0', {
            headers: AuthUtils.getAuthHeaders(),
          });
          if (response.ok) {
            const result = await response.json();
            // Handle different response formats
            if (result.data) {
              features = result.data.items || [];
            } else {
              features = result.items || result;
            }
            populateFeatures();
          }
        } catch (error) {
          console.error('Error loading features:', error);
        }
      }

      function populateFeatures() {
        const container = document.getElementById('featuresGrid');
        container.innerHTML = ''; // Clear existing features

        features.forEach((feature) => {
          const featureItem = document.createElement('div');
          featureItem.className = 'feature-item';
          featureItem.dataset.nameAr = feature.nameAr.toLowerCase();
          featureItem.dataset.nameEn = (feature.nameEn || '').toLowerCase();

          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.id = `feature_${feature.id}`;
          checkbox.name = 'features';
          checkbox.value = feature.id;

          const label = document.createElement('label');
          label.htmlFor = `feature_${feature.id}`;
          label.innerHTML = `
                    <span class="feature-icon">${feature.icon || '⭐'}</span>
                    <span>${feature.nameAr}</span>
                `;

          featureItem.appendChild(checkbox);
          featureItem.appendChild(label);

          // Add click handler for the entire item
          featureItem.addEventListener('click', function (e) {
            if (e.target !== checkbox) {
              checkbox.checked = !checkbox.checked;
              updateFeatureItemState(featureItem, checkbox.checked);
            }
          });

          // Add change handler for checkbox
          checkbox.addEventListener('change', function () {
            updateFeatureItemState(featureItem, this.checked);
          });

          container.appendChild(featureItem);
        });
      }

      function updateFeatureItemState(featureItem, isSelected) {
        if (isSelected) {
          featureItem.classList.add('selected');
        } else {
          featureItem.classList.remove('selected');
        }
      }

      function setupFeaturesSearch() {
        const searchInput = document.getElementById('featuresSearch');
        const clearBtn = document.getElementById('clearFeaturesSearch');
        const featuresGrid = document.getElementById('featuresGrid');
        const noResults = document.getElementById('noFeaturesResults');

        searchInput.addEventListener('input', function () {
          const searchTerm = this.value.toLowerCase().trim();
          clearBtn.style.display = searchTerm ? 'block' : 'none';

          let visibleCount = 0;
          const featureItems = featuresGrid.querySelectorAll('.feature-item');

          featureItems.forEach((item) => {
            const nameAr = item.dataset.nameAr || '';
            const nameEn = item.dataset.nameEn || '';
            const matches =
              nameAr.includes(searchTerm) || nameEn.includes(searchTerm);

            if (matches) {
              item.style.display = 'flex';
              visibleCount++;
            } else {
              item.style.display = 'none';
            }
          });

          // Show/hide no results message
          if (visibleCount === 0 && searchTerm) {
            noResults.style.display = 'block';
            featuresGrid.style.display = 'none';
          } else {
            noResults.style.display = 'none';
            featuresGrid.style.display = 'grid';
          }
        });

        clearBtn.addEventListener('click', function () {
          searchInput.value = '';
          this.style.display = 'none';

          const featureItems = featuresGrid.querySelectorAll('.feature-item');
          featureItems.forEach((item) => {
            item.style.display = 'flex';
          });

          noResults.style.display = 'none';
          featuresGrid.style.display = 'grid';
          searchInput.focus();
        });
      }

      async function loadUsers() {
        try {
          const response = await fetch('/v1/users', {
            headers: AuthUtils.getAuthHeaders(),
          });
          if (response.ok) {
            const result = await response.json();
            users = result.data?.items || result.data || result;
            populateUsers();
          }
        } catch (error) {
          console.error('Error loading users:', error);
        }
      }

      function populateUsers() {
        const select = document.getElementById('ownerUserId');
        users.forEach((user) => {
          const option = document.createElement('option');
          option.value = user.id;
          option.textContent = `${user.firstName} ${user.lastName} (${user.email})`;
          select.appendChild(option);
        });
      }

      async function handleSubmit(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('submitBtn');
        const submitBtnText = document.getElementById('submitBtnText');
        const submitSpinner = document.getElementById('submitSpinner');

        // Show loading state
        submitBtn.disabled = true;
        submitBtnText.style.display = 'none';
        submitSpinner.style.display = 'inline-block';

        try {
          const formData = new FormData(e.target);

          // Handle phone numbers with country codes
          const phoneCountryCode =
            document.getElementById('phoneCountryCode').value;
          const phoneNumber = formData.get('phoneNumber');
          const whatsappCountryCode = document.getElementById(
            'whatsappCountryCode',
          ).value;
          const whatsAppNumber = formData.get('whatsAppNumber');

          const businessData = {
            nameAr: formData.get('nameAr'),
            nameEn: formData.get('nameEn'),
            primaryCategoryId: parseInt(formData.get('primaryCategoryId')),
            ownerUserId: parseInt(formData.get('ownerUserId')),
            shortDescriptionAr: formData.get('shortDescriptionAr'),
            shortDescriptionEn: formData.get('shortDescriptionEn'),
            fullDescriptionAr: formData.get('fullDescriptionAr'),
            fullDescriptionEn: formData.get('fullDescriptionEn'),
            phoneNumber: phoneNumber
              ? `${phoneCountryCode}${phoneNumber}`
              : undefined,
            whatsAppNumber: whatsAppNumber
              ? `${whatsappCountryCode}${whatsAppNumber}`
              : undefined,
            priceRange: formData.get('priceRange') || undefined,
            logoUrl: formData.get('logoUrl') || undefined,
            coverPhotoUrl: formData.get('coverPhotoUrl') || undefined,
            isOpen24x7: formData.get('isOpen24x7') === 'on',
            isActive: formData.get('isActive') === 'on',
            isVerified: formData.get('isVerified') === 'on',
            isPremium: formData.get('isPremium') === 'on',
            featureIds: Array.from(
              document.querySelectorAll('input[name="features"]:checked'),
            ).map((cb) => parseInt(cb.value)),
            paymentMethods: Array.from(
              document.querySelectorAll('input[name="paymentMethods"]:checked'),
            ).map((cb) => cb.value),
          };

          // Handle gallery URLs
          const galleryUrls = formData.get('galleryUrls');
          if (galleryUrls) {
            businessData.galleryUrls = galleryUrls
              .split('\n')
              .filter((url) => url.trim())
              .slice(0, 10);
          }

          // Handle operating hours
          if (!businessData.isOpen24x7) {
            const operatingHours = {};
            const days = [
              'sunday',
              'monday',
              'tuesday',
              'wednesday',
              'thursday',
              'friday',
              'saturday',
            ];

            days.forEach((day) => {
              const isClosed = formData.get(`${day}_closed`) === 'on';
              const openTime = formData.get(`${day}_open`);
              const closeTime = formData.get(`${day}_close`);

              if (isClosed) {
                operatingHours[day] = { isClosed: true };
              } else if (openTime && closeTime) {
                operatingHours[day] = {
                  open: openTime,
                  close: closeTime,
                  isClosed: false,
                };
              }
            });

            if (Object.keys(operatingHours).length > 0) {
              businessData.operatingHours = operatingHours;
            }
          }

          // Handle location
          const locationType = document.querySelector(
            'input[name="locationType"]:checked',
          ).value;
          if (locationType === 'existing') {
            const locationId = formData.get('locationId');
            if (locationId) {
              businessData.locationId = parseInt(locationId);
            }
          } else {
            // Create new location
            const newLocation = {
              nameAr: formData.get('newLocationNameAr'),
              nameEn: formData.get('newLocationNameEn'),
              latitude: parseFloat(formData.get('newLocationLatitude')),
              longitude: parseFloat(formData.get('newLocationLongitude')),
              streetAddressAr: formData.get('newLocationStreetAddressAr'),
              streetAddressEn: formData.get('newLocationStreetAddressEn'),
              cityAr: formData.get('newLocationCityAr'),
              cityEn: formData.get('newLocationCityEn'),
              stateAr: formData.get('newLocationStateAr'),
              stateEn: formData.get('newLocationStateEn'),
              countryAr: formData.get('newLocationCountryAr'),
              countryEn: formData.get('newLocationCountryEn'),
              postalCode: formData.get('newLocationPostalCode'),
              mapUrl: formData.get('newLocationMapUrl'),
            };

            // Remove empty fields
            Object.keys(newLocation).forEach((key) => {
              if (!newLocation[key] && newLocation[key] !== 0) {
                delete newLocation[key];
              }
            });

            if (
              newLocation.nameAr &&
              newLocation.nameEn &&
              newLocation.latitude &&
              newLocation.longitude
            ) {
              businessData.location = newLocation;
            }
          }

          const response = await fetch('/v1/businesses', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...AuthUtils.getAuthHeaders(),
            },
            body: JSON.stringify(businessData),
          });

          const result = await response.json();

          if (response.ok && result.success) {
            showMessage('تم إنشاء العمل التجاري بنجاح!', 'success');
            setTimeout(() => {
              window.location.href = 'list.html';
            }, 2000);
          } else {
            showMessage(
              result.message || 'فشل في إنشاء العمل التجاري',
              'error',
            );
          }
        } catch (error) {
          console.error('Error creating business:', error);
          showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
        } finally {
          // Reset loading state
          submitBtn.disabled = false;
          submitBtnText.style.display = 'inline';
          submitSpinner.style.display = 'none';
        }
      }

      function showMessage(message, type) {
        const responseMessage = document.getElementById('responseMessage');
        responseMessage.textContent = message;
        responseMessage.className = `response-message ${type}`;
        responseMessage.style.display = 'block';

        if (type === 'success') {
          setTimeout(() => {
            responseMessage.style.display = 'none';
          }, 5000);
        }
      }

      function setupPhoneNumberValidation() {
        const phoneInput = document.getElementById('phoneNumber');
        const whatsappInput = document.getElementById('whatsAppNumber');

        [phoneInput, whatsappInput].forEach((input) => {
          // Store the original value to prevent unnecessary processing
          let lastValue = input.value;
          input.addEventListener('input', function (e) {
            // Only process if the value actually changed and it's a user input
            if (this.value === lastValue) return;

            // Store the cursor position
            const cursorPosition = this.selectionStart;
            const originalLength = this.value.length;
            // Remove any non-digit characters
            const cleanValue = this.value.replace(/\D/g, '');

            // Only update if the cleaned value is different
            if (this.value !== cleanValue) {
              this.value = cleanValue;

              // Restore cursor position accounting for removed characters
              const removedChars = originalLength - cleanValue.length;
              const newCursorPosition = Math.max(
                0,
                cursorPosition - removedChars,
              );
              this.setSelectionRange(newCursorPosition, newCursorPosition);
            }

            lastValue = this.value;

            // Add visual feedback for valid/invalid numbers
            validatePhoneNumberVisually(this);
          });

          input.addEventListener('blur', function () {
            validatePhoneNumberVisually(this);
          });

          // Initial validation for pre-populated values
          validatePhoneNumberVisually(input);
        });
      }

      function validatePhoneNumberVisually(input) {
        const value = input.value;
        const length = value.length;

        // Get the corresponding help text element
        const helpTextId =
          input.id === 'phoneNumber' ? 'phoneNumberHelp' : 'whatsAppNumberHelp';
        const helpText = document.getElementById(helpTextId);

        if (length === 0) {
          // Empty field - neutral state
          input.style.borderColor = '#e1e8ed';
          input.style.backgroundColor = '#fafbfc';
          input.style.boxShadow = '';
          if (helpText) {
            helpText.className = 'form-help';
            helpText.textContent =
              input.id === 'phoneNumber'
                ? 'أدخل رقم الهاتف بدون رمز الدولة (9-10 أرقام)'
                : 'أدخل رقم الواتساب بدون رمز الدولة (9-10 أرقام)';
          }
        } else if (length >= 9 && length <= 10) {
          // Valid length - success state
          input.style.borderColor = '#28a745';
          input.style.backgroundColor = '#f8fff9';
          input.style.boxShadow = '0 0 0 2px rgba(40, 167, 69, 0.1)';
          if (helpText) {
            helpText.className = 'form-help success';
            helpText.textContent = `✓ رقم صحيح (${length} أرقام)`;
          }
        } else if (length < 9) {
          // Too short - error state
          input.style.borderColor = '#dc3545';
          input.style.backgroundColor = '#fff5f5';
          input.style.boxShadow = '0 0 0 2px rgba(220, 53, 69, 0.1)';
          if (helpText) {
            helpText.className = 'form-help error';
            helpText.textContent = `✗ قصير جداً (${length} من 9-10 أرقام مطلوبة)`;
          }
        } else {
          // Too long - error state
          input.style.borderColor = '#dc3545';
          input.style.backgroundColor = '#fff5f5';
          input.style.boxShadow = '0 0 0 2px rgba(220, 53, 69, 0.1)';
          if (helpText) {
            helpText.className = 'form-help error';
            helpText.textContent = `✗ طويل جداً (${length} من 9-10 أرقام مطلوبة)`;
          }
        }

          // Add a small transition for smooth color changes
        input.style.transition = 'all 0.2s ease';
      }
    </script>
  </body>
</html>
