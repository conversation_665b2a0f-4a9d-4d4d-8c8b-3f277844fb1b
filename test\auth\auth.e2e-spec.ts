import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { AppModule } from '../../src/app.module';
import { AuthHelper } from '../helpers/auth.helper';

describe('Auth Controller (e2e)', () => {
  let app: INestApplication<App>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/auth/login (POST)', () => {
    it('should login with test admin user', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          loginId: AuthHelper.TEST_ADMIN.email,
          password: AuthHelper.TEST_ADMIN.password,
        })
        .expect(201);

      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('tokenType');
      expect(response.body.tokenType).toBe('Bearer');
    });

    it('should login with test regular user', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          loginId: AuthHelper.TEST_USER.email,
          password: AuthHelper.TEST_USER.password,
        })
        .expect(201);

      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('tokenType');
    });

    it('should fail with invalid credentials', async () => {
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          loginId: AuthHelper.TEST_ADMIN.email,
          password: 'wrong-password',
        })
        .expect(401);
    });
  });

  describe('/auth/me (GET)', () => {
    it('should get current user profile', async () => {
      const { accessToken } = await AuthHelper.login(app);

      const response = await request(app.getHttpServer())
        .get('/auth/me')
        .set(AuthHelper.getAuthHeader(accessToken))
        .expect(200);

      expect(response.body.data.email).toBe(AuthHelper.TEST_ADMIN.email);
      expect(response.body.data.firstName).toBe(
        AuthHelper.TEST_ADMIN.firstName,
      );
      expect(response.body.data.lastName).toBe(AuthHelper.TEST_ADMIN.lastName);
    });

    it('should fail without authentication', async () => {
      await request(app.getHttpServer()).get('/auth/me').expect(401);
    });
  });

  describe('/auth/refresh (POST)', () => {
    it('should refresh access token', async () => {
      const { refreshToken } = await AuthHelper.login(app);

      const response = await request(app.getHttpServer())
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');
    });

    it('should fail with invalid refresh token', async () => {
      await request(app.getHttpServer())
        .post('/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);
    });
  });
});
