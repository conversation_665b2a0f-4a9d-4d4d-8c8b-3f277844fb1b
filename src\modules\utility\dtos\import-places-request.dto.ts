import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  Is<PERSON><PERSON><PERSON>,
  IsLongitude,
  IsN<PERSON>ber,
  IsOptional,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

export class ImportPlacesDto {
  @ApiProperty({
    description: 'Latitude coordinate for the center of the search area',
    example: 24.7136,
    minimum: -90,
    maximum: 90,
  })
  @IsLatitude()
  @Type(() => Number)
  latitude: number;

  @ApiProperty({
    description: 'Longitude coordinate for the center of the search area',
    example: 46.6753,
    minimum: -180,
    maximum: 180,
  })
  @IsLongitude()
  @Type(() => Number)
  longitude: number;

  @ApiProperty({
    description: 'Search radius in kilometers',
    example: 5,
    minimum: 0.1,
    maximum: 50,
  })
  @IsNumber()
  @Min(0.1)
  @Max(50)
  @Type(() => Number)
  radius: number;

  @ApiProperty({
    description: 'Optional array of category IDs to import. If not provided, all categories will be processed',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  categoryIds?: number[];
}
