import { IGenericService } from '@app/common';
import { CreateLocationDto } from '../../dtos/create-location.dto';
import { GetAllLocationDto } from '../../dtos/get-all-location.dto';
import { UpdateLocationDto } from '../../dtos/update-location.dto';
import { Location } from '../../entities/location.entity';
import { LocationSchema } from '../../schemes/location.schema';

export interface ILocationService
  extends IGenericService<
    Location,
    typeof LocationSchema,
    GetAllLocationDto,
    CreateLocationDto,
    UpdateLocationDto
  > {
  findByCoordinates(
    latitude: number,
    longitude: number,
    radiusKm?: number,
  ): Promise<Location[]>;
  findWithinBounds(
    minLatitude: number,
    maxLatitude: number,
    minLongitude: number,
    maxLongitude: number,
  ): Promise<Location[]>;
  findByCity(cityEn?: string, cityAr?: string): Promise<Location[]>;
  findByCountry(countryEn?: string, countryAr?: string): Promise<Location[]>;
  findByParentId(parentId: number | null): Promise<Location[]>;
  findWithChildren(id: number): Promise<Location | null>;
  findNearest(
    latitude: number,
    longitude: number,
    limit?: number,
  ): Promise<Location[]>;
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number;
  validateCoordinates(latitude: number, longitude: number): boolean;
}

export const ILocationService = Symbol('ILocationService');
