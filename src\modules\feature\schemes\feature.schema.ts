import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '@app/infrastructure/database/schemas/base.schema';
import { Feature } from '../entities/feature.entity';
import { Business } from '../../business';

export const FeatureSchema = new EntitySchema<Feature>({
  name: Feature.name,
  target: Feature,
  tableName: 'features',
  columns: {
    ...BaseSchemaProperties,
    nameEn: {
      type: String,
      nullable: false,
    },
    nameAr: {
      type: String,
      nullable: false,
    },
    icon: {
      type: String,
      nullable: false,
    },
  },
  relations: {
    businesses: {
      target: 'Business',
      type: 'many-to-many',
      inverseSide: 'features',
    },
  },
  indices: [
    {
      name: 'features_name_en_unique',
      columns: ['nameEn'],
      unique: true,
    },
    {
      name: 'features_name_ar_unique',
      columns: ['nameAr'],
      unique: true,
    },
  ],
});
