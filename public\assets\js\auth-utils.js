// Authentication utility functions for token management

const AuthUtils = {
    // Storage keys
    STORAGE_KEYS: {
        ACCESS_TOKEN: 'qareeb_access_token',
        REFRESH_TOKEN: 'qareeb_refresh_token',
        TOKEN_EXPIRY: 'qareeb_token_expiry',
        USER_DATA: 'qareeb_user_data',
        REMEMBER_ME: 'qareeb_remember_me'
    },

    // Token refresh threshold (5 minutes before expiry)
    REFRESH_THRESHOLD: 5 * 60 * 1000,

    // Auto refresh interval handle
    refreshInterval: null,

    // Save tokens to storage
    saveTokens(accessToken, refreshToken, expiresIn, rememberMe = true) {
        const storage = rememberMe ? localStorage : sessionStorage;
        const expiryTime = Date.now() + (expiresIn * 1000);

        storage.setItem(this.STORAGE_KEYS.ACCESS_TOKEN, accessToken);
        storage.setItem(this.STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        storage.setItem(this.STORAGE_KEYS.TOKEN_EXPIRY, expiryTime);
        storage.setItem(this.STORAGE_KEYS.REMEMBER_ME, rememberMe);

        // Decode and save user data from token
        try {
            const userData = this.decodeToken(accessToken);
            if (userData) {
                storage.setItem(this.STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
            }
        } catch (error) {
            console.error('Error decoding token:', error);
        }
    },

    // Alias for backward compatibility
    setTokens(accessToken, refreshToken, expiresIn = 3600, rememberMe = true) {
        this.saveTokens(accessToken, refreshToken, expiresIn, rememberMe);
    },

    // Get authorization headers for API requests
    getAuthHeaders() {
        const accessToken = this.getAccessToken();
        if (!accessToken) {
            return {};
        }
        return {
            'Authorization': `Bearer ${accessToken}`
        };
    },

    // Get access token
    getAccessToken() {
        const rememberMe = localStorage.getItem(this.STORAGE_KEYS.REMEMBER_ME) === 'true';
        const storage = rememberMe ? localStorage : sessionStorage;
        return storage.getItem(this.STORAGE_KEYS.ACCESS_TOKEN);
    },

    // Get refresh token
    getRefreshToken() {
        const rememberMe = localStorage.getItem(this.STORAGE_KEYS.REMEMBER_ME) === 'true';
        const storage = rememberMe ? localStorage : sessionStorage;
        return storage.getItem(this.STORAGE_KEYS.REFRESH_TOKEN);
    },

    // Get user data
    getUserData() {
        const rememberMe = localStorage.getItem(this.STORAGE_KEYS.REMEMBER_ME) === 'true';
        const storage = rememberMe ? localStorage : sessionStorage;
        const userData = storage.getItem(this.STORAGE_KEYS.USER_DATA);
        return userData ? JSON.parse(userData) : null;
    },

    // Check if tokens are valid
    isAuthenticated() {
        const accessToken = this.getAccessToken();
        const refreshToken = this.getRefreshToken();
        
        if (!accessToken || !refreshToken) {
            return false;
        }

        // Check if refresh token is still valid
        if (this.isTokenExpired(refreshToken)) {
            this.clearTokens();
            return false;
        }

        return true;
    },

    // Check if access token needs refresh
    needsRefresh() {
        const rememberMe = localStorage.getItem(this.STORAGE_KEYS.REMEMBER_ME) === 'true';
        const storage = rememberMe ? localStorage : sessionStorage;
        const expiryTime = parseInt(storage.getItem(this.STORAGE_KEYS.TOKEN_EXPIRY));
        
        if (!expiryTime) return true;
        
        const timeUntilExpiry = expiryTime - Date.now();
        return timeUntilExpiry <= this.REFRESH_THRESHOLD;
    },

    // Clear all tokens
    clearTokens() {
        // Clear from both storages to be safe
        Object.values(this.STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });

        // Clear refresh interval
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    },

    // Decode JWT token
    decodeToken(token) {
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            return JSON.parse(jsonPayload);
        } catch (error) {
            console.error('Error decoding token:', error);
            return null;
        }
    },

    // Check if token is expired
    isTokenExpired(token) {
        const decoded = this.decodeToken(token);
        if (!decoded || !decoded.exp) return true;
        
        const expiryTime = decoded.exp * 1000; // Convert to milliseconds
        return Date.now() > expiryTime;
    },

    // Refresh access token
    async refreshAccessToken() {
        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            throw new Error('No refresh token available');
        }

        try {
            const response = await fetch('/v1/auth/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ refreshToken })
            });

            if (!response.ok) {
                throw new Error('Failed to refresh token');
            }

            const result = await response.json();

            // Check for success using the new API response structure
            const isSuccess = (response.ok || response.status === 201) &&
                             (result.statusIndicator === "SUCCESS" || result.success);

            if (isSuccess && result.data && result.data.accessToken) {
                const rememberMe = localStorage.getItem(this.STORAGE_KEYS.REMEMBER_ME) === 'true';
                this.saveTokens(
                    result.data.accessToken,
                    result.data.refreshToken || refreshToken,
                    result.data.expiresIn || 3600,
                    rememberMe
                );
                return result.data.accessToken;
            } else {
                throw new Error('Invalid refresh response');
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.clearTokens();
            throw error;
        }
    },

    // Setup automatic token refresh
    setupAutoRefresh() {
        // Clear any existing interval
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // Check every minute if token needs refresh
        this.refreshInterval = setInterval(async () => {
            if (this.isAuthenticated() && this.needsRefresh()) {
                try {
                    await this.refreshAccessToken();
                    console.log('Token refreshed successfully');
                } catch (error) {
                    console.error('Auto refresh failed:', error);
                    // Redirect to login if refresh fails
                    window.location.href = '/login.html';
                }
            }
        }, 60000); // Check every minute

        // Also refresh immediately if needed
        if (this.needsRefresh()) {
            this.refreshAccessToken().catch(error => {
                console.error('Initial refresh failed:', error);
            });
        }
    },

    // Make authenticated API request
    async authenticatedFetch(url, options = {}) {
        const accessToken = this.getAccessToken();
        
        if (!accessToken) {
            throw new Error('No access token available');
        }

        // Add authorization header
        options.headers = {
            ...options.headers,
            'Authorization': `Bearer ${accessToken}`
        };

        // Make request
        let response = await fetch(url, options);

        // If unauthorized, try to refresh token and retry
        if (response.status === 401) {
            try {
                const newAccessToken = await this.refreshAccessToken();
                options.headers['Authorization'] = `Bearer ${newAccessToken}`;
                response = await fetch(url, options);
            } catch (error) {
                // Refresh failed, redirect to login
                window.location.href = '/login.html';
                throw error;
            }
        }

        return response;
    },

    // Initialize auth utilities
    init() {
        // Check if user is authenticated on page load
        if (this.isAuthenticated()) {
            this.setupAutoRefresh();
        }

        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        });
    }
};

// Initialize auth utilities when script loads
AuthUtils.init();