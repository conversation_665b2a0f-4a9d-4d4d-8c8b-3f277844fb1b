/* Import Cairo font for Arabic support */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* CSS Variables for consistent theming */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

/* Improve touch feedback on mobile */
html {
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', sans-serif;
    background: var(--primary-gradient);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    touch-action: manipulation;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Layout Structure */
.app-layout {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: var(--header-height);
}

.sidebar-logo {
    font-size: 2rem;
    transition: var(--transition);
}

.sidebar-title {
    font-size: 1.4rem;
    font-weight: 700;
    transition: var(--transition);
    white-space: nowrap;
}

.sidebar.collapsed .sidebar-title {
    opacity: 0;
    transform: translateX(20px);
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: absolute;
    top: 20px;
    left: -15px;
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
    z-index: 1001;
}

.sidebar-toggle:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
}

/* Navigation Menu */
.nav-menu {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section-title {
    padding: 0 20px 10px;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 600;
    transition: var(--transition);
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
    transform: translateX(20px);
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    gap: 15px;
    font-weight: 500;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.nav-link.active {
    background: rgba(102, 126, 234, 0.2);
    color: white;
    border-left: 4px solid var(--primary-color);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--primary-color);
}

.nav-icon {
    font-size: 1.2rem;
    min-width: 20px;
    text-align: center;
    transition: var(--transition);
}

.nav-text {
    transition: var(--transition);
    white-space: nowrap;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    transform: translateX(20px);
}

/* User Profile Section */
.user-profile {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    flex-shrink: 0;
}

.user-details {
    flex: 1;
    min-width: 0;
    transition: var(--transition);
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar.collapsed .user-details {
    opacity: 0;
    transform: translateX(20px);
}

.logout-btn {
    width: 100%;
    padding: 10px;
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #ff6b7a;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 500;
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 0.3);
    border-color: rgba(220, 53, 69, 0.5);
}

.sidebar.collapsed .logout-btn {
    padding: 10px 5px;
    font-size: 0.8rem;
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    transition: var(--transition);
    min-height: 100vh;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.sidebar.collapsed + .main-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* Mobile Overlay */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
    /* Ensure overlay doesn't block clicks when hidden */
    pointer-events: none;
}

.mobile-overlay.visible {
    pointer-events: auto;
}

.mobile-overlay.active {
    opacity: 1;
}

/* Force hide sidebar on mobile */
.sidebar.mobile-hidden {
    transform: translateX(100%) !important;
    visibility: hidden !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .cards-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .card {
        padding: 20px 15px;
    }

    .card-actions {
        flex-direction: column;
        gap: 10px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Improve touch targets for mobile */
    .btn {
        padding: 14px 20px;
        min-width: 100%;
        font-size: 15px;
    }
    
    /* Prevent double-tap zoom on buttons */
    button, a {
        touch-action: manipulation;
    }
    
    .sidebar {
        transform: translateX(100%) !important;
        width: var(--sidebar-width);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        visibility: hidden !important;
    }

    .sidebar.mobile-open {
        visibility: visible !important;
    }

    .sidebar.mobile-open {
        transform: translateX(0) !important;
    }

    .sidebar.collapsed {
        width: var(--sidebar-width);
        transform: translateX(100%) !important;
    }

    .sidebar.collapsed.mobile-open {
        transform: translateX(0) !important;
    }
    
    .main-content {
        margin-right: 0 !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .sidebar.collapsed + .main-content {
        margin-right: 0 !important;
    }
    
    .mobile-overlay {
        display: block;
        opacity: 0;
        pointer-events: none;
    }

    .mobile-overlay.visible {
        opacity: 1;
        pointer-events: auto;
    }

    .sidebar.mobile-open ~ .mobile-overlay.visible {
        display: block;
        opacity: 1;
        pointer-events: auto;
    }
    
    .sidebar-toggle {
        position: fixed;
        top: 20px;
        right: 20px;
        left: auto;
        z-index: 1002;
    }

    /* Mobile Content Container */
    .content-container {
        padding: 15px !important;
        margin: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    /* Mobile Container */
    .container {
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }

    /* Mobile Page Header */
    .page-header {
        padding: 20px 15px !important;
        margin: 0 0 20px 0 !important;
        border-radius: 8px !important;
    }

    .page-title {
        font-size: 1.8rem !important;
    }

    .page-subtitle {
        font-size: 1rem !important;
    }

    /* Mobile App Layout */
    .app-layout {
        width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Ensure body doesn't have horizontal scroll */
    body {
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }
}

/* Content Container */
.content-container {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Page Header */
.page-header {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 20px;
}
