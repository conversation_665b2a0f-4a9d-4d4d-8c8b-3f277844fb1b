import { BaseEntity, BooleanTransform } from '@app/common';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUrl,
  Max,
  Max<PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { User } from '../../auth/entities/user.entity';
import { Category } from '../../category';
import { Feature } from '../../feature';
import { Location } from '../../location';
import { PaymentMethod, PriceRange } from '../enums';
import { OperatingHours } from '../interfaces/operating-hours.interface';

export class Business extends BaseEntity {
  @IsNotEmpty()
  @IsString()
  nameAr: string;

  @IsNotEmpty()
  @IsString()
  nameEn: string;

  @IsOptional()
  @IsPhoneNumber()
  phoneNumber?: string;

  @IsOptional()
  @IsPhoneNumber()
  whatsAppNumber?: string;

  @IsOptional()
  @IsEnum(PriceRange)
  priceRange?: PriceRange;

  @IsNotEmpty()
  @IsNumber()
  locationId: number;

  @IsNotEmpty()
  @IsNumber()
  primaryCategoryId: number;

  @IsOptional()
  operatingHours?: OperatingHours;

  @IsOptional()
  @IsBoolean()
  isOpen24x7?: boolean = false;

  @IsOptional()
  ramadanHours?: OperatingHours;

  @IsOptional()
  @IsString()
  @IsUrl()
  logoUrl?: string;

  @IsOptional()
  @IsString()
  @IsUrl()
  coverPhotoUrl?: string;

  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  @Max(10, { each: true })
  galleryUrls?: string[];

  @IsOptional()
  @IsString()
  @MaxLength(160)
  shortDescriptionAr?: string;

  @IsOptional()
  @IsString()
  @MaxLength(160)
  shortDescriptionEn?: string;

  @IsOptional()
  @IsString()
  fullDescriptionAr?: string;

  @IsOptional()
  @IsString()
  fullDescriptionEn?: string;

  @IsOptional()
  @IsBoolean()
  @BooleanTransform()
  isActive?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @BooleanTransform()
  isVerified?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @BooleanTransform()
  isPremium?: boolean = false;

  @IsOptional()
  @IsDateString()
  premiumExpiresAt?: Date;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  averageRating?: number = 0;

  @IsOptional()
  @IsNumber()
  @Min(0)
  totalReviewsCount?: number = 0;

  @IsOptional()
  @IsNumber()
  @Min(0)
  totalViewsCount?: number = 0;

  @IsOptional()
  @IsNumber()
  @Min(0)
  lastMonthViews?: number = 0;

  @IsOptional()
  @IsNumber()
  ownerUserId?: number;

  @IsOptional()
  @IsArray()
  @IsEnum(PaymentMethod, { each: true })
  paymentMethods?: PaymentMethod[];

  // Relations
  @IsOptional()
  @ValidateNested()
  @Type(() => Location)
  location?: Location;

  @IsOptional()
  @ValidateNested()
  @Type(() => Category)
  primaryCategory?: Category;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Feature)
  features?: Feature[];

  @IsOptional()
  @ValidateNested()
  @Type(() => User)
  owner?: User;
}
