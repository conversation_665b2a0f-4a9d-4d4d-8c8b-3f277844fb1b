import {
  EntityManager,
  EntitySchema,
  EntityTarget,
  FindManyOptions,
  FindOneOptions,
  FindOptionsWhere,
  Repository,
  UpdateResult,
} from 'typeorm';
import { BaseEntity } from '../entities/base.entity';
import { IGenericRepository } from '../interfaces';

export abstract class GenericRepository<
  Entity extends BaseEntity,
  Schema extends EntitySchema<Entity>,
> implements IGenericRepository<Entity, Schema>
{
  protected constructor(private readonly repository: Repository<Entity>) {}

  getEntity(): EntityTarget<Entity> {
    return this.repository.target;
  }

  async findAndCountAll(
    query: FindManyOptions<Entity>,
  ): Promise<[Entity[], number]> {
    return this.repository.findAndCount(query);
  }

  async findOneById(id: number): Promise<Entity | null> {
    return this.repository.findOne({
      where: {
        id: id as any,
      },
    });
  }

  async findOneBy(
    query: FindOneOptions<Entity>,
    manager?: EntityManager,
  ): Promise<Entity | null> {
    return manager
      ? manager.findOne(this.repository.target, query)
      : this.repository.findOne(query);
  }

  async createOne(entity: Omit<Entity, 'id'>): Promise<Entity> {
    return this.repository.save(entity as Entity);
  }

  async updateOne(entity: Partial<Entity>): Promise<UpdateResult> {
    return await this.repository.update(
      { id: entity.id as any },
      { ...(entity as any) },
    );
  }

  async updateOneBy(
    filter: FindOptionsWhere<Entity>,
    entity: Partial<Entity>,
    manager?: EntityManager,
  ): Promise<UpdateResult> {
    return manager
      ? manager.update(this.repository.target, filter, entity as any)
      : this.repository.update(filter, entity as any);
  }

  async updateMany(entities: Entity[]): Promise<UpdateResult[]> {
    return Promise.all(
      entities.map((entity) => {
        return this.updateOne(entity);
      }),
    );
  }

  async createMany(entity: Entity[]): Promise<Entity[]> {
    return this.repository.save(entity);
  }

  async save(entities: Entity[], manager?: EntityManager): Promise<Entity[]> {
    return manager
      ? manager.save(this.repository.target, entities)
      : this.repository.save(entities);
  }

  async deleteOne(id: number): Promise<void> {
    await this.repository.softDelete(id);
  }
}
