/* Pagination Component Styles */

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #e1e8ed;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-info .total-count {
    font-weight: 600;
    color: var(--primary-color);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.pagination-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.pagination-size-selector select {
    padding: 6px 10px;
    border: 2px solid #e1e8ed;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    color: var(--dark-color);
    cursor: pointer;
    transition: var(--transition);
}

.pagination-size-selector select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.pagination-nav {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    border: 2px solid #e1e8ed;
    background: white;
    color: var(--dark-color);
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    user-select: none;
}

.pagination-btn:hover:not(.disabled) {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
    color: #999;
}

.pagination-btn.disabled:hover {
    transform: none;
    border-color: #e1e8ed;
    background: #f8f9fa;
}

.pagination-btn i {
    font-size: 12px;
}

.pagination-btn.prev,
.pagination-btn.next {
    font-weight: 600;
}

.pagination-btn.prev i,
.pagination-btn.next i {
    margin: 0 4px;
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    color: #999;
    font-weight: 600;
    user-select: none;
}

/* Loading state for pagination */
.pagination-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pagination-loading .pagination-btn {
    cursor: wait;
}

/* Responsive design */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }
    
    .pagination-info {
        justify-content: center;
        text-align: center;
    }
    
    .pagination-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .pagination-size-selector {
        justify-content: center;
    }
    
    .pagination-nav {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .pagination-btn {
        min-width: 44px;
        height: 44px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .pagination-nav {
        gap: 3px;
    }
    
    .pagination-btn {
        min-width: 36px;
        height: 36px;
        padding: 6px 8px;
        font-size: 14px;
    }
    
    .pagination-btn.prev .btn-text,
    .pagination-btn.next .btn-text {
        display: none;
    }
}

/* Animation for page transitions */
.content-loading {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.content-loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Pagination summary styles */
.pagination-summary {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: #666;
}

.pagination-summary .current-range {
    font-weight: 600;
    color: var(--primary-color);
}

.pagination-summary .total-items {
    font-weight: 600;
    color: var(--dark-color);
}

/* Quick jump to page */
.pagination-jump {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.pagination-jump input {
    width: 60px;
    padding: 6px 8px;
    border: 2px solid #e1e8ed;
    border-radius: 6px;
    text-align: center;
    font-size: 14px;
}

.pagination-jump input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.pagination-jump button {
    padding: 6px 12px;
    border: 2px solid #e1e8ed;
    background: white;
    color: var(--dark-color);
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-jump button:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    color: var(--primary-color);
}
