import { NestExpressApplication } from '@nestjs/platform-express';

export function initSecurity(app: NestExpressApplication) {
  // app.use(helmet({}));

  // Development and production domains
  const whitelist: RegExp[] = [
    new RegExp('.*localhost.*'),
    new RegExp('.*127\\.0\\.0\\.1.*'),
    new RegExp('.*192\\.168\\..*'), // Local network
    new RegExp('.*10\\.0\\..*'), // Local network
    new RegExp('.*172\\.16\\..*'), // Local network
    // Add your production domains here
    // new RegExp('.*yourdomain\\.com.*'),
    new RegExp('.*qareeb-plus-website-561378108820.me-central1.run.app.*'),
    new RegExp('.*qareeb-plus-backend-561378108820.me-central1.run.app.*'),
    new RegExp('.*qareeb-plus.web.app.*'),
    new RegExp('.*qareeb.yousef-rabie.com.*'),
  ];

  app.enableCors({
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Api-Key',
    ],
    exposedHeaders: ['X-Total-Count'],
    origin: (origin: string, callback) => {
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) return callback(null, true);

      // Check if origin matches whitelist
      if (whitelist.some((pattern) => pattern.test(origin))) {
        return callback(null, true);
      }

      // In development, log rejected origins for debugging
      if (process.env.NODE_ENV === 'development') {
        console.warn(`CORS: Rejected origin ${origin}`);
      }

      callback(new Error('Not allowed by CORS policy'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    optionsSuccessStatus: 200, // Some legacy browsers choke on 204
  });
}
