import {
  EntityManager,
  EntitySchema,
  EntityTarget,
  FindManyOptions,
  FindOneOptions,
  FindOptionsWhere,
  ObjectLiteral,
  UpdateResult,
} from 'typeorm';

export interface IGenericRepository<
  Entity extends ObjectLiteral,
  Schema extends EntitySchema<Entity>,
> {
  getEntity(): EntityTarget<Entity>;

  findAndCountAll(query: FindManyOptions<Entity>): Promise<[Entity[], number]>;

  findOneById(id: number): Promise<Entity | null>;

  findOneBy(
    query: FindOneOptions<Entity>,
    manager?: EntityManager,
  ): Promise<Entity | null>;

  createOne(entity: Omit<Entity, 'id'>): Promise<Entity>;

  updateOne(entity: Partial<Entity>): Promise<UpdateResult>;

  updateMany(entities: Entity[]): Promise<UpdateResult[]>;

  createMany(entity: Entity[]): Promise<Entity[]>;

  save(entities: Entity[], manager?: EntityManager): Promise<Entity[]>;

  deleteOne(id: number): Promise<void>;

  updateOneBy(
    filter: FindOptionsWhere<Entity>,
    entity: Partial<Entity>,
    manager?: EntityManager,
  ): Promise<UpdateResult>;
}

export const IGenericRepository = Symbol('IGenericRepository');
