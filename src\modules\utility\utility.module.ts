import { Module } from '@nestjs/common';
import { BusinessModule } from '../business/business.module';
import { CategoryModule } from '../category/category.module';
import { FeatureModule } from '../feature/feature.module';
import { LocationModule } from '../location/location.module';
import { UtilityController } from './controllers/utility.controller';
import { IUtilityService } from './interfaces/utility-service/utility-service.interface';
import { UtilityService } from './services/utility.service';

@Module({
  imports: [
    BusinessModule,
    LocationModule,
    CategoryModule,
    FeatureModule,
  ],
  controllers: [UtilityController],
  providers: [
    {
      provide: IUtilityService,
      useClass: UtilityService,
    },
  ],
  exports: [IUtilityService],
})
export class UtilityModule {}
