import { Inject, Injectable } from '@nestjs/common';
import { GenericService } from '@app/common';
import { Feature } from '../entities/feature.entity';
import { FeatureSchema } from '../schemes/feature.schema';
import { CreateFeatureDto } from '../dtos/create-feature.dto';
import { UpdateFeatureDto } from '../dtos/update-feature.dto';
import { GetAllFeatureDto } from '../dtos/get-all-feature.dto';
import { IFeatureRepository } from '../interfaces/feature-repository/feature-repository.interface';
import { IFeatureService } from '../interfaces/feature-service/feature-service.interface';

@Injectable()
export class FeatureService
  extends GenericService<
    Feature,
    typeof FeatureSchema,
    GetAllFeatureDto,
    CreateFeatureDto,
    UpdateFeatureDto
  >
  implements IFeatureService
{
  constructor(
    @Inject(IFeatureRepository)
    private readonly featureRepository: IFeatureRepository,
  ) {
    super(featureRepository);
  }
}
