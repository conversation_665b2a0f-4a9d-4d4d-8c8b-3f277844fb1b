import { OmitType, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNumber,
  IsObject,
  IsOptional,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { CreateLocationDto } from '../../location/dtos/create-location.dto';
import { Business } from '../entities/business.entity';

export class CreateBusinessDto extends OmitType(
  PickType(Business, [
    'nameAr',
    'nameEn',
    'phoneNumber',
    'whatsAppNumber',
    'priceRange',
    'locationId',
    'primaryCategoryId',
    'operatingHours',
    'isOpen24x7',
    'ramadanHours',
    'logoUrl',
    'coverPhotoUrl',
    'galleryUrls',
    'shortDescriptionAr',
    'shortDescriptionEn',
    'fullDescriptionAr',
    'fullDescriptionEn',
    'ownerUserId',
    'paymentMethods',
    'isActive',
    'isVerified',
    'isPremium',
  ] as const),
  ['locationId'] as const,
) {
  @ValidateIf((o) => !o.location)
  @IsNumber()
  locationId?: number;

  @ValidateIf((o) => !o.locationId)
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => CreateLocationDto)
  location?: CreateLocationDto;

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  featureIds?: number[];
}
