import { Public } from '@app/common/decorators';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseFloatPipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  Api<PERSON>asicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IGetAllResponseInterface } from '../../../common/interfaces';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { CreateLocationDto } from '../dtos/create-location.dto';
import { CreatePublicLocationDto } from '../dtos/create-public-location.dto';
import { GetAllLocationDto } from '../dtos/get-all-location.dto';
import { UpdateLocationDto } from '../dtos/update-location.dto';
import { Location } from '../entities/location.entity';
import { ILocationService } from '../interfaces/location-service/location-service.interface';

@Controller('locations')
@ApiTags('Locations')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class LocationController {
  constructor(
    @Inject(ILocationService)
    private readonly locationService: ILocationService,
  ) {}

  @Post('public')
  @ApiOperation({ summary: 'Publicly create a new location' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The location has been successfully created.',
    type: Location,
  })
  @HttpCode(HttpStatus.CREATED)
  @Public()
  async createPublic(
    @Body() createDto: CreatePublicLocationDto,
  ): Promise<Location> {
    return this.locationService.create(Location, createDto);
  }

  @Get('public')
  @ApiOperation({
    summary: 'Publicly get all locations with pagination and filtering',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  @Public()
  async getAllPublic(
    @Query() getAllDto: GetAllLocationDto,
  ): Promise<IGetAllResponseInterface<Location>> {
    return this.locationService.getAll(getAllDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all locations with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getAll(
    @Query() getAllDto: GetAllLocationDto,
  ): Promise<IGetAllResponseInterface<Location>> {
    return this.locationService.getAll(getAllDto);
  }

  @Get('coordinates/:latitude/:longitude')
  @ApiOperation({
    summary: 'Get locations by coordinates with optional radius',
  })
  @ApiParam({
    name: 'latitude',
    description: 'Latitude coordinate',
    type: Number,
  })
  @ApiParam({
    name: 'longitude',
    description: 'Longitude coordinate',
    type: Number,
  })
  @ApiQuery({
    name: 'radiusKm',
    description: 'Search radius in kilometers',
    type: Number,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getByCoordinates(
    @Param('latitude', ParseFloatPipe) latitude: number,
    @Param('longitude', ParseFloatPipe) longitude: number,
    @Query('radiusKm') radiusKm?: number,
  ): Promise<Location[]> {
    return this.locationService.findByCoordinates(
      latitude,
      longitude,
      radiusKm,
    );
  }

  @Get('bounds')
  @ApiOperation({ summary: 'Get locations within geographical bounds' })
  @ApiQuery({
    name: 'minLatitude',
    description: 'Minimum latitude',
    type: Number,
  })
  @ApiQuery({
    name: 'maxLatitude',
    description: 'Maximum latitude',
    type: Number,
  })
  @ApiQuery({
    name: 'minLongitude',
    description: 'Minimum longitude',
    type: Number,
  })
  @ApiQuery({
    name: 'maxLongitude',
    description: 'Maximum longitude',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getWithinBounds(
    @Query('minLatitude', ParseFloatPipe) minLatitude: number,
    @Query('maxLatitude', ParseFloatPipe) maxLatitude: number,
    @Query('minLongitude', ParseFloatPipe) minLongitude: number,
    @Query('maxLongitude', ParseFloatPipe) maxLongitude: number,
  ): Promise<Location[]> {
    return this.locationService.findWithinBounds(
      minLatitude,
      maxLatitude,
      minLongitude,
      maxLongitude,
    );
  }

  @Get('nearest/:latitude/:longitude')
  @ApiOperation({ summary: 'Get nearest locations to coordinates' })
  @ApiParam({
    name: 'latitude',
    description: 'Latitude coordinate',
    type: Number,
  })
  @ApiParam({
    name: 'longitude',
    description: 'Longitude coordinate',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of results',
    type: Number,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The nearest locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getNearest(
    @Param('latitude', ParseFloatPipe) latitude: number,
    @Param('longitude', ParseFloatPipe) longitude: number,
    @Query('limit') limit?: number,
  ): Promise<Location[]> {
    return this.locationService.findNearest(latitude, longitude, limit);
  }

  @Get('city')
  @ApiOperation({ summary: 'Get locations by city' })
  @ApiQuery({
    name: 'cityEn',
    description: 'City name in English',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'cityAr',
    description: 'City name in Arabic',
    type: String,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getByCity(
    @Query('cityEn') cityEn?: string,
    @Query('cityAr') cityAr?: string,
  ): Promise<Location[]> {
    return this.locationService.findByCity(cityEn, cityAr);
  }

  @Get('country')
  @ApiOperation({ summary: 'Get locations by country' })
  @ApiQuery({
    name: 'countryEn',
    description: 'Country name in English',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'countryAr',
    description: 'Country name in Arabic',
    type: String,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getByCountry(
    @Query('countryEn') countryEn?: string,
    @Query('countryAr') countryAr?: string,
  ): Promise<Location[]> {
    return this.locationService.findByCountry(countryEn, countryAr);
  }

  @Get('parent/:parentId')
  @ApiOperation({ summary: 'Get locations by parent ID' })
  @ApiParam({
    name: 'parentId',
    description: 'Parent Location ID',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The locations have been successfully retrieved.',
    type: Location,
    isArray: true,
  })
  async getByParentId(
    @Param('parentId', ParseIntPipe) parentId: number,
  ): Promise<Location[]> {
    return this.locationService.findByParentId(parentId);
  }

  @Get('distance/:lat1/:lon1/:lat2/:lon2')
  @ApiOperation({ summary: 'Calculate distance between two coordinates' })
  @ApiParam({ name: 'lat1', description: 'First latitude', type: Number })
  @ApiParam({ name: 'lon1', description: 'First longitude', type: Number })
  @ApiParam({ name: 'lat2', description: 'Second latitude', type: Number })
  @ApiParam({ name: 'lon2', description: 'Second longitude', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Distance calculated successfully.',
    schema: { type: 'number' },
  })
  calculateDistance(
    @Param('lat1', ParseFloatPipe) lat1: number,
    @Param('lon1', ParseFloatPipe) lon1: number,
    @Param('lat2', ParseFloatPipe) lat2: number,
    @Param('lon2', ParseFloatPipe) lon2: number,
  ): { distance: number } {
    const distance = this.locationService.calculateDistance(
      lat1,
      lon1,
      lat2,
      lon2,
    );
    return { distance };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get location by ID' })
  @ApiParam({ name: 'id', description: 'Location ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The location has been successfully retrieved.',
    type: Location,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Location not found.',
  })
  async getById(@Param('id', ParseIntPipe) id: number): Promise<Location> {
    return this.locationService.findOneById(id);
  }

  @Get(':id/with-children')
  @ApiOperation({ summary: 'Get location with children' })
  @ApiParam({ name: 'id', description: 'Location ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The location with children has been successfully retrieved.',
    type: Location,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Location not found.',
  })
  async getWithChildren(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Location | null> {
    return this.locationService.findWithChildren(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create new location' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The location has been successfully created.',
    type: Location,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDto: CreateLocationDto): Promise<Location> {
    return this.locationService.create(Location, createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an existing location' })
  @ApiParam({ name: 'id', description: 'Location ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The location has been successfully updated.',
    type: Location,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Location not found.',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateLocationDto,
  ): Promise<Location> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.locationService.update(Location, updateDtoWithId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a location' })
  @ApiParam({ name: 'id', description: 'Location ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The location has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Location not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.locationService.deleteOne(id);
  }
}
