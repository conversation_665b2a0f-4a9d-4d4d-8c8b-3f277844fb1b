import { EntitySchemaColumnOptions } from 'typeorm';

export const BaseSchemaProperties: Record<string, EntitySchemaColumnOptions> = {
  id: {
    type: Number,
    primary: true,
    generated: true,
  },
  createdAt: {
    type: 'timestamp',
    nullable: false,
    createDate: true,
  },
  updatedAt: {
    type: 'timestamp',
    nullable: false,
    updateDate: true,
  },
  deletedAt: {
    type: 'timestamp',
    nullable: true,
    deleteDate: true,
  },
};
