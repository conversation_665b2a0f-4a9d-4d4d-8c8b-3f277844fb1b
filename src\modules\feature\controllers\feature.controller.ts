import { Public } from '@app/common';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IGetAllResponseInterface } from '../../../common/interfaces';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { CreateFeatureDto } from '../dtos/create-feature.dto';
import { GetAllFeatureDto } from '../dtos/get-all-feature.dto';
import { UpdateFeatureDto } from '../dtos/update-feature.dto';
import { Feature } from '../entities/feature.entity';
import { IFeatureService } from '../interfaces/feature-service/feature-service.interface';

@Controller('features')
@ApiTags('Features')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class FeatureController {
  constructor(
    @Inject(IFeatureService) private readonly featureService: IFeatureService,
  ) {}

  /**
   * Get all features with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all features with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The features have been successfully retrieved.',
    type: Feature,
    isArray: true,
  })
  @Public()
  async getAll(
    @Query() getAllDto: GetAllFeatureDto,
  ): Promise<IGetAllResponseInterface<Feature>> {
    return this.featureService.getAll(getAllDto);
  }

  /**
   * Get feature by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get feature by ID' })
  @ApiParam({ name: 'id', description: 'Feature ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The feature has been successfully retrieved.',
    type: Feature,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Feature not found.',
  })
  async getById(@Param('id', ParseIntPipe) id: number): Promise<Feature> {
    return this.featureService.findOneById(id);
  }

  /**
   * Create new feature
   */
  @Post()
  @ApiOperation({ summary: 'Create new feature' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The feature has been successfully created.',
    type: Feature,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDto: CreateFeatureDto): Promise<Feature> {
    return this.featureService.create(Feature, createDto);
  }

  /**
   * Update an existing feature
   */
  @Put(':id')
  @ApiOperation({ summary: 'Update an existing feature' })
  @ApiParam({ name: 'id', description: 'Feature ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The feature has been successfully updated.',
    type: Feature,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Feature not found.',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateFeatureDto,
  ): Promise<Feature> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.featureService.update(Feature, updateDtoWithId);
  }

  /**
   * Delete a feature
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a feature' })
  @ApiParam({ name: 'id', description: 'Feature ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The feature has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Feature not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.featureService.deleteOne(id);
  }
}
