-- Business Update Script for Qareeb Plus Backend
-- This script updates business descriptions, properties, and features
-- Generated for 209 businesses across 15 categories
--
-- IMPORTANT: This script uses conditional updates to preserve existing data
-- - Only updates descriptions if they are NULL or empty
-- - Only sets isOpen24x7 if it's NULL
-- - Only sets priceRange if it's NULL
-- - Only adds features to businesses that don't already have features assigned
--
-- This ensures existing business data is not overwritten

-- First, let's see what we're working with
SELECT 'Current business count:' as info, COUNT(*) as count FROM businesses;
SELECT 'Available categories:' as info;
SELECT id, "nameAr", "nameEn" FROM categories ORDER BY id;
SELECT 'Available features:' as info;
SELECT id, "nameAr", "nameEn" FROM features WHERE "deletedAt" IS NULL ORDER BY id;

-- Update businesses with category-specific descriptions and properties (only if not already set)
-- Category 1: Restaurants
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN "shortDescriptionAr" IS NULL OR "shortDescriptionAr" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'مطعم يقدم أشهى الأطباق المحلية والعالمية بجودة عالية وخدمة ممتازة في أجواء مريحة ومناسبة للعائلات'
                WHEN id % 3 = 1 THEN 'تجربة طعام فريدة تجمع بين النكهات الأصيلة والحديثة مع خدمة احترافية وأجواء دافئة ومرحبة'
                ELSE 'مطعم عائلي يتميز بتقديم وجبات طازجة ولذيذة مع اهتمام خاص بالجودة والنظافة وراحة الضيوف'
            END
        ELSE "shortDescriptionAr"
    END,
    "shortDescriptionEn" = CASE
        WHEN "shortDescriptionEn" IS NULL OR "shortDescriptionEn" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'A restaurant offering the most delicious local and international dishes with high quality and excellent service in a comfortable family-friendly atmosphere'
                WHEN id % 3 = 1 THEN 'A unique dining experience combining authentic and modern flavors with professional service and warm, welcoming ambiance'
                ELSE 'A family restaurant distinguished by serving fresh and delicious meals with special attention to quality, cleanliness and guest comfort'
            END
        ELSE "shortDescriptionEn"
    END,
    "fullDescriptionAr" = CASE
        WHEN "fullDescriptionAr" IS NULL OR "fullDescriptionAr" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'مطعم يقدم أشهى الأطباق المحلية والعالمية بجودة عالية وخدمة ممتازة في أجواء مريحة ومناسبة للعائلات. نحن نفخر بتقديم تجربة طعام استثنائية تجمع بين الأصالة والحداثة، مع التركيز على استخدام أجود المكونات الطازجة والطبيعية. فريقنا المدرب يحرص على تقديم خدمة راقية ومتميزة لضمان رضا جميع ضيوفنا.'
                WHEN id % 3 = 1 THEN 'تجربة طعام فريدة تجمع بين النكهات الأصيلة والحديثة مع خدمة احترافية وأجواء دافئة ومرحبة. مطعمنا يتميز بقائمة طعام متنوعة تناسب جميع الأذواق، مع التركيز على الجودة والطعم الأصيل. نوفر بيئة مريحة وأنيقة مثالية للعائلات والأصدقاء للاستمتاع بوجبة لا تُنسى.'
                ELSE 'مطعم عائلي يتميز بتقديم وجبات طازجة ولذيذة مع اهتمام خاص بالجودة والنظافة وراحة الضيوف. نحن ملتزمون بتقديم أطباق شهية محضرة بعناية فائقة، مع الحرص على توفير تجربة طعام ممتعة ومريحة لجميع أفراد العائلة في أجواء دافئة ومرحبة.'
            END
        ELSE "fullDescriptionAr"
    END,
    "fullDescriptionEn" = CASE
        WHEN "fullDescriptionEn" IS NULL OR "fullDescriptionEn" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'A restaurant offering the most delicious local and international dishes with high quality and excellent service in a comfortable family-friendly atmosphere. We pride ourselves on providing an exceptional dining experience that combines authenticity with modernity, focusing on using the finest fresh and natural ingredients. Our trained team ensures elegant and distinguished service to guarantee the satisfaction of all our guests.'
                WHEN id % 3 = 1 THEN 'A unique dining experience combining authentic and modern flavors with professional service and warm, welcoming ambiance. Our restaurant features a diverse menu that suits all tastes, focusing on quality and authentic flavor. We provide a comfortable and elegant environment perfect for families and friends to enjoy an unforgettable meal.'
                ELSE 'A family restaurant distinguished by serving fresh and delicious meals with special attention to quality, cleanliness and guest comfort. We are committed to providing delicious dishes prepared with exceptional care, ensuring a pleasant and comfortable dining experience for all family members in a warm and welcoming atmosphere.'
            END
        ELSE "fullDescriptionEn"
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 1;

-- Category 2: Shopping
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN "shortDescriptionAr" IS NULL OR "shortDescriptionAr" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'متجر شامل يوفر مجموعة واسعة من المنتجات عالية الجودة بأسعار تنافسية وخدمة عملاء ممتازة'
                WHEN id % 3 = 1 THEN 'وجهة التسوق المثالية التي تجمع بين التنوع والجودة والأسعار المناسبة في مكان واحد مريح'
                ELSE 'محل تجاري متميز يقدم أفضل المنتجات المحلية والعالمية مع ضمان الجودة والخدمة السريعة'
            END
        ELSE "shortDescriptionAr"
    END,
    "shortDescriptionEn" = CASE
        WHEN "shortDescriptionEn" IS NULL OR "shortDescriptionEn" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'A comprehensive store offering a wide range of high-quality products at competitive prices with excellent customer service'
                WHEN id % 3 = 1 THEN 'The ideal shopping destination that combines variety, quality and reasonable prices in one convenient location'
                ELSE 'A distinguished commercial store offering the best local and international products with quality guarantee and fast service'
            END
        ELSE "shortDescriptionEn"
    END,
    "fullDescriptionAr" = CASE
        WHEN "fullDescriptionAr" IS NULL OR "fullDescriptionAr" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'متجر شامل يوفر مجموعة واسعة من المنتجات عالية الجودة بأسعار تنافسية وخدمة عملاء ممتازة. نحن نفخر بتقديم تشكيلة متنوعة من المنتجات التي تلبي احتياجات جميع أفراد الأسرة، مع الحرص على توفير أفضل الأسعار والعروض المميزة. فريق خدمة العملاء لدينا مدرب لتقديم المساعدة والنصائح لضمان تجربة تسوق مريحة وممتعة.'
                WHEN id % 3 = 1 THEN 'وجهة التسوق المثالية التي تجمع بين التنوع والجودة والأسعار المناسبة في مكان واحد مريح. متجرنا يضم مجموعة واسعة من المنتجات المختارة بعناية لتناسب جميع الاحتياجات والميزانيات. نوفر بيئة تسوق مريحة وآمنة مع خدمات متميزة تجعل تجربة التسوق لديكم سهلة وممتعة.'
                ELSE 'محل تجاري متميز يقدم أفضل المنتجات المحلية والعالمية مع ضمان الجودة والخدمة السريعة. نحن ملتزمون بتوفير منتجات عالية الجودة بأسعار معقولة، مع التركيز على رضا العملاء وتقديم خدمة متميزة. موقعنا المناسب وساعات العمل المرنة تجعلنا الخيار الأمثل لجميع احتياجاتكم التسويقية.'
            END
        ELSE "fullDescriptionAr"
    END,
    "fullDescriptionEn" = CASE
        WHEN "fullDescriptionEn" IS NULL OR "fullDescriptionEn" = '' THEN
            CASE
                WHEN id % 3 = 0 THEN 'A comprehensive store offering a wide range of high-quality products at competitive prices with excellent customer service. We pride ourselves on providing a diverse selection of products that meet the needs of all family members, while ensuring the best prices and special offers. Our customer service team is trained to provide assistance and advice to ensure a comfortable and enjoyable shopping experience.'
                WHEN id % 3 = 1 THEN 'The ideal shopping destination that combines variety, quality and reasonable prices in one convenient location. Our store features a wide range of carefully selected products to suit all needs and budgets. We provide a comfortable and safe shopping environment with excellent services that make your shopping experience easy and enjoyable.'
                ELSE 'A distinguished commercial store offering the best local and international products with quality guarantee and fast service. We are committed to providing high-quality products at reasonable prices, focusing on customer satisfaction and providing excellent service. Our convenient location and flexible hours make us the ideal choice for all your shopping needs.'
            END
        ELSE "fullDescriptionEn"
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 2;

-- Category 3: Healthcare
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مركز طبي متخصص يقدم خدمات صحية شاملة بأحدث التقنيات وفريق طبي مؤهل وذو خبرة عالية'
        WHEN id % 3 = 1 THEN 'عيادة طبية تتميز بالرعاية الصحية المتميزة والخدمة الإنسانية مع الالتزام بأعلى معايير الجودة والسلامة'
        ELSE 'مؤسسة صحية رائدة تهدف إلى تقديم أفضل الخدمات الطبية والعلاجية في بيئة آمنة ومريحة'
    END,
    "shortDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'A specialized medical center providing comprehensive health services with the latest technologies and a qualified, highly experienced medical team'
        WHEN id % 3 = 1 THEN 'A medical clinic distinguished by excellent healthcare and humanitarian service with commitment to the highest quality and safety standards'
        ELSE 'A leading healthcare institution aiming to provide the best medical and therapeutic services in a safe and comfortable environment'
    END,
    "fullDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مركز طبي متخصص يقدم خدمات صحية شاملة بأحدث التقنيات وفريق طبي مؤهل وذو خبرة عالية. نحن ملتزمون بتقديم رعاية صحية متميزة تلبي احتياجات جميع المرضى، مع التركيز على الدقة في التشخيص والعلاج. مرافقنا الحديثة وأجهزتنا المتطورة تضمن حصولكم على أفضل خدمة طبية ممكنة.'
        WHEN id % 3 = 1 THEN 'عيادة طبية تتميز بالرعاية الصحية المتميزة والخدمة الإنسانية مع الالتزام بأعلى معايير الجودة والسلامة. فريقنا الطبي المتخصص يحرص على تقديم رعاية شخصية ومتميزة لكل مريض، مع الاهتمام بالراحة النفسية والجسدية. نوفر بيئة طبية آمنة ونظيفة تضمن أفضل النتائج العلاجية.'
        ELSE 'مؤسسة صحية رائدة تهدف إلى تقديم أفضل الخدمات الطبية والعلاجية في بيئة آمنة ومريحة. نحن نفخر بفريقنا الطبي المتميز والمرافق الحديثة التي تمكننا من تقديم رعاية صحية شاملة ومتطورة. التزامنا بالتميز والجودة يجعلنا الخيار الأمثل لجميع احتياجاتكم الصحية.'
    END,
    "fullDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'A specialized medical center providing comprehensive health services with the latest technologies and a qualified, highly experienced medical team. We are committed to providing excellent healthcare that meets the needs of all patients, focusing on accuracy in diagnosis and treatment. Our modern facilities and advanced equipment ensure you receive the best possible medical service.'
        WHEN id % 3 = 1 THEN 'A medical clinic distinguished by excellent healthcare and humanitarian service with commitment to the highest quality and safety standards. Our specialized medical team ensures personalized and excellent care for each patient, with attention to psychological and physical comfort. We provide a safe and clean medical environment that ensures the best therapeutic results.'
        ELSE 'A leading healthcare institution aiming to provide the best medical and therapeutic services in a safe and comfortable environment. We pride ourselves on our distinguished medical team and modern facilities that enable us to provide comprehensive and advanced healthcare. Our commitment to excellence and quality makes us the ideal choice for all your healthcare needs.'
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 3;

-- Category 4: Entertainment
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مركز ترفيهي متكامل يوفر أنشطة متنوعة وممتعة للجميع في أجواء آمنة ومريحة مع خدمة متميزة'
        WHEN id % 3 = 1 THEN 'وجهة ترفيهية مثالية تجمع بين المتعة والإثارة مع مرافق حديثة وأنشطة مناسبة لجميع الأعمار'
        ELSE 'مكان ترفيهي فريد يقدم تجارب ممتعة ولا تُنسى مع الحرص على الأمان والراحة لجميع الزوار'
    END,
    "shortDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'A comprehensive entertainment center offering diverse and enjoyable activities for everyone in a safe and comfortable atmosphere with excellent service'
        WHEN id % 3 = 1 THEN 'An ideal entertainment destination combining fun and excitement with modern facilities and activities suitable for all ages'
        ELSE 'A unique entertainment venue offering enjoyable and unforgettable experiences while ensuring safety and comfort for all visitors'
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 4;

-- Category 5: Services
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مقدم خدمات محترف يتميز بالجودة والسرعة والدقة في التنفيذ مع الالتزام بالمواعيد وإرضاء العملاء'
        WHEN id % 3 = 1 THEN 'خدمات متخصصة عالية الجودة تلبي احتياجات العملاء بكفاءة ومهنية مع ضمان الرضا التام'
        ELSE 'مؤسسة خدمية رائدة تقدم حلول شاملة ومبتكرة مع فريق عمل مدرب وذو خبرة واسعة'
    END,
    "shortDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'A professional service provider distinguished by quality, speed and precision in execution with commitment to deadlines and customer satisfaction'
        WHEN id % 3 = 1 THEN 'High-quality specialized services meeting customer needs efficiently and professionally with guarantee of complete satisfaction'
        ELSE 'A leading service institution providing comprehensive and innovative solutions with a trained and extensively experienced team'
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 5;

-- Category 6: Fast Food
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مطعم وجبات سريعة يقدم أطباق لذيذة وطازجة بسرعة وجودة عالية مع خيارات متنوعة تناسب جميع الأذواق'
        WHEN id % 3 = 1 THEN 'وجهة مثالية للوجبات السريعة اللذيذة والصحية مع خدمة سريعة وأسعار مناسبة في أجواء نظيفة ومريحة'
        ELSE 'مطعم عصري متخصص في الوجبات السريعة الطازجة مع التركيز على الجودة والنظافة والخدمة المتميزة'
    END,
    "shortDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'A fast food restaurant serving delicious and fresh dishes quickly with high quality and diverse options suitable for all tastes'
        WHEN id % 3 = 1 THEN 'An ideal destination for delicious and healthy fast food with quick service and reasonable prices in a clean and comfortable atmosphere'
        ELSE 'A modern restaurant specializing in fresh fast food with focus on quality, cleanliness and excellent service'
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 6;

-- Category 7: Fine Dining
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مطعم راقي يقدم تجربة طعام فاخرة مع أطباق مبتكرة وخدمة استثنائية في أجواء أنيقة ومميزة'
        WHEN id % 3 = 1 THEN 'وجهة طعام فاخرة تتميز بالأطباق الراقية والخدمة المتميزة مع اهتمام خاص بكل التفاصيل'
        ELSE 'مطعم فاخر يجمع بين فن الطبخ والضيافة الراقية ليقدم تجربة طعام لا تُنسى في بيئة أنيقة'
    END,
    "shortDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'An upscale restaurant offering a luxurious dining experience with innovative dishes and exceptional service in an elegant and distinctive atmosphere'
        WHEN id % 3 = 1 THEN 'A luxury dining destination distinguished by fine dishes and excellent service with special attention to every detail'
        ELSE 'A luxury restaurant combining culinary art and upscale hospitality to provide an unforgettable dining experience in an elegant environment'
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 7;

-- Category 8: Cafes
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN id % 3 = 0 THEN 'مقهى عصري يقدم أجود أنواع القهوة والمشروبات الساخنة والباردة مع أجواء مريحة مثالية للاسترخاء والعمل'
        WHEN id % 3 = 1 THEN 'كافيه مميز يجمع بين جودة القهوة المحضرة بعناية والأجواء الدافئة المناسبة للقاءات والدراسة'
        ELSE 'مقهى أنيق يوفر تجربة قهوة استثنائية مع مجموعة متنوعة من المشروبات والحلويات في بيئة هادئة ومريحة'
    END,
    "shortDescriptionEn" = CASE
        WHEN id % 3 = 0 THEN 'A modern cafe serving the finest coffee and hot and cold beverages with a comfortable atmosphere perfect for relaxation and work'
        WHEN id % 3 = 1 THEN 'A distinctive cafe combining carefully prepared quality coffee with warm ambiance suitable for meetings and studying'
        ELSE 'An elegant cafe providing an exceptional coffee experience with a diverse selection of beverages and desserts in a quiet and comfortable environment'
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" = 8;

-- Update remaining categories with basic descriptions and properties
UPDATE businesses
SET
    "shortDescriptionAr" = CASE
        WHEN "primaryCategoryId" = 9 THEN 'متجر ملابس عصري يقدم أحدث صيحات الموضة بجودة عالية وأسعار مناسبة'
        WHEN "primaryCategoryId" = 10 THEN 'متجر إلكترونيات متخصص يوفر أحدث الأجهزة والتقنيات بضمان شامل وخدمة متميزة'
        WHEN "primaryCategoryId" = 11 THEN 'مستشفى متطور يقدم خدمات طبية شاملة على مدار الساعة بأعلى معايير الجودة'
        WHEN "primaryCategoryId" = 12 THEN 'صيدلية شاملة توفر جميع الأدوية والمستلزمات الطبية مع استشارة صيدلانية مجانية'
        WHEN "primaryCategoryId" = 13 THEN 'مخبز تقليدي يقدم أطيب المخبوزات الطازجة والحلويات الشرقية والغربية'
        WHEN "primaryCategoryId" = 14 THEN 'خياط ماهر متخصص في تفصيل وتعديل الملابس بدقة عالية وإتقان'
        WHEN "primaryCategoryId" = 15 THEN 'متجر مواد بناء شامل يوفر جميع احتياجات البناء والتشييد بأفضل الأسعار'
        ELSE "shortDescriptionAr"
    END,
    "shortDescriptionEn" = CASE
        WHEN "primaryCategoryId" = 9 THEN 'A modern clothing store offering the latest fashion trends with high quality and reasonable prices'
        WHEN "primaryCategoryId" = 10 THEN 'A specialized electronics store providing the latest devices and technologies with comprehensive warranty and excellent service'
        WHEN "primaryCategoryId" = 11 THEN 'An advanced hospital providing comprehensive medical services 24/7 with the highest quality standards'
        WHEN "primaryCategoryId" = 12 THEN 'A comprehensive pharmacy providing all medicines and medical supplies with free pharmaceutical consultation'
        WHEN "primaryCategoryId" = 13 THEN 'A traditional bakery offering the finest fresh baked goods and Eastern and Western sweets'
        WHEN "primaryCategoryId" = 14 THEN 'A skilled tailor specializing in tailoring and altering clothes with high precision and craftsmanship'
        WHEN "primaryCategoryId" = 15 THEN 'A comprehensive building materials store providing all construction and building needs at the best prices'
        ELSE "shortDescriptionEn"
    END,
    "isOpen24x7" = CASE
        WHEN "isOpen24x7" IS NULL THEN true
        ELSE "isOpen24x7"
    END,
    "priceRange" = CASE
        WHEN "priceRange" IS NULL THEN '₤₤'
        ELSE "priceRange"
    END
WHERE "primaryCategoryId" IN (9, 10, 11, 12, 13, 14, 15);

-- Now let's add features to businesses based on their categories
-- Only add features if business doesn't already have any features assigned
-- First, let's see which businesses already have features
SELECT 'Businesses with existing features:' as info, COUNT(DISTINCT "businessId") as count FROM business_features;

-- Only proceed with feature assignment for businesses that don't have features yet

-- Add features for Restaurants (Category 1) - only for businesses without existing features
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 1
AND b.id NOT IN (SELECT DISTINCT "businessId" FROM business_features WHERE "businessId" IS NOT NULL)
AND (
    -- Select random features for each business (using modulo to create variation)
    (b.id % 10 = 0 AND f.id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)) OR
    (b.id % 10 = 1 AND f.id IN (2, 3, 4, 5, 6, 7, 8, 9, 10, 11)) OR
    (b.id % 10 = 2 AND f.id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12)) OR
    (b.id % 10 = 3 AND f.id IN (4, 5, 6, 7, 8, 9, 10, 11, 12, 13)) OR
    (b.id % 10 = 4 AND f.id IN (5, 6, 7, 8, 9, 10, 11, 12, 13, 14)) OR
    (b.id % 10 = 5 AND f.id IN (6, 7, 8, 9, 10, 11, 12, 13, 14, 15)) OR
    (b.id % 10 = 6 AND f.id IN (7, 8, 9, 10, 11, 12, 13, 14, 15, 16)) OR
    (b.id % 10 = 7 AND f.id IN (8, 9, 10, 11, 12, 13, 14, 15, 16, 17)) OR
    (b.id % 10 = 8 AND f.id IN (9, 10, 11, 12, 13, 14, 15, 16, 17, 18)) OR
    (b.id % 10 = 9 AND f.id IN (10, 11, 12, 13, 14, 15, 16, 17, 18, 19))
);

-- Add features for Shopping (Category 2) - only for businesses without existing features
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 2
AND b.id NOT IN (SELECT DISTINCT "businessId" FROM business_features WHERE "businessId" IS NOT NULL)
AND (
    (b.id % 8 = 0 AND f.id IN (1, 2, 3, 4, 5, 6, 7, 8)) OR
    (b.id % 8 = 1 AND f.id IN (2, 3, 4, 5, 6, 7, 8, 9)) OR
    (b.id % 8 = 2 AND f.id IN (3, 4, 5, 6, 7, 8, 9, 10)) OR
    (b.id % 8 = 3 AND f.id IN (4, 5, 6, 7, 8, 9, 10, 11)) OR
    (b.id % 8 = 4 AND f.id IN (5, 6, 7, 8, 9, 10, 11, 12)) OR
    (b.id % 8 = 5 AND f.id IN (6, 7, 8, 9, 10, 11, 12, 13)) OR
    (b.id % 8 = 6 AND f.id IN (7, 8, 9, 10, 11, 12, 13, 14)) OR
    (b.id % 8 = 7 AND f.id IN (8, 9, 10, 11, 12, 13, 14, 15))
);

-- Add features for Healthcare (Category 3) - only for businesses without existing features
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 3
AND b.id NOT IN (SELECT DISTINCT "businessId" FROM business_features WHERE "businessId" IS NOT NULL)
AND (
    (b.id % 6 = 0 AND f.id IN (1, 2, 3, 4, 5, 6)) OR
    (b.id % 6 = 1 AND f.id IN (2, 3, 4, 5, 6, 7)) OR
    (b.id % 6 = 2 AND f.id IN (3, 4, 5, 6, 7, 8)) OR
    (b.id % 6 = 3 AND f.id IN (4, 5, 6, 7, 8, 9)) OR
    (b.id % 6 = 4 AND f.id IN (5, 6, 7, 8, 9, 10)) OR
    (b.id % 6 = 5 AND f.id IN (6, 7, 8, 9, 10, 11))
);

-- Add features for Entertainment (Category 4)
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 4
AND (
    (b.id % 7 = 0 AND f.id IN (1, 2, 3, 4, 5, 6, 7)) OR
    (b.id % 7 = 1 AND f.id IN (2, 3, 4, 5, 6, 7, 8)) OR
    (b.id % 7 = 2 AND f.id IN (3, 4, 5, 6, 7, 8, 9)) OR
    (b.id % 7 = 3 AND f.id IN (4, 5, 6, 7, 8, 9, 10)) OR
    (b.id % 7 = 4 AND f.id IN (5, 6, 7, 8, 9, 10, 11)) OR
    (b.id % 7 = 5 AND f.id IN (6, 7, 8, 9, 10, 11, 12)) OR
    (b.id % 7 = 6 AND f.id IN (7, 8, 9, 10, 11, 12, 13))
);

-- Add features for Services (Category 5)
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 5
AND (
    (b.id % 5 = 0 AND f.id IN (1, 2, 3, 4, 5)) OR
    (b.id % 5 = 1 AND f.id IN (2, 3, 4, 5, 6)) OR
    (b.id % 5 = 2 AND f.id IN (3, 4, 5, 6, 7)) OR
    (b.id % 5 = 3 AND f.id IN (4, 5, 6, 7, 8)) OR
    (b.id % 5 = 4 AND f.id IN (5, 6, 7, 8, 9))
);

-- Add features for Fast Food (Category 6)
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 6
AND (
    (b.id % 8 = 0 AND f.id IN (1, 2, 3, 4, 5, 6, 7, 8)) OR
    (b.id % 8 = 1 AND f.id IN (2, 3, 4, 5, 6, 7, 8, 9)) OR
    (b.id % 8 = 2 AND f.id IN (3, 4, 5, 6, 7, 8, 9, 10)) OR
    (b.id % 8 = 3 AND f.id IN (4, 5, 6, 7, 8, 9, 10, 11)) OR
    (b.id % 8 = 4 AND f.id IN (5, 6, 7, 8, 9, 10, 11, 12)) OR
    (b.id % 8 = 5 AND f.id IN (6, 7, 8, 9, 10, 11, 12, 13)) OR
    (b.id % 8 = 6 AND f.id IN (7, 8, 9, 10, 11, 12, 13, 14)) OR
    (b.id % 8 = 7 AND f.id IN (8, 9, 10, 11, 12, 13, 14, 15))
);

-- Add features for Fine Dining (Category 7)
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 7
AND (
    (b.id % 9 = 0 AND f.id IN (1, 2, 3, 4, 5, 6, 7, 8, 9)) OR
    (b.id % 9 = 1 AND f.id IN (2, 3, 4, 5, 6, 7, 8, 9, 10)) OR
    (b.id % 9 = 2 AND f.id IN (3, 4, 5, 6, 7, 8, 9, 10, 11)) OR
    (b.id % 9 = 3 AND f.id IN (4, 5, 6, 7, 8, 9, 10, 11, 12)) OR
    (b.id % 9 = 4 AND f.id IN (5, 6, 7, 8, 9, 10, 11, 12, 13)) OR
    (b.id % 9 = 5 AND f.id IN (6, 7, 8, 9, 10, 11, 12, 13, 14)) OR
    (b.id % 9 = 6 AND f.id IN (7, 8, 9, 10, 11, 12, 13, 14, 15)) OR
    (b.id % 9 = 7 AND f.id IN (8, 9, 10, 11, 12, 13, 14, 15, 16)) OR
    (b.id % 9 = 8 AND f.id IN (9, 10, 11, 12, 13, 14, 15, 16, 17))
);

-- Add features for Cafes (Category 8)
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" = 8
AND (
    (b.id % 6 = 0 AND f.id IN (1, 2, 3, 4, 5, 6)) OR
    (b.id % 6 = 1 AND f.id IN (2, 3, 4, 5, 6, 7)) OR
    (b.id % 6 = 2 AND f.id IN (3, 4, 5, 6, 7, 8)) OR
    (b.id % 6 = 3 AND f.id IN (4, 5, 6, 7, 8, 9)) OR
    (b.id % 6 = 4 AND f.id IN (5, 6, 7, 8, 9, 10)) OR
    (b.id % 6 = 5 AND f.id IN (6, 7, 8, 9, 10, 11))
);

-- Add features for remaining categories (9-15)
INSERT INTO business_features ("businessId", "featureId")
SELECT b.id, f.id
FROM businesses b
CROSS JOIN features f
WHERE b."primaryCategoryId" IN (9, 10, 11, 12, 13, 14, 15)
AND (
    -- Clothing (9)
    (b."primaryCategoryId" = 9 AND b.id % 5 = 0 AND f.id IN (1, 2, 3, 4, 5)) OR
    (b."primaryCategoryId" = 9 AND b.id % 5 = 1 AND f.id IN (2, 3, 4, 5, 6)) OR
    (b."primaryCategoryId" = 9 AND b.id % 5 = 2 AND f.id IN (3, 4, 5, 6, 7)) OR
    (b."primaryCategoryId" = 9 AND b.id % 5 = 3 AND f.id IN (4, 5, 6, 7, 8)) OR
    (b."primaryCategoryId" = 9 AND b.id % 5 = 4 AND f.id IN (5, 6, 7, 8, 9)) OR

    -- Electronics (10)
    (b."primaryCategoryId" = 10 AND b.id % 6 = 0 AND f.id IN (1, 2, 3, 4, 5, 6)) OR
    (b."primaryCategoryId" = 10 AND b.id % 6 = 1 AND f.id IN (2, 3, 4, 5, 6, 7)) OR
    (b."primaryCategoryId" = 10 AND b.id % 6 = 2 AND f.id IN (3, 4, 5, 6, 7, 8)) OR
    (b."primaryCategoryId" = 10 AND b.id % 6 = 3 AND f.id IN (4, 5, 6, 7, 8, 9)) OR
    (b."primaryCategoryId" = 10 AND b.id % 6 = 4 AND f.id IN (5, 6, 7, 8, 9, 10)) OR
    (b."primaryCategoryId" = 10 AND b.id % 6 = 5 AND f.id IN (6, 7, 8, 9, 10, 11)) OR

    -- Hospitals (11)
    (b."primaryCategoryId" = 11 AND b.id % 4 = 0 AND f.id IN (1, 2, 3, 4)) OR
    (b."primaryCategoryId" = 11 AND b.id % 4 = 1 AND f.id IN (2, 3, 4, 5)) OR
    (b."primaryCategoryId" = 11 AND b.id % 4 = 2 AND f.id IN (3, 4, 5, 6)) OR
    (b."primaryCategoryId" = 11 AND b.id % 4 = 3 AND f.id IN (4, 5, 6, 7)) OR

    -- Pharmacies (12)
    (b."primaryCategoryId" = 12 AND b.id % 4 = 0 AND f.id IN (1, 2, 3, 4)) OR
    (b."primaryCategoryId" = 12 AND b.id % 4 = 1 AND f.id IN (2, 3, 4, 5)) OR
    (b."primaryCategoryId" = 12 AND b.id % 4 = 2 AND f.id IN (3, 4, 5, 6)) OR
    (b."primaryCategoryId" = 12 AND b.id % 4 = 3 AND f.id IN (4, 5, 6, 7)) OR

    -- Bakery (13)
    (b."primaryCategoryId" = 13 AND b.id % 5 = 0 AND f.id IN (1, 2, 3, 4, 5)) OR
    (b."primaryCategoryId" = 13 AND b.id % 5 = 1 AND f.id IN (2, 3, 4, 5, 6)) OR
    (b."primaryCategoryId" = 13 AND b.id % 5 = 2 AND f.id IN (3, 4, 5, 6, 7)) OR
    (b."primaryCategoryId" = 13 AND b.id % 5 = 3 AND f.id IN (4, 5, 6, 7, 8)) OR
    (b."primaryCategoryId" = 13 AND b.id % 5 = 4 AND f.id IN (5, 6, 7, 8, 9)) OR

    -- Tailor (14)
    (b."primaryCategoryId" = 14 AND b.id % 3 = 0 AND f.id IN (1, 2, 3)) OR
    (b."primaryCategoryId" = 14 AND b.id % 3 = 1 AND f.id IN (2, 3, 4)) OR
    (b."primaryCategoryId" = 14 AND b.id % 3 = 2 AND f.id IN (3, 4, 5)) OR

    -- Building Supply Store (15)
    (b."primaryCategoryId" = 15 AND b.id % 4 = 0 AND f.id IN (1, 2, 3, 4)) OR
    (b."primaryCategoryId" = 15 AND b.id % 4 = 1 AND f.id IN (2, 3, 4, 5)) OR
    (b."primaryCategoryId" = 15 AND b.id % 4 = 2 AND f.id IN (3, 4, 5, 6)) OR
    (b."primaryCategoryId" = 15 AND b.id % 4 = 3 AND f.id IN (4, 5, 6, 7))
);

-- Final verification queries
SELECT 'Update Summary:' as info;
SELECT 'Total businesses updated:' as info, COUNT(*) as count FROM businesses WHERE "isOpen24x7" = true;
SELECT 'Businesses by category:' as info;
SELECT c."nameEn" as category, COUNT(b.id) as business_count
FROM categories c
LEFT JOIN businesses b ON c.id = b."primaryCategoryId"
GROUP BY c.id, c."nameEn"
ORDER BY c.id;

SELECT 'Feature assignments summary:' as info;
SELECT COUNT(*) as total_feature_assignments FROM business_features;
SELECT 'Features per business (sample):' as info;
SELECT b.id, b."nameEn", COUNT(bf."featureId") as feature_count
FROM businesses b
LEFT JOIN business_features bf ON b.id = bf."businessId"
GROUP BY b.id, b."nameEn"
ORDER BY b.id
LIMIT 10;

SELECT 'Script execution completed successfully!' as status;
