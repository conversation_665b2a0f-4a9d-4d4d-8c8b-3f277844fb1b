import { IGenericService } from '@app/common';
import { CreateBusinessDto } from '../../dtos/create-business.dto';
import { GetAllBusinessDto } from '../../dtos/get-all-business.dto';
import { UpdateBusinessDto } from '../../dtos/update-business.dto';
import { Business } from '../../entities/business.entity';
import { PaymentMethod, PriceRange } from '../../enums';
import { BusinessSchema } from '../../schemes/business.schema';

export interface IBusinessService
  extends IGenericService<
    Business,
    typeof BusinessSchema,
    GetAllBusinessDto,
    CreateBusinessDto,
    UpdateBusinessDto
  > {
  findByLocationId(locationId: number): Promise<Business[]>;
  findByPrimaryCategoryId(primaryCategoryId: number): Promise<Business[]>;
  findByOwnerUserId(ownerUserId: number): Promise<Business[]>;
  findByFeatureId(featureId: number): Promise<Business[]>;
  findByPriceRange(priceRange: PriceRange): Promise<Business[]>;
  findByPaymentMethod(paymentMethod: PaymentMethod): Promise<Business[]>;
  findByRatingRange(minRating: number, maxRating?: number): Promise<Business[]>;
  findByCity(cityEn?: string, cityAr?: string): Promise<Business[]>;
  findNearby(
    latitude: number,
    longitude: number,
    radiusKm?: number,
  ): Promise<Business[]>;
  findActiveBusinesses(): Promise<Business[]>;
  findVerifiedBusinesses(): Promise<Business[]>;
  findPremiumBusinesses(): Promise<Business[]>;
  incrementViews(businessId: number): Promise<void>;
  updateBusinessRating(
    businessId: number,
    averageRating: number,
    totalReviews: number,
  ): Promise<void>;
  activateBusiness(businessId: number): Promise<Business>;
  deactivateBusiness(businessId: number): Promise<Business>;
  verifyBusiness(businessId: number): Promise<Business>;
  unverifyBusiness(businessId: number): Promise<Business>;
  upgradeToPremium(businessId: number, expiresAt: Date): Promise<Business>;
  downgradePremium(businessId: number): Promise<Business>;
  findExpiredPremiumBusinesses(): Promise<Business[]>;
  validateBusinessOwnership(
    businessId: number,
    userId: number,
  ): Promise<boolean>;
  updateCategoryBusinessCounts(): Promise<void>;
}

export const IBusinessService = Symbol('IBusinessService');
