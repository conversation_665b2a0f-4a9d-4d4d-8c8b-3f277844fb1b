import * as argon2 from 'argon2';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
} from 'typeorm';
import { v4 as uuid } from 'uuid';
import { User } from '../entities/user.entity';

@EventSubscriber()
export class UserDatabaseListener implements EntitySubscriberInterface<User> {
  constructor(dataSource: DataSource) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return User;
  }

  async beforeInsert(event: InsertEvent<User>) {
    const user = event.entity;
    console.log('user', user);
    user.password = await argon2.hash(user.password);
    user.apiKey = uuid();
  }
}
