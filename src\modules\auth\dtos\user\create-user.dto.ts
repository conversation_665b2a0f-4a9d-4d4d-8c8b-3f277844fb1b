import { ApiHideProperty, OmitType } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { DefaultRole } from '../../../../common';
import { User } from '../../entities/user.entity';

export class CreateUserDto extends OmitType(User, [
  'role',
  'createdAt',
  'updatedAt',
  'apiKey',
  'id',
  'role',
  'roleId',
] as const) {
  @ApiHideProperty()
  @IsOptional()
  role?: DefaultRole;

  @ApiHideProperty()
  @IsOptional()
  roleId?: number;

  @ApiHideProperty()
  @IsOptional()
  apiKey?: string;
}
