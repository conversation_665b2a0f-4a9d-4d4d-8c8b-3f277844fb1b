import { Transform } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';
import { GetAllDto } from '@app/common';

export class GetAllCategoryDto extends GetAllDto {
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  parentId?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  level?: number;

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  includeChildren?: boolean;
}
