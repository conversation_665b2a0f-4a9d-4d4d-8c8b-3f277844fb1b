import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCategoriesTable1748958000000 implements MigrationInterface {
  name = 'CreateCategoriesTable1748958000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "categories"
                             (
                               "id"                 SERIAL            NOT NULL,
                               "createdAt"          TIMESTAMP         NOT NULL DEFAULT now(),
                               "updatedAt"          TIMESTAMP         NOT NULL DEFAULT now(),
                               "deletedAt"          TIMESTAMP,
                               "name"               character varying NOT NULL,
                               "description"        character varying,
                               "icon"               character varying,
                               "cover"              character varying,
                               "slug"               character varying,
                               "numberOfBusinesses" integer           NOT NULL DEFAULT 0,
                               "sortOrder"          integer           NOT NULL DEFAULT 0,
                               "level"              integer           NOT NULL DEFAULT 0,
                               "path"               character varying,
                               "parentId"           integer,
                               CONSTRAINT "PK_24dbc6126a28ff948da33e97d3b" PRIMARY KEY ("id")
                             )`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "categories_slug_unique" ON "categories" ("slug")`,
    );
    await queryRunner.query(
      `CREATE INDEX "categories_parent_id_index" ON "categories" ("parentId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "categories_level_index" ON "categories" ("level")`,
    );
    await queryRunner.query(
      `CREATE INDEX "categories_sort_order_index" ON "categories" ("sortOrder")`,
    );
    await queryRunner.query(`ALTER TABLE "categories"
      ADD CONSTRAINT "FK_9a6f051e66982b5f87318aa4e23" FOREIGN KEY ("parentId") REFERENCES "categories" ("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "categories" DROP CONSTRAINT "FK_9a6f051e66982b5f87318aa4e23"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."categories_sort_order_index"`,
    );
    await queryRunner.query(`DROP INDEX "public"."categories_level_index"`);
    await queryRunner.query(`DROP INDEX "public"."categories_parent_id_index"`);
    await queryRunner.query(`DROP INDEX "public"."categories_slug_unique"`);
    await queryRunner.query(`DROP TABLE "categories"`);
  }
}
