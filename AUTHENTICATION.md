# Authentication & Authorization Implementation

This document describes the JWT-based authentication and role-based authorization system implemented in the Qareeb backend.

## Overview

The authentication system uses:
- **JWT (JSON Web Tokens)** for stateless authentication
- **Argon2** for password hashing
- **Passport.js** with JWT strategy
- **Role-Based Access Control (RBAC)** with permissions

## Key Components

### 1. Authentication Flow

#### Registration
- POST `/api/v1/auth/register`
- Creates a new user with default "USER" role
- Returns JWT tokens (access & refresh)

#### Login
- POST `/api/v1/auth/login`
- Validates credentials
- Returns JWT tokens (access & refresh)

#### Token Refresh
- POST `/api/v1/auth/refresh`
- Exchanges refresh token for new access token

### 2. Guards

#### JwtAuthGuard
- Global guard that protects all routes by default
- Routes can be made public using `@PublicMeta()` decorator

#### RolesGuard
- Checks if user has required roles
- Use with `@Roles('ADMIN')` decorator

#### PermissionsGuard
- Checks if user has required permissions
- Use with `@Permissions('users.read')` decorator

### 3. Decorators

- `@CurrentUser()` - Injects current authenticated user into controller method
- `@Roles(...roles)` - Specifies required roles for a route
- `@Permissions(...permissions)` - Specifies required permissions for a route
- `@PublicMeta()` - Makes a route publicly accessible (no auth required)

## Environment Variables

Add these to your `.env` file:

```env
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRATION=1h
JWT_REFRESH_TOKEN_EXPIRATION=7d
```

## Usage Examples

### Protected Route (Default)
```typescript
@Get('profile')
async getProfile(@CurrentUser() user: any) {
  // Route is automatically protected by global JwtAuthGuard
  return user;
}
```

### Public Route
```typescript
@Post('login')
@PublicMeta()
async login(@Body() dto: LoginDto) {
  // This route is publicly accessible
  return this.authService.login(dto);
}
```

### Role-Protected Route
```typescript
@Get('admin-only')
@Roles('ADMIN')
async adminOnly() {
  // Only users with ADMIN role can access
  return { message: 'Admin content' };
}
```

### Permission-Protected Route
```typescript
@Delete(':id')
@Permissions('users.delete')
async deleteUser(@Param('id') id: string) {
  // Only users with users.delete permission can access
  return this.userService.delete(id);
}
```

## Default Roles & Permissions

The system includes a migration that seeds:

### Roles
- **ADMIN** - Full access to all resources
- **USER** - Basic read access

### Permissions
Each resource (users, roles, permissions) has CRUD permissions:
- `{resource}.read`
- `{resource}.create`
- `{resource}.update`
- `{resource}.delete`

## API Documentation

All authentication endpoints are documented in Swagger at `/api-docs`.

## Security Notes

1. **Change JWT secrets** in production
2. **Use HTTPS** in production to protect tokens in transit
3. **Implement token blacklisting** for logout functionality if needed
4. **Consider implementing rate limiting** on auth endpoints
5. **Monitor failed login attempts** for security