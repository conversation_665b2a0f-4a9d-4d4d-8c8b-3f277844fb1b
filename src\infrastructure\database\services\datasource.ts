import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { config as dotenvConfig } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';

export function loadTypeOrmConfig(): TypeOrmModuleOptions {
  dotenvConfig({ path: `./.env` });

  return {
    type: 'postgres',
    url: process.env.DATABASE_URL,
    autoLoadEntities: true,
    synchronize: false,
    logger: 'simple-console',
    logging: 'all',
  };
}

const config = loadTypeOrmConfig();

export const connectionSource = new DataSource({
  ...config,
  entities: ['./src/modules/**/*.entity.ts', './src/modules/**/*.schema.ts'],
  migrations: ['./src/infrastructure/database/migrations/*.ts'],
  migrationsTableName: 'migrations',
  migrationsTransactionMode: 'all',
} as DataSourceOptions);
console.log('connectionSource', connectionSource);
