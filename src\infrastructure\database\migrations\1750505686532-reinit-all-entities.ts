import { MigrationInterface, QueryRunner } from 'typeorm';

export class ReinitAllEntities1750505686532 implements MigrationInterface {
  name = 'ReinitAllEntities1750505686532';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "locations" DROP CONSTRAINT "FK_locations_parent"`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" DROP CONSTRAINT "FK_9a6f051e66982b5f87318aa4e23"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT "FK_businesses_location"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT "FK_businesses_category"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT "FK_businesses_owner"`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" DROP CONSTRAINT "FK_business_features_business"`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" DROP CONSTRAINT "FK_business_features_feature"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."businesses_active_premium_index"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."businesses_location_active_index"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."businesses_category_active_index"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."businesses_active_verified_index"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."business_features_business_id_index"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."business_features_feature_id_index"`,
    );
    await queryRunner.query(
      `ALTER TABLE "locations" ADD "deletedAt" TIMESTAMP`,
    );
    await queryRunner.query(`ALTER TABLE "features" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD "deletedAt" TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "locations" ADD CONSTRAINT "UQ_e223e9f324b8863e09039ed4428" UNIQUE ("placeId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ALTER COLUMN "nameEn" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ALTER COLUMN "nameAr" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD CONSTRAINT "UQ_420d9f679d41281f282f5bc7d09" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dfa460354cee4538b6ba745d4e" ON "business_features" ("businessId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a08f45f0b5f8eed4982089a57c" ON "business_features" ("featureId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "locations" ADD CONSTRAINT "FK_9f238930bae84c7eafad3785d7b" FOREIGN KEY ("parentId") REFERENCES "locations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD CONSTRAINT "FK_9a6f051e66982b5f0318981bcaa" FOREIGN KEY ("parentId") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_e50599142704b6eaee8f87d56aa" FOREIGN KEY ("locationId") REFERENCES "locations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_f9ac8da04aa24df37c3053b444b" FOREIGN KEY ("primaryCategoryId") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_cc99d9f12bfc4456c2a7e8b45f7" FOREIGN KEY ("ownerUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" ADD CONSTRAINT "FK_dfa460354cee4538b6ba745d4e2" FOREIGN KEY ("businessId") REFERENCES "businesses"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" ADD CONSTRAINT "FK_a08f45f0b5f8eed4982089a57c5" FOREIGN KEY ("featureId") REFERENCES "features"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "business_features" DROP CONSTRAINT "FK_a08f45f0b5f8eed4982089a57c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" DROP CONSTRAINT "FK_dfa460354cee4538b6ba745d4e2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT "FK_cc99d9f12bfc4456c2a7e8b45f7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT "FK_f9ac8da04aa24df37c3053b444b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" DROP CONSTRAINT "FK_e50599142704b6eaee8f87d56aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" DROP CONSTRAINT "FK_9a6f051e66982b5f0318981bcaa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "locations" DROP CONSTRAINT "FK_9f238930bae84c7eafad3785d7b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a08f45f0b5f8eed4982089a57c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_dfa460354cee4538b6ba745d4e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" DROP CONSTRAINT "UQ_420d9f679d41281f282f5bc7d09"`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ALTER COLUMN "nameAr" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ALTER COLUMN "nameEn" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "locations" DROP CONSTRAINT "UQ_e223e9f324b8863e09039ed4428"`,
    );
    await queryRunner.query(`ALTER TABLE "businesses" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "features" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "locations" DROP COLUMN "deletedAt"`);
    await queryRunner.query(
      `CREATE INDEX "business_features_feature_id_index" ON "business_features" ("featureId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "business_features_business_id_index" ON "business_features" ("businessId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "businesses_active_verified_index" ON "businesses" ("isActive", "isVerified") `,
    );
    await queryRunner.query(
      `CREATE INDEX "businesses_category_active_index" ON "businesses" ("primaryCategoryId", "isActive") `,
    );
    await queryRunner.query(
      `CREATE INDEX "businesses_location_active_index" ON "businesses" ("locationId", "isActive") `,
    );
    await queryRunner.query(
      `CREATE INDEX "businesses_active_premium_index" ON "businesses" ("isActive", "isPremium") `,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" ADD CONSTRAINT "FK_business_features_feature" FOREIGN KEY ("featureId") REFERENCES "features"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_features" ADD CONSTRAINT "FK_business_features_business" FOREIGN KEY ("businessId") REFERENCES "businesses"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_businesses_owner" FOREIGN KEY ("ownerUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_businesses_category" FOREIGN KEY ("primaryCategoryId") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "businesses" ADD CONSTRAINT "FK_businesses_location" FOREIGN KEY ("locationId") REFERENCES "locations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD CONSTRAINT "FK_9a6f051e66982b5f87318aa4e23" FOREIGN KEY ("parentId") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "locations" ADD CONSTRAINT "FK_locations_parent" FOREIGN KEY ("parentId") REFERENCES "locations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
