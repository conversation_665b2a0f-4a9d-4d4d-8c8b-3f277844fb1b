import { IGenericRepository } from '@app/common';
import { Category } from '../../entities/category.entity';
import { CategorySchema } from '../../schemes/category.schema';

export interface ICategoryRepository
  extends IGenericRepository<Category, typeof CategorySchema> {
  findByParentId(parentId: number | null): Promise<Category[]>;
  findByLevel(level: number): Promise<Category[]>;
  findWithChildren(id: number): Promise<Category | null>;
  findHierarchy(): Promise<Category[]>;
  updateNumberOfBusinesses(id: number, count: number): Promise<void>;
}

export const ICategoryRepository = Symbol('ICategoryRepository');
