import { IGetAllResponseInterface } from '@app/common';
import { Public } from '@app/common/decorators';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseFloatPipe,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { CreateBusinessDto } from '../dtos/create-business.dto';
import { CreatePublicBusinessDto } from '../dtos/create-public-business.dto';
import { GetAllBusinessDto } from '../dtos/get-all-business.dto';
import { UpdateBusinessDto } from '../dtos/update-business.dto';
import { Business } from '../entities/business.entity';
import { IBusinessService } from '../interfaces/business-service/business-service.interface';

@Controller('businesses')
@ApiTags('Businesses')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class BusinessController {
  constructor(
    @Inject(IBusinessService)
    private readonly businessService: IBusinessService,
  ) {}

  @Post('public')
  @ApiOperation({ summary: 'Publicly create a new business' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The business has been successfully created.',
    type: Business,
  })
  @HttpCode(HttpStatus.CREATED)
  @Public()
  async createPublic(
    @Body() createDto: CreatePublicBusinessDto,
  ): Promise<Business> {
    return this.businessService.create(Business, {
      ...createDto,
      isActive: false,
      isVerified: false,
      isPremium: false,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all businesses with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  @Public()
  async getAll(
    @Query() getAllDto: GetAllBusinessDto,
  ): Promise<IGetAllResponseInterface<Business>> {
    return this.businessService.getAll(getAllDto);
  }

  @Get('location/:locationId')
  @ApiOperation({ summary: 'Get businesses by location ID' })
  @ApiParam({
    name: 'locationId',
    description: 'Location ID',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getByLocationId(
    @Param('locationId', ParseIntPipe) locationId: number,
  ): Promise<Business[]> {
    return this.businessService.findByLocationId(locationId);
  }

  @Get('category/:categoryId')
  @ApiOperation({ summary: 'Get businesses by primary category ID' })
  @ApiParam({
    name: 'categoryId',
    description: 'Category ID',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  @Public()
  async getByPrimaryCategoryId(
    @Param('categoryId', ParseIntPipe) categoryId: number,
  ): Promise<Business[]> {
    return this.businessService.findByPrimaryCategoryId(categoryId);
  }

  @Get('owner/:ownerId')
  @ApiOperation({ summary: 'Get businesses by owner user ID' })
  @ApiParam({
    name: 'ownerId',
    description: 'Owner User ID',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getByOwnerUserId(
    @Param('ownerId', ParseIntPipe) ownerId: number,
  ): Promise<Business[]> {
    return this.businessService.findByOwnerUserId(ownerId);
  }

  @Get('feature/:featureId')
  @ApiOperation({ summary: 'Get businesses that have a specific feature' })
  @ApiParam({
    name: 'featureId',
    description: 'Feature ID',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getByFeatureId(
    @Param('featureId', ParseIntPipe) featureId: number,
  ): Promise<Business[]> {
    return this.businessService.findByFeatureId(featureId);
  }

  @Get('rating/:minRating')
  @ApiOperation({ summary: 'Get businesses by minimum rating' })
  @ApiParam({
    name: 'minRating',
    description: 'Minimum rating',
    type: Number,
  })
  @ApiQuery({
    name: 'maxRating',
    description: 'Maximum rating',
    type: Number,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getByRatingRange(
    @Param('minRating', ParseFloatPipe) minRating: number,
    @Query('maxRating') maxRating?: number,
  ): Promise<Business[]> {
    const maxRatingParsed = maxRating
      ? parseFloat(maxRating.toString())
      : undefined;
    return this.businessService.findByRatingRange(minRating, maxRatingParsed);
  }

  @Get('city')
  @ApiOperation({ summary: 'Get businesses by city' })
  @ApiQuery({
    name: 'cityEn',
    description: 'City name in English',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'cityAr',
    description: 'City name in Arabic',
    type: String,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getByCity(
    @Query('cityEn') cityEn?: string,
    @Query('cityAr') cityAr?: string,
  ): Promise<Business[]> {
    return this.businessService.findByCity(cityEn, cityAr);
  }

  @Get('nearby/:latitude/:longitude')
  @ApiOperation({ summary: 'Get businesses near coordinates' })
  @ApiParam({
    name: 'latitude',
    description: 'Latitude coordinate',
    type: Number,
  })
  @ApiParam({
    name: 'longitude',
    description: 'Longitude coordinate',
    type: Number,
  })
  @ApiQuery({
    name: 'radiusKm',
    description: 'Search radius in kilometers',
    type: Number,
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getNearby(
    @Param('latitude', ParseFloatPipe) latitude: number,
    @Param('longitude', ParseFloatPipe) longitude: number,
    @Query('radiusKm') radiusKm?: number,
  ): Promise<Business[]> {
    const radiusParsed = radiusKm ? parseFloat(radiusKm.toString()) : undefined;
    return this.businessService.findNearby(latitude, longitude, radiusParsed);
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active businesses' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The active businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getActiveBusinesses(): Promise<Business[]> {
    return this.businessService.findActiveBusinesses();
  }

  @Get('verified')
  @ApiOperation({ summary: 'Get all verified businesses' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The verified businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  async getVerifiedBusinesses(): Promise<Business[]> {
    return this.businessService.findVerifiedBusinesses();
  }

  @Get('premium')
  @ApiOperation({ summary: 'Get all premium businesses' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The premium businesses have been successfully retrieved.',
    type: Business,
    isArray: true,
  })
  @Public()
  async getPremiumBusinesses(): Promise<Business[]> {
    return this.businessService.findPremiumBusinesses();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get business by ID' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully retrieved.',
    type: Business,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Business not found.',
  })
  @Public()
  async getById(@Param('id', ParseIntPipe) id: number): Promise<Business> {
    const business = await this.businessService.findOneById(id);
    // Increment view count when business is accessed
    await this.businessService.incrementViews(id);
    return business;
  }

  @Post()
  @ApiOperation({ summary: 'Create new business' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The business has been successfully created.',
    type: Business,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDto: CreateBusinessDto): Promise<Business> {
    return this.businessService.create(Business, createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an existing business' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully updated.',
    type: Business,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Business not found.',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateBusinessDto,
  ): Promise<Business> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.businessService.update(Business, updateDtoWithId);
  }

  @Patch(':id/activate')
  @ApiOperation({ summary: 'Activate a business' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully activated.',
    type: Business,
  })
  async activate(@Param('id', ParseIntPipe) id: number): Promise<Business> {
    return this.businessService.activateBusiness(id);
  }

  @Patch(':id/deactivate')
  @ApiOperation({ summary: 'Deactivate a business' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully deactivated.',
    type: Business,
  })
  async deactivate(@Param('id', ParseIntPipe) id: number): Promise<Business> {
    return this.businessService.deactivateBusiness(id);
  }

  @Patch(':id/verify')
  @ApiOperation({ summary: 'Verify a business' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully verified.',
    type: Business,
  })
  async verify(@Param('id', ParseIntPipe) id: number): Promise<Business> {
    return this.businessService.verifyBusiness(id);
  }

  @Patch(':id/unverify')
  @ApiOperation({ summary: 'Unverify a business' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully unverified.',
    type: Business,
  })
  async unverify(@Param('id', ParseIntPipe) id: number): Promise<Business> {
    return this.businessService.unverifyBusiness(id);
  }

  @Patch(':id/upgrade-premium')
  @ApiOperation({ summary: 'Upgrade business to premium' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully upgraded to premium.',
    type: Business,
  })
  async upgradePremium(
    @Param('id', ParseIntPipe) id: number,
    @Body('expiresAt') expiresAt: string,
  ): Promise<Business> {
    return this.businessService.upgradeToPremium(id, new Date(expiresAt));
  }

  @Patch(':id/downgrade-premium')
  @ApiOperation({ summary: 'Downgrade business from premium' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The business has been successfully downgraded from premium.',
    type: Business,
  })
  async downgradePremium(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Business> {
    return this.businessService.downgradePremium(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a business' })
  @ApiParam({ name: 'id', description: 'Business ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The business has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Business not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.businessService.deleteOne(id);
  }
}
