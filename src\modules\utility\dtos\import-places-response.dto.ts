import { ApiProperty } from '@nestjs/swagger';

export class ImportPlacesResponseDto {
  @ApiProperty({
    description: 'Total number of places processed from Google Places API',
    example: 150,
  })
  totalProcessed: number;

  @ApiProperty({
    description: 'Number of new business records created',
    example: 45,
  })
  businessesCreated: number;

  @ApiProperty({
    description: 'Number of new location records created',
    example: 42,
  })
  locationsCreated: number;

  @ApiProperty({
    description: 'Number of new feature records created',
    example: 8,
  })
  featuresCreated: number;

  @ApiProperty({
    description: 'Array of error messages encountered during import',
    example: ['Failed to create business for place ID: ChIJ...', 'Invalid location data for place: ...'],
    type: [String],
  })
  errors: string[];
}
