import { GenericRepository } from '@app/common';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThanOrEqual, MoreThanOrEqual, Raw, Repository } from 'typeorm';
import { Business } from '../entities/business.entity';
import { PaymentMethod, PriceRange } from '../enums';
import { IBusinessRepository } from '../interfaces/business-repository/business-repository.interface';
import { BusinessSchema } from '../schemes/business.schema';

@Injectable()
export class BusinessRepository
  extends GenericRepository<Business, typeof BusinessSchema>
  implements IBusinessRepository
{
  constructor(
    @InjectRepository(BusinessSchema)
    private readonly businessRepository: Repository<Business>,
  ) {
    super(businessRepository);
  }

  async findByLocationId(locationId: number): Promise<Business[]> {
    return this.businessRepository.find({
      where: { locationId, isActive: true },
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async findByPrimaryCategoryId(
    primaryCategoryId: number,
  ): Promise<Business[]> {
    return this.businessRepository.find({
      where: { primaryCategoryId, isActive: true },
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async findByOwnerUserId(ownerUserId: number): Promise<Business[]> {
    return this.businessRepository.find({
      where: { ownerUserId },
      order: { createdAt: 'DESC' },
    });
  }

  async findByFeatureId(featureId: number): Promise<Business[]> {
    return this.businessRepository
      .createQueryBuilder('business')
      .leftJoinAndSelect('business.features', 'feature')
      .where('business.isActive = :isActive', { isActive: true })
      .andWhere('feature.id = :featureId', { featureId })
      .orderBy('business.averageRating', 'DESC')
      .addOrderBy('business.totalReviewsCount', 'DESC')
      .getMany();
  }

  async findByPriceRange(priceRange: PriceRange): Promise<Business[]> {
    return this.businessRepository.find({
      where: { priceRange, isActive: true },
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async findByPaymentMethod(paymentMethod: PaymentMethod): Promise<Business[]> {
    return this.businessRepository
      .createQueryBuilder('business')
      .where('business.isActive = :isActive', { isActive: true })
      .andWhere('business.paymentMethods @> :paymentMethod', {
        paymentMethod: JSON.stringify([paymentMethod]),
      })
      .orderBy('business.averageRating', 'DESC')
      .addOrderBy('business.totalReviewsCount', 'DESC')
      .getMany();
  }

  async findByRatingRange(
    minRating: number,
    maxRating?: number,
  ): Promise<Business[]> {
    const whereCondition: any = {
      averageRating: MoreThanOrEqual(minRating),
      isActive: true,
    };

    if (maxRating !== undefined) {
      whereCondition.averageRating = Raw(
        (alias) => `${alias} BETWEEN :minRating AND :maxRating`,
        { minRating, maxRating },
      );
    }

    return this.businessRepository.find({
      where: whereCondition,
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async findByCity(cityEn?: string, cityAr?: string): Promise<Business[]> {
    const queryBuilder = this.businessRepository
      .createQueryBuilder('business')
      .leftJoinAndSelect('business.location', 'location')
      .where('business.isActive = :isActive', { isActive: true });

    if (cityEn) {
      queryBuilder.andWhere('location.cityEn = :cityEn', { cityEn });
    }

    if (cityAr) {
      queryBuilder.andWhere('location.cityAr = :cityAr', { cityAr });
    }

    return queryBuilder
      .orderBy('business.averageRating', 'DESC')
      .addOrderBy('business.totalReviewsCount', 'DESC')
      .getMany();
  }

  async findNearby(
    latitude: number,
    longitude: number,
    radiusKm = 10,
  ): Promise<Business[]> {
    return this.businessRepository
      .createQueryBuilder('business')
      .leftJoinAndSelect('business.location', 'location')
      .where('business.isActive = :isActive', { isActive: true })
      .andWhere(
        `(6371 * acos(cos(radians(:lat)) * cos(radians(location.latitude)) * cos(radians(location.longitude) - radians(:lng)) + sin(radians(:lat)) * sin(radians(location.latitude)))) <= :radius`,
        { lat: latitude, lng: longitude, radius: radiusKm },
      )
      .orderBy(
        `(6371 * acos(cos(radians(:lat)) * cos(radians(location.latitude)) * cos(radians(location.longitude) - radians(:lng)) + sin(radians(:lat)) * sin(radians(location.latitude))))`,
        'ASC',
      )
      .setParameters({ lat: latitude, lng: longitude })
      .getMany();
  }

  async findActiveBusinesses(): Promise<Business[]> {
    return this.businessRepository.find({
      where: { isActive: true },
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async findVerifiedBusinesses(): Promise<Business[]> {
    return this.businessRepository.find({
      where: { isVerified: true, isActive: true },
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async findPremiumBusinesses(): Promise<Business[]> {
    return this.businessRepository.find({
      where: {
        isPremium: true,
        isActive: true,
        premiumExpiresAt: MoreThanOrEqual(new Date()),
      },
      order: { averageRating: 'DESC', totalReviewsCount: 'DESC' },
    });
  }

  async updateViewsCount(businessId: number): Promise<void> {
    await this.businessRepository.increment(
      { id: businessId },
      'totalViewsCount',
      1,
    );
    await this.businessRepository.increment(
      { id: businessId },
      'lastMonthViews',
      1,
    );
  }

  async updateRating(
    businessId: number,
    averageRating: number,
    totalReviews: number,
  ): Promise<void> {
    await this.businessRepository.update(businessId, {
      averageRating,
      totalReviewsCount: totalReviews,
    });
  }

  async findExpiredPremiumBusinesses(): Promise<Business[]> {
    return this.businessRepository.find({
      where: {
        isPremium: true,
        premiumExpiresAt: LessThanOrEqual(new Date()),
      },
    });
  }

  async createBusinessWithFeatures(
    business: Business,
    featureIds?: number[],
  ): Promise<Business> {
    return this.businessRepository.manager.transaction(async (manager) => {
      // Create the business first
      const savedBusiness = await manager.save(business);

      // If feature IDs are provided, associate them with the business
      if (featureIds && featureIds.length > 0) {
        // Insert into the junction table
        const businessFeatureValues = featureIds.map((featureId) => ({
          businessId: savedBusiness.id,
          featureId,
        }));

        await manager
          .createQueryBuilder()
          .insert()
          .into('business_features')
          .values(businessFeatureValues)
          .execute();
      }

      return savedBusiness;
    });
  }

  async updateBusinessWithFeatures(
    business: Business,
    featureIds?: number[],
  ): Promise<Business> {
    return this.businessRepository.manager.transaction(async (manager) => {
      // Update the business first
      const savedBusiness = await manager.save(business);

      // Remove existing feature associations
      await manager
        .createQueryBuilder()
        .delete()
        .from('business_features')
        .where('businessId = :businessId', { businessId: savedBusiness.id })
        .execute();

      // If feature IDs are provided, associate them with the business
      if (featureIds && featureIds.length > 0) {
        // Insert new associations into the junction table
        const businessFeatureValues = featureIds.map((featureId) => ({
          businessId: savedBusiness.id,
          featureId,
        }));

        await manager
          .createQueryBuilder()
          .insert()
          .into('business_features')
          .values(businessFeatureValues)
          .execute();
      }

      return savedBusiness;
    });
  }

  async incrementCategoryBusinessCount(categoryId: number): Promise<void> {
    await this.businessRepository.manager.query(
      `UPDATE categories SET "numberOfBusinesses" = "numberOfBusinesses" + 1 WHERE id = $1`,
      [categoryId],
    );
  }

  async decrementCategoryBusinessCount(categoryId: number): Promise<void> {
    await this.businessRepository.manager.query(
      `UPDATE categories SET "numberOfBusinesses" = GREATEST("numberOfBusinesses" - 1, 0) WHERE id = $1`,
      [categoryId],
    );
  }

  async updateCategoryBusinessCount(
    categoryId: number,
    count: number,
  ): Promise<void> {
    await this.businessRepository.manager.query(
      `UPDATE categories SET "numberOfBusinesses" = $1 WHERE id = $2`,
      [count, categoryId],
    );
  }
}
