import { GenericService, IGetAllResponseInterface } from '@app/common';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CheckRoleContainsPermissionDto } from '../dtos/role/check-role-contains-permission.dto';
import { CreateRoleDto } from '../dtos/role/create-role.dto';
import { GetAllRoleDto } from '../dtos/role/get-all-role.dto';
import { UpdateRoleDto } from '../dtos/role/update-role.dto';
import { Permission } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';
import { IRoleRepository } from '../interfaces/role/role-repository.interface';
import { IRoleService } from '../interfaces/role/role-service.interface';
import { RoleSchema } from '../schemes/role.schema';
import { EntityManager, FindManyOptions } from 'typeorm';
import { IPermissionService } from '../interfaces/permission/permission-service.interface';

@Injectable()
export class RoleService
  extends GenericService<
    Role,
    typeof RoleSchema,
    GetAllRoleDto,
    CreateRoleDto,
    UpdateRoleDto
  >
  implements IRoleService
{
  constructor(
    @Inject(IRoleRepository) private readonly roleRepository: IRoleRepository,
    @Inject(IPermissionService)
    private readonly permissionService: IPermissionService,
  ) {
    super(roleRepository);
  }

  async getAll(
    getAllDto: GetAllRoleDto,
    initialQuery?: FindManyOptions<Role>,
  ): Promise<IGetAllResponseInterface<Role>> {
    if (getAllDto.searchKey) getAllDto.searchOnJson = 'name';
    return super.getAll(getAllDto, initialQuery);
  }

  async update(
    entityType: new () => Role,
    updateDto: UpdateRoleDto,
    entityManager?: EntityManager,
  ): Promise<Role> {
    const role = await this.findOneWithPermissions(updateDto.id);
    if (!role) {
      throw new NotFoundException(`Role with ID "${updateDto.id}" not found.`);
    }
    if (updateDto.permissions && updateDto.permissions.length > 0) {
      const permissions = await Promise.all(
        updateDto.permissions.map((permissionId) =>
          this.permissionService.findOneById(permissionId),
        ),
      );
      role.permissions = permissions;
    }
    return super.update(entityType, updateDto, entityManager);
  }

  async create(
    entityType: new () => Role,
    createDto: CreateRoleDto,
    entityManager?: EntityManager,
  ): Promise<Role> {
    if (createDto.permissions && createDto.permissions.length > 0) {
      await Promise.all(
        createDto.permissions.map((permissionId) =>
          this.permissionService.findOneById(permissionId),
        ),
      );
    }
    return super.create(entityType, createDto, entityManager);
  }

  async checkIfContainsPermission({
    roleId,
    action,
  }: CheckRoleContainsPermissionDto): Promise<boolean> {
    const role = await this.findOneWithPermissions(roleId);
    if (!role.permissions) {
      return false;
    }
    return role.permissions.some(
      (permission: Permission) => permission.action === action,
    );
  }
  async addNewPermission(
    role: Role,
    permission: Permission,
    manager?: EntityManager,
  ): Promise<Role | null> {
    return await this.roleRepository.addNewPermission(
      role,
      permission,
      manager,
    );
  }
  async findByName(name: string): Promise<Role> {
    const role = await this.roleRepository.findByName(name);
    if (!role) {
      throw new NotFoundException(`Role with name "${name}" not found.`);
    }
    return role;
  }

  async findOneWithPermissions(id: number): Promise<Role> {
    const role = await this.roleRepository.findOneWithPermissions(id);
    if (!role) {
      throw new NotFoundException(`Role with ID "${id}" not found.`);
    }
    return role;
  }
}
