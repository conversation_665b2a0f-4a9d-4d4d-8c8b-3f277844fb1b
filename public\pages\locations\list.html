<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المواقع - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pagination.css">
    
    <style>
        .locations-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .search-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .location-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .location-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .location-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .location-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-gradient);
            border-radius: 12px;
            color: white;
        }
        
        .location-info h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .location-info p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .location-details {
            margin-bottom: 20px;
        }
        
        .location-detail {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .location-detail i {
            width: 16px;
            color: var(--primary-color);
        }
        
        .coordinates {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.85rem;
            margin-bottom: 15px;
            direction: ltr;
            text-align: left;
        }
        
        .location-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }
        
        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
            display: block;
        }
        
        @media (max-width: 768px) {
            .locations-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .locations-grid {
                grid-template-columns: 1fr;
            }
            
            .location-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">المواقع</h1>
            <p class="page-subtitle">إدارة وعرض جميع المواقع الجغرافية</p>
        </div>
        
        <div class="locations-header">
            <div class="search-filters">
                <input type="text" id="searchInput" class="search-input" placeholder="البحث في المواقع..." dir="rtl" style="padding: 10px 15px; border: 2px solid #e1e8ed; border-radius: 8px; font-size: 14px; min-width: 250px;">
                <select id="cityFilter" class="search-input" style="padding: 10px 15px; border: 2px solid #e1e8ed; border-radius: 8px; font-size: 14px;">
                    <option value="">جميع المدن</option>
                </select>
                <select id="countryFilter" class="search-input" style="padding: 10px 15px; border: 2px solid #e1e8ed; border-radius: 8px; font-size: 14px;">
                    <option value="">جميع البلدان</option>
                </select>
            </div>
            
            <a href="form.html" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة موقع جديد
            </a>
        </div>
        
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p>جاري تحميل المواقع...</p>
        </div>
        
        <div id="locationsContainer" class="locations-grid" style="display: none;">
            <!-- Locations will be loaded here -->
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-map-marker-alt"></i>
            <h3>لا توجد مواقع</h3>
            <p>لم يتم العثور على أي مواقع مطابقة للبحث</p>
            <a href="form.html" class="btn btn-primary" style="margin-top: 20px;">
                <i class="fas fa-plus"></i>
                إضافة أول موقع
            </a>
        </div>

        <!-- Pagination Container -->
        <div id="paginationContainer" style="margin-top: 30px;"></div>

        <div class="response-message" id="responseMessage"></div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script src="../../assets/js/pagination.js"></script>
    <script>
        let locations = [];
        let currentSearchTerm = '';
        let currentCityFilter = '';
        let currentCountryFilter = '';
        let pagination = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePagination();
            loadLocations();
            setupEventListeners();
        });

        function initializePagination() {
            pagination = new PaginationManager({
                containerId: 'paginationContainer',
                pageSize: 12,
                onPageChange: (page, offset) => {
                    loadLocations(offset, pagination.pageSize);
                },
                onPageSizeChange: (pageSize) => {
                    loadLocations(0, pageSize);
                },
                showPageSizeSelector: true,
                pageSizeOptions: [6, 12, 24, 48]
            });
        }

        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            const cityFilter = document.getElementById('cityFilter');
            const countryFilter = document.getElementById('countryFilter');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearchTerm = this.value.trim();
                    loadLocations(0, pagination.pageSize);
                }, 300);
            });

            cityFilter.addEventListener('change', function() {
                currentCityFilter = this.value;
                loadLocations(0, pagination.pageSize);
            });

            countryFilter.addEventListener('change', function() {
                currentCountryFilter = this.value;
                loadLocations(0, pagination.pageSize);
            });
        }

        async function loadLocations(offset = 0, limit = 12) {
            const loadingContainer = document.getElementById('loadingContainer');
            const locationsContainer = document.getElementById('locationsContainer');
            const emptyState = document.getElementById('emptyState');

            try {
                loadingContainer.style.display = 'block';
                locationsContainer.style.display = 'none';
                emptyState.style.display = 'none';

                if (pagination) {
                    pagination.setLoading(true);
                }

                // Build query parameters
                const params = new URLSearchParams({
                    offset: offset.toString(),
                    limit: limit.toString()
                });

                if (currentSearchTerm) {
                    params.append('searchKey', currentSearchTerm);
                }

                if (currentCityFilter) {
                    params.append('cityAr', currentCityFilter);
                }

                if (currentCountryFilter) {
                    params.append('countryAr', currentCountryFilter);
                }

                const response = await fetch(`/v1/locations?${params.toString()}`, {
                    headers: AuthUtils.getAuthHeaders()
                });

                if (response.ok) {
                    const result = await response.json();
                    // Handle different response formats
                    if (result.data) {
                        // Format: { data: { items: [], count: number } }
                        locations = result.data.items || [];
                        const totalCount = result.data.count || result.data.totalCount || locations.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    } else {
                        // Direct format: { items: [], count: number }
                        locations = result.items || result;
                        const totalCount = result.count || locations.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    }

                    if (locations.length === 0) {
                        emptyState.style.display = 'block';
                    } else {
                        displayLocations(locations);
                        locationsContainer.style.display = 'grid';
                        populateFilters(locations);
                    }
                } else {
                    throw new Error('Failed to load locations');
                }
            } catch (error) {
                console.error('Error loading locations:', error);
                showMessage('فشل في تحميل المواقع', 'error');
                emptyState.style.display = 'block';

                if (pagination) {
                    pagination.updateData(0);
                }
            } finally {
                loadingContainer.style.display = 'none';
                if (pagination) {
                    pagination.setLoading(false);
                }
            }
        }

        function displayLocations(locationsToShow) {
            const container = document.getElementById('locationsContainer');
            container.innerHTML = '';

            locationsToShow.forEach(location => {
                const locationCard = createLocationCard(location);
                container.appendChild(locationCard);
            });
        }

        function createLocationCard(location) {
            const card = document.createElement('div');
            card.className = 'location-card';

            const address = [
                location.streetAddressAr,
                location.cityAr,
                location.stateAr,
                location.countryAr
            ].filter(Boolean).join(', ');

            card.innerHTML = `
                <div class="location-header">
                    <div class="location-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="location-info">
                        <h3>${location.nameAr}</h3>
                        <p>${location.nameEn || ''}</p>
                    </div>
                </div>

                <div class="coordinates">
                    ${location.latitude}, ${location.longitude}
                </div>

                <div class="location-details">
                    ${address ? `
                        <div class="location-detail">
                            <i class="fas fa-home"></i>
                            <span>${address}</span>
                        </div>
                    ` : ''}
                    ${location.nearestLandmarkAr ? `
                        <div class="location-detail">
                            <i class="fas fa-landmark"></i>
                            <span>${location.nearestLandmarkAr}</span>
                        </div>
                    ` : ''}
                    ${location.mapUrl ? `
                        <div class="location-detail">
                            <i class="fas fa-external-link-alt"></i>
                            <a href="${location.mapUrl}" target="_blank" style="color: var(--primary-color);">عرض على الخريطة</a>
                        </div>
                    ` : ''}
                </div>

                <div class="location-actions">
                    <a href="edit.html?id=${location.id}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <button class="btn btn-danger btn-sm" onclick="deleteLocation(${location.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            `;

            return card;
        }

        function populateFilters(locationsData) {
            const cityFilter = document.getElementById('cityFilter');
            const countryFilter = document.getElementById('countryFilter');

            // Get unique cities and countries
            const cities = [...new Set(locationsData.map(loc => loc.cityAr).filter(Boolean))];
            const countries = [...new Set(locationsData.map(loc => loc.countryAr).filter(Boolean))];

            // Clear existing options (except first one)
            cityFilter.innerHTML = '<option value="">جميع المدن</option>';
            countryFilter.innerHTML = '<option value="">جميع البلدان</option>';

            // Add city options
            cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                cityFilter.appendChild(option);
            });

            // Add country options
            countries.forEach(country => {
                const option = document.createElement('option');
                option.value = country;
                option.textContent = country;
                countryFilter.appendChild(option);
            });
        }

        async function deleteLocation(locationId) {
            if (!confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
                return;
            }

            try {
                const response = await fetch(`/v1/locations/${locationId}`, {
                    method: 'DELETE',
                    headers: AuthUtils.getAuthHeaders()
                });

                if (response.ok) {
                    showMessage('تم حذف الموقع بنجاح', 'success');
                    // Reload current page
                    const currentOffset = pagination ? pagination.getOffset() : 0;
                    const currentLimit = pagination ? pagination.pageSize : 12;
                    loadLocations(currentOffset, currentLimit);
                } else {
                    throw new Error('Failed to delete location');
                }
            } catch (error) {
                console.error('Error deleting location:', error);
                showMessage('فشل في حذف الموقع', 'error');
            }
        }

        function showMessage(message, type) {
            const responseMessage = document.getElementById('responseMessage');
            responseMessage.textContent = message;
            responseMessage.className = `response-message ${type}`;
            responseMessage.style.display = 'block';

            setTimeout(() => {
                responseMessage.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
