<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تعديل الفئة - قريب بلس</title>

    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css" />
    <link rel="stylesheet" href="../../assets/css/components.css" />
    <link rel="stylesheet" href="../../assets/css/forms.css" />

    <style>
      .emoji-picker {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 10px;
        max-height: 200px;
        overflow-y: auto;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 15px;
        background: #fafbfc;
        margin-top: 10px;
      }

      .emoji-option {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        border: 2px solid transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: var(--transition);
        background: white;
      }

      .emoji-option:hover {
        border-color: var(--primary-color);
        background: rgba(102, 126, 234, 0.1);
      }

      .emoji-option.selected {
        border-color: var(--primary-color);
        background: rgba(102, 126, 234, 0.2);
      }

      .selected-emoji {
        font-size: 2rem;
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 15px;
      }

      .icon-input-group {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        align-items: center;
      }

      .icon-text-input {
        flex: 1;
        padding: 12px;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        font-size: 1.2rem;
        text-align: center;
        background: white;
        transition: var(--transition);
      }

      .icon-text-input:focus {
        border-color: var(--primary-color);
        outline: none;
        background: rgba(102, 126, 234, 0.05);
      }

      .icon-mode-toggle {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: var(--transition);
        white-space: nowrap;
      }

      .icon-mode-toggle:hover {
        background: var(--primary-dark);
      }

      .emoji-picker.hidden {
        display: none;
      }

      .icon-input-hint {
        font-size: 0.85rem;
        color: #666;
        margin-top: 5px;
      }

      .slug-preview {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 6px;
        font-family: monospace;
        color: #666;
        margin-top: 5px;
        direction: ltr;
        text-align: left;
      }

      .category-id {
        background: #e9ecef;
        padding: 10px;
        border-radius: 6px;
        font-family: monospace;
        color: #666;
        margin-bottom: 15px;
      }

      .loading-container {
        text-align: center;
        padding: 60px 20px;
      }
    </style>
  </head>
  <body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">تعديل الفئة</h1>
        <p class="page-subtitle">تعديل بيانات الفئة المحددة</p>
      </div>

      <div id="loadingContainer" class="loading-container">
        <div class="loading-spinner"></div>
        <p>جاري تحميل بيانات الفئة...</p>
      </div>

      <div class="form-container" id="formContainer" style="display: none">
        <div class="category-id" id="categoryId">معرف الفئة: #</div>

        <form id="categoryForm">
          <div class="form-section">
            <h2>📂 المعلومات الأساسية</h2>

            <div class="form-group">
              <div class="bilingual-label">
                <span class="ar">اسم الفئة (عربي)</span>
                <span class="en">Category Name (Arabic)</span>
              </div>
              <input
                type="text"
                id="nameAr"
                name="nameAr"
                required
                dir="rtl"
                placeholder="أدخل اسم الفئة بالعربية"
              />
              <div class="slug-preview" id="slugPreview">category-slug</div>
            </div>

            <div class="form-group">
              <div class="bilingual-label">
                <span class="ar">اسم الفئة (إنجليزي)</span>
                <span class="en">Category Name (English)</span>
              </div>
              <input
                type="text"
                id="nameEn"
                name="nameEn"
                required
                dir="ltr"
                placeholder="Enter category name in English"
              />
            </div>

            <div class="form-group">
              <label for="descriptionAr">الوصف (عربي)</label>
              <textarea
                id="descriptionAr"
                name="descriptionAr"
                rows="3"
                dir="rtl"
                placeholder="وصف مختصر عن الفئة"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="descriptionEn">الوصف (إنجليزي)</label>
              <textarea
                id="descriptionEn"
                name="descriptionEn"
                rows="3"
                dir="ltr"
                placeholder="Brief description of the category"
              ></textarea>
            </div>
          </div>

          <div class="form-section">
            <h2>🎨 التخصيص</h2>

            <div class="form-group">
              <label>أيقونة الفئة</label>
              <div class="selected-emoji" id="selectedEmoji">📂</div>
              
              <div class="icon-input-group">
                <input 
                  type="text" 
                  id="iconTextInput" 
                  class="icon-text-input" 
                  placeholder="أدخل أيقونة مخصصة (رمز تعبيري أو نص)"
                  dir="ltr"
                />
                <button type="button" class="icon-mode-toggle" id="toggleEmojiPicker">
                  📝 اختيار من القائمة
                </button>
              </div>
              
              <div class="icon-input-hint">
                يمكنك إدخال أي رمز تعبيري أو نص، أو الاختيار من القائمة أدناه
              </div>
              
              <div class="emoji-picker" id="emojiPicker">
                <!-- Emojis will be populated here -->
              </div>
              <input type="hidden" id="icon" name="icon" value="📂" />
            </div>

            <div class="form-group">
              <label for="cover">رابط صورة الغلاف (اختياري)</label>
              <input
                type="url"
                id="cover"
                name="cover"
                dir="ltr"
                placeholder="https://example.com/cover.jpg"
              />
              <small>رابط صورة الغلاف للفئة</small>
            </div>

            <div class="form-group">
              <label for="slug">الرابط المختصر (Slug)</label>
              <input
                type="text"
                id="slug"
                name="slug"
                dir="ltr"
                placeholder="category-slug"
              />
              <small>سيتم إنشاؤه تلقائياً من اسم الفئة إذا ترك فارغاً</small>
              <div class="slug-preview" id="slugPreview">category-slug</div>
            </div>
          </div>

          <div class="form-section">
            <h2>⚙️ الإعدادات</h2>

            <div class="form-group">
              <label for="parentId">الفئة الأب (اختياري)</label>
              <select id="parentId" name="parentId">
                <option value="">فئة رئيسية</option>
              </select>
              <small>اختر فئة أب لإنشاء فئة فرعية</small>
            </div>

            <div class="form-group">
              <label for="sortOrder">ترتيب العرض</label>
              <input
                type="number"
                id="sortOrder"
                name="sortOrder"
                value="0"
                min="0"
                dir="ltr"
              />
              <small>رقم أقل = يظهر أولاً</small>
            </div>

            <div class="form-group">
              <label for="level">مستوى الفئة</label>
              <input
                type="number"
                id="level"
                name="level"
                value="0"
                min="0"
                max="3"
                dir="ltr"
              />
              <small>0 = فئة رئيسية، 1 = فئة فرعية، إلخ</small>
            </div>

            <div class="form-group">
              <label for="path">مسار الفئة (اختياري)</label>
              <input
                type="text"
                id="path"
                name="path"
                dir="ltr"
                placeholder="/main-category/sub-category"
              />
              <small>المسار الهرمي للفئة</small>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary" id="submitBtn">
              <span id="submitBtnText">حفظ التغييرات</span>
              <div
                class="loading-spinner"
                id="submitSpinner"
                style="display: none"
              ></div>
            </button>
            <a href="list.html" class="btn btn-secondary">إلغاء</a>
            <button type="button" class="btn btn-danger" id="deleteBtn">
              <i class="fas fa-trash"></i>
              حذف الفئة
            </button>
          </div>

          <div class="response-message" id="responseMessage"></div>
        </form>
      </div>

      <div id="errorContainer" class="empty-state" style="display: none">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>خطأ في تحميل البيانات</h3>
        <p>لم يتم العثور على الفئة المطلوبة</p>
        <a href="list.html" class="btn btn-primary" style="margin-top: 20px">
          العودة إلى قائمة الفئات
        </a>
      </div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script>
      const categoryEmojis = [
        '📂',
        '🏪',
        '🍕',
        '☕',
        '🏥',
        '🎓',
        '🏦',
        '⛽',
        '🚗',
        '🏠',
        '👕',
        '📱',
        '💻',
        '🎮',
        '📚',
        '🎵',
        '🎬',
        '🏋️',
        '💄',
        '🧸',
        '🌸',
        '🍰',
        '🍔',
        '🍜',
        '🥗',
        '🍺',
        '🍷',
        '🎂',
        '🍭',
        '🧊',
        '🥤',
        '🍳',
        '🛒',
        '🛍️',
        '👗',
        '👠',
        '⌚',
        '💍',
        '🎒',
        '🧳',
        '🏖️',
        '🏔️',
        '🎪',
        '🎨',
        '🎭',
        '🎯',
        '🎲',
        '🏛️',
        '🏢',
        '🏬',
        '🏭',
        '🏗️',
        '🏘️',
        '🏚️',
        '🏟️',
        '🏞️',
        '🌆',
        '🌇',
        '🌃',
        '🌉',
        '🌁',
        '⛩️',
        '🕌',
        '⛪',
        '🏰',
        '🗼',
        '🗽',
        '⛲',
        '⛱️',
        '🏕️',
        '🎡',
        '🎢',
        '🍞',
      ];

      let categories = [];
      let currentCategoryId = null;
      let currentCategory = null;

      document.addEventListener('DOMContentLoaded', function () {
        // Check authentication
        if (!AuthUtils.isAuthenticated()) {
          window.location.href = '../auth/login.html';
          return;
        }

        // Get category ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        currentCategoryId = urlParams.get('id');

        if (!currentCategoryId) {
          showError();
          return;
        }

        loadCategory();
        loadParentCategories();
        populateEmojiPicker();
        setupEventListeners();
      });

      function setupEventListeners() {
        const form = document.getElementById('categoryForm');
        const nameAr = document.getElementById('nameAr');
        const nameEn = document.getElementById('nameEn');
        const deleteBtn = document.getElementById('deleteBtn');
        const iconTextInput = document.getElementById('iconTextInput');
        const toggleEmojiPicker = document.getElementById('toggleEmojiPicker');

        form.addEventListener('submit', handleSubmit);
        nameAr.addEventListener('input', updateSlugPreview);
        nameEn.addEventListener('input', updateSlugPreview);
        deleteBtn.addEventListener('click', handleDelete);
        
        // Icon input event listeners
        iconTextInput.addEventListener('input', handleIconTextInput);
        toggleEmojiPicker.addEventListener('click', toggleEmojiPickerVisibility);
      }

      async function loadCategory() {
        const loadingContainer = document.getElementById('loadingContainer');
        const formContainer = document.getElementById('formContainer');
        const errorContainer = document.getElementById('errorContainer');

        try {
          const response = await fetch(`/v1/categories/${currentCategoryId}`, {
            headers: AuthUtils.getAuthHeaders(),
          });
          if (response.ok) {
            const result = await response.json();
            currentCategory = result.data || result;
            populateForm(currentCategory);

            loadingContainer.style.display = 'none';
            formContainer.style.display = 'block';
          } else {
            throw new Error('Category not found');
          }
        } catch (error) {
          console.error('Error loading category:', error);
          loadingContainer.style.display = 'none';
          errorContainer.style.display = 'block';
        }
      }

      function populateForm(category) {
        document.getElementById('categoryId').textContent =
          `معرف الفئة: #${category.id}`;
        document.getElementById('nameAr').value = category.nameAr || '';
        document.getElementById('nameEn').value = category.nameEn || '';
        document.getElementById('descriptionAr').value =
          category.descriptionAr || '';
        document.getElementById('descriptionEn').value =
          category.descriptionEn || '';
        document.getElementById('cover').value = category.cover || '';
        document.getElementById('slug').value = category.slug || '';
        document.getElementById('sortOrder').value = category.sortOrder || 0;
        document.getElementById('level').value = category.level || 0;
        document.getElementById('path').value = category.path || '';

        if (category.icon) {
          selectEmoji(category.icon);
          // Also populate the text input with the current icon
          document.getElementById('iconTextInput').value = category.icon;
        }

        if (category.parentId) {
          document.getElementById('parentId').value = category.parentId;
        }

        updateSlugPreview();
      }

      function populateEmojiPicker() {
        const emojiPicker = document.getElementById('emojiPicker');

        categoryEmojis.forEach((emoji) => {
          const emojiOption = document.createElement('div');
          emojiOption.className = 'emoji-option';
          emojiOption.textContent = emoji;
          emojiOption.addEventListener('click', () => selectEmoji(emoji));
          emojiPicker.appendChild(emojiOption);
        });
      }

      function selectEmoji(emoji) {
        document.getElementById('selectedEmoji').textContent = emoji;
        document.getElementById('icon').value = emoji;
        
        // Clear the text input when an emoji is selected from the picker
        document.getElementById('iconTextInput').value = emoji;

        document.querySelectorAll('.emoji-option').forEach((option) => {
          option.classList.remove('selected');
        });

        const selectedOption = Array.from(
          document.querySelectorAll('.emoji-option'),
        ).find((option) => option.textContent === emoji);
        if (selectedOption) {
          selectedOption.classList.add('selected');
        }
      }

      function updateSlugPreview() {
        const nameAr = document.getElementById('nameAr').value;
        const nameEn = document.getElementById('nameEn').value;

        const text = nameEn || nameAr;
        const slug = text
          .toLowerCase()
          .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim('-');

        document.getElementById('slugPreview').textContent =
          slug || 'category-slug';
      }

      async function loadParentCategories() {
        try {
          const response = await fetch('/v1/categories', {
            headers: AuthUtils.getAuthHeaders(),
          });
          if (response.ok) {
            const result = await response.json();
            categories = result.data?.items || result.data || result;
            populateParentCategories();
          }
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      function populateParentCategories() {
        const select = document.getElementById('parentId');

        // Filter out current category and its children to prevent circular references
        const availableCategories = categories.filter(
          (cat) =>
            cat.id !== parseInt(currentCategoryId) &&
            cat.parentId !== parseInt(currentCategoryId) &&
            !cat.parentId, // Only show top-level categories as potential parents
        );

        availableCategories.forEach((category) => {
          const option = document.createElement('option');
          option.value = category.id;
          option.textContent = category.nameAr;
          select.appendChild(option);
        });
      }

      async function handleSubmit(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('submitBtn');
        const submitBtnText = document.getElementById('submitBtnText');
        const submitSpinner = document.getElementById('submitSpinner');

        submitBtn.disabled = true;
        submitBtnText.style.display = 'none';
        submitSpinner.style.display = 'inline-block';

        try {
          const formData = new FormData(e.target);
          const categoryData = {
            nameAr: formData.get('nameAr'),
            nameEn: formData.get('nameEn'),
            icon: formData.get('icon'),
            sortOrder: parseInt(formData.get('sortOrder')) || 0,
            level: parseInt(formData.get('level')) || 0,
          };

          // Add optional fields if they have values
          const descriptionAr = formData.get('descriptionAr');
          if (descriptionAr) categoryData.descriptionAr = descriptionAr;

          const descriptionEn = formData.get('descriptionEn');
          if (descriptionEn) categoryData.descriptionEn = descriptionEn;

          const cover = formData.get('cover');
          if (cover) categoryData.cover = cover;

          const slug = formData.get('slug');
          if (slug) categoryData.slug = slug;

          const path = formData.get('path');
          if (path) categoryData.path = path;

          const parentId = formData.get('parentId');
          if (parentId) {
            categoryData.parentId = parseInt(parentId);
          } else {
            categoryData.parentId = null;
          }

          const response = await fetch(`/v1/categories/${currentCategoryId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              ...AuthUtils.getAuthHeaders(),
            },
            body: JSON.stringify(categoryData),
          });

          const result = await response.json();

          if (response.ok && result.success) {
            showMessage('تم تحديث الفئة بنجاح!', 'success');
            setTimeout(() => {
              window.location.href = 'list.html';
            }, 2000);
          } else {
            showMessage(result.message || 'فشل في تحديث الفئة', 'error');
          }
        } catch (error) {
          console.error('Error updating category:', error);
          showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
        } finally {
          submitBtn.disabled = false;
          submitBtnText.style.display = 'inline';
          submitSpinner.style.display = 'none';
        }
      }

      async function handleDelete() {
        if (
          !confirm(
            'هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع الفئات الفرعية أيضاً.',
          )
        ) {
          return;
        }

        try {
          const response = await fetch(`/v1/categories/${currentCategoryId}`, {
            method: 'DELETE',
            headers: AuthUtils.getAuthHeaders(),
          });

          if (response.ok) {
            showMessage('تم حذف الفئة بنجاح', 'success');
            setTimeout(() => {
              window.location.href = 'list.html';
            }, 2000);
          } else {
            throw new Error('Failed to delete category');
          }
        } catch (error) {
          console.error('Error deleting category:', error);
          showMessage('فشل في حذف الفئة', 'error');
        }
      }

      function showError() {
        document.getElementById('loadingContainer').style.display = 'none';
        document.getElementById('errorContainer').style.display = 'block';
      }

      function handleIconTextInput(e) {
        const icon = e.target.value.trim();
        if (icon) {
          document.getElementById('selectedEmoji').textContent = icon;
          document.getElementById('icon').value = icon;
          
          // Clear emoji picker selection since user is using custom input
          document.querySelectorAll('.emoji-option').forEach((option) => {
            option.classList.remove('selected');
          });
        }
      }

      function toggleEmojiPickerVisibility() {
        const emojiPicker = document.getElementById('emojiPicker');
        const toggleBtn = document.getElementById('toggleEmojiPicker');
        
        if (emojiPicker.classList.contains('hidden')) {
          emojiPicker.classList.remove('hidden');
          toggleBtn.textContent = '🔼 إخفاء القائمة';
        } else {
          emojiPicker.classList.add('hidden');
          toggleBtn.textContent = '📝 اختيار من القائمة';
        }
      }

      function showMessage(message, type) {
        const responseMessage = document.getElementById('responseMessage');
        responseMessage.textContent = message;
        responseMessage.className = `response-message ${type}`;
        responseMessage.style.display = 'block';

        if (type === 'success') {
          setTimeout(() => {
            responseMessage.style.display = 'none';
          }, 5000);
        }
      }
    </script>
  </body>
</html>
