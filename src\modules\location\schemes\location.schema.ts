import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '../../../infrastructure/database/schemas/base.schema';
import { Location } from '../entities/location.entity';

export const LocationSchema = new EntitySchema<Location>({
  name: Location.name,
  target: Location,
  tableName: 'locations',
  columns: {
    ...BaseSchemaProperties,
    nameEn: {
      type: String,
      nullable: false,
    },
    nameAr: {
      type: String,
      nullable: false,
    },
    latitude: {
      type: 'decimal',
      precision: 10,
      scale: 8,
      nullable: false,
    },
    longitude: {
      type: 'decimal',
      precision: 11,
      scale: 8,
      nullable: false,
    },
    streetAddressEn: {
      type: String,
      nullable: true,
    },
    streetAddressAr: {
      type: String,
      nullable: true,
    },
    cityEn: {
      type: String,
      nullable: true,
    },
    cityAr: {
      type: String,
      nullable: true,
    },
    stateEn: {
      type: String,
      nullable: true,
    },
    stateAr: {
      type: String,
      nullable: true,
    },
    countryEn: {
      type: String,
      nullable: true,
    },
    countryAr: {
      type: String,
      nullable: true,
    },
    postalCode: {
      type: String,
      nullable: true,
    },
    nearestLandmarkEn: {
      type: String,
      nullable: true,
    },
    nearestLandmarkAr: {
      type: String,
      nullable: true,
    },
    descriptionEn: {
      type: 'text',
      nullable: true,
    },
    descriptionAr: {
      type: 'text',
      nullable: true,
    },
    mapUrl: {
      type: String,
      nullable: true,
    },
    placeId: {
      type: String,
      nullable: true,
      unique: true,
    },
    accuracy: {
      type: 'decimal',
      precision: 3,
      scale: 2,
      nullable: true,
    },
    altitude: {
      type: 'decimal',
      precision: 8,
      scale: 2,
      nullable: true,
    },
    timezone: {
      type: String,
      nullable: true,
    },
    parentId: {
      type: Number,
      nullable: true,
    },
  },
  relations: {
    parent: {
      target: Location.name,
      type: 'many-to-one',
      nullable: true,
      joinColumn: { name: 'parentId' },
      onDelete: 'CASCADE',
    },
    children: {
      target: Location.name,
      type: 'one-to-many',
      inverseSide: 'parent',
    },
  },
  indices: [
    {
      name: 'locations_coordinates_index',
      columns: ['latitude', 'longitude'],
    },
    {
      name: 'locations_place_id_unique',
      columns: ['placeId'],
      unique: true,
    },
    {
      name: 'locations_parent_id_index',
      columns: ['parentId'],
    },
    {
      name: 'locations_city_en_index',
      columns: ['cityEn'],
    },
    {
      name: 'locations_city_ar_index',
      columns: ['cityAr'],
    },
    {
      name: 'locations_country_en_index',
      columns: ['countryEn'],
    },
    {
      name: 'locations_country_ar_index',
      columns: ['countryAr'],
    },
  ],
});
