import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';

export interface TestUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export class AuthHelper {
  static readonly TEST_ADMIN: TestUser = {
    email: '<EMAIL>',
    password: 'Test123!@#',
    firstName: 'Test',
    lastName: 'Admin',
  };

  static readonly TEST_USER: TestUser = {
    email: '<EMAIL>',
    password: 'Test123!@#',
    firstName: 'Test',
    lastName: 'User',
  };

  /**
   * Login with a test user and return auth tokens
   */
  static async login(
    app: INestApplication<App>,
    user: TestUser = AuthHelper.TEST_ADMIN,
  ): Promise<AuthTokens> {
    const response = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        loginId: user.email,
        password: user.password,
      });

    if (response.status !== 201) {
      console.error('<PERSON><PERSON> failed:', response.body);
      throw new Error(
        `<PERSON><PERSON> failed with status ${response.status}: ${JSON.stringify(response.body)}`,
      );
    }

    return {
      accessToken: response.body.accessToken,
      refreshToken: response.body.refreshToken,
    };
  }

  /**
   * Get authorization header with bearer token
   */
  static getAuthHeader(token: string): { Authorization: string } {
    return { Authorization: `Bearer ${token}` };
  }

  /**
   * Create a custom test user (for tests that need specific user data)
   */
  static async createTestUser(
    app: INestApplication<App>,
    userData: {
      email: string;
      password: string;
      firstName: string;
      lastName: string;
    },
  ): Promise<AuthTokens> {
    await request(app.getHttpServer())
      .post('/auth/register')
      .send(userData)
      .expect(201);

    return AuthHelper.login(app, userData);
  }
}
