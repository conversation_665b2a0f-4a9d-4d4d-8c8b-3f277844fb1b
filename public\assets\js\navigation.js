// Modern Sidebar Navigation System
class SidebarNavigation {
    constructor() {
        this.sidebar = null;
        this.sidebarToggle = null;
        this.mobileOverlay = null;
        this.isCollapsed = false;
        this.isMobile = window.innerWidth <= 768;
        
        this.init();
        this.bindEvents();
    }

    init() {
        this.createSidebar();
        this.updateAuthStatus();
        this.setActivePage();

        // Initialize sidebar state based on screen size
        if (this.isMobile) {
            // Ensure sidebar is hidden on mobile
            this.sidebar.classList.remove('collapsed');
            this.isCollapsed = false;

            // Force mobile state after a short delay to ensure DOM is ready
            setTimeout(() => {
                this.forceMobileState();
            }, 50);
        } else {
            // Load collapsed state from localStorage for desktop
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState === 'true') {
                this.toggleSidebar();
            }
        }
    }

    createSidebar() {
        // Remove existing navigation if present
        const existingNav = document.querySelector('.top-nav');
        if (existingNav) {
            existingNav.remove();
        }

        // Create sidebar HTML
        const sidebarHTML = `
            <div class="app-layout">
                <aside class="sidebar" id="sidebar">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <div class="sidebar-header">
                        <div class="sidebar-logo">🏪</div>
                        <div class="sidebar-title">قريب بلس</div>
                    </div>

                    <nav class="nav-menu">
                        <div class="nav-section">
                            <div class="nav-section-title">القائمة الرئيسية</div>
                            <div class="nav-item">
                                <a href="/index.html" class="nav-link" data-page="home">
                                    <span class="nav-icon">🏠</span>
                                    <span class="nav-text">الرئيسية</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/business/list.html" class="nav-link" data-page="businesses">
                                    <span class="nav-icon">🏪</span>
                                    <span class="nav-text">الأعمال التجارية</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/business/form.html" class="nav-link" data-page="business-form">
                                    <span class="nav-icon">➕</span>
                                    <span class="nav-text">إضافة عمل تجاري</span>
                                </a>
                            </div>
                        </div>

                        <div class="nav-section">
                            <div class="nav-section-title">إدارة البيانات</div>
                            <div class="nav-item">
                                <a href="/pages/categories/list.html" class="nav-link" data-page="categories">
                                    <span class="nav-icon">📂</span>
                                    <span class="nav-text">الفئات</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/categories/form.html" class="nav-link" data-page="category-form">
                                    <span class="nav-icon">📝</span>
                                    <span class="nav-text">إضافة فئة</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/features/list.html" class="nav-link" data-page="features">
                                    <span class="nav-icon">⭐</span>
                                    <span class="nav-text">الميزات</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/features/form.html" class="nav-link" data-page="feature-form">
                                    <span class="nav-icon">✨</span>
                                    <span class="nav-text">إضافة ميزة</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/locations/list.html" class="nav-link" data-page="locations">
                                    <span class="nav-icon">📍</span>
                                    <span class="nav-text">المواقع</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/pages/locations/form.html" class="nav-link" data-page="location-form">
                                    <span class="nav-icon">🗺️</span>
                                    <span class="nav-text">إضافة موقع</span>
                                </a>
                            </div>
                        </div>
                    </nav>

                    <div class="user-profile" id="userProfile">
                        <div class="user-info" id="userInfo" style="display: none;">
                            <div class="user-avatar" id="userAvatar">👤</div>
                            <div class="user-details">
                                <div class="user-name" id="userName">مستخدم</div>
                                <div class="user-role" id="userRole">User</div>
                            </div>
                        </div>
                        <button class="logout-btn" id="logoutBtn" style="display: none;">
                            تسجيل الخروج
                        </button>
                        <a href="/pages/auth/login.html" class="logout-btn" id="loginBtn">
                            تسجيل الدخول
                        </a>
                    </div>
                </aside>

                <div class="mobile-overlay" id="mobileOverlay"></div>
                
                <main class="main-content" id="mainContent">
                    <!-- Page content will be inserted here -->
                </main>
            </div>
        `;

        // Insert sidebar into body
        document.body.insertAdjacentHTML('afterbegin', sidebarHTML);
        
        // Get references
        this.sidebar = document.getElementById('sidebar');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.mobileOverlay = document.getElementById('mobileOverlay');
        this.mainContent = document.getElementById('mainContent');

        // Force proper initial state for mobile
        this.forceMobileState();
    }

    bindEvents() {
        // Sidebar toggle
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Mobile overlay click
        if (this.mobileOverlay) {
            this.mobileOverlay.addEventListener('click', () => this.closeMobileSidebar());
        }

        // Window resize
        window.addEventListener('resize', () => this.handleResize());

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (this.isMobile) {
                    this.closeMobileSidebar();
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                this.toggleSidebar();
            }
            if (e.key === 'Escape' && this.isMobile) {
                this.closeMobileSidebar();
            }
        });
    }

    toggleSidebar() {
        if (this.isMobile) {
            this.toggleMobileSidebar();
        } else {
            this.toggleDesktopSidebar();
        }
    }

    toggleDesktopSidebar() {
        this.isCollapsed = !this.isCollapsed;
        this.sidebar.classList.toggle('collapsed', this.isCollapsed);
        
        // Save state
        localStorage.setItem('sidebarCollapsed', this.isCollapsed.toString());
        
        // Update toggle icon
        const icon = this.sidebarToggle.querySelector('i');
        if (icon) {
            icon.className = this.isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left';
        }
    }

    toggleMobileSidebar() {
        const isOpen = this.sidebar.classList.contains('mobile-open');
        
        if (isOpen) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    openMobileSidebar() {
        // Remove mobile-hidden class to allow opening
        this.sidebar.classList.remove('mobile-hidden');
        this.sidebar.style.transform = '';

        this.sidebar.classList.add('mobile-open');
        this.mobileOverlay.style.display = 'block';
        // Force reflow to ensure the display style is applied
        void this.mobileOverlay.offsetHeight;
        this.mobileOverlay.classList.add('visible');
        document.body.style.overflow = 'hidden';
    }

    closeMobileSidebar() {
        this.sidebar.classList.remove('mobile-open');
        this.mobileOverlay.classList.remove('visible');

        // Wait for transition to complete before hiding
        setTimeout(() => {
            this.mobileOverlay.style.display = 'none';
            this.mobileOverlay.style.pointerEvents = 'none';

            // Re-apply mobile-hidden class if still on mobile
            if (this.isMobile) {
                this.sidebar.classList.add('mobile-hidden');
                this.sidebar.style.transform = 'translateX(100%)';
            }
        }, 300);

        document.body.style.overflow = '';
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        // If switching from mobile to desktop
        if (wasMobile && !this.isMobile) {
            // Close mobile sidebar and reset mobile states
            this.sidebar.classList.remove('mobile-open');
            this.mobileOverlay.classList.remove('visible');
            this.mobileOverlay.style.display = 'none';
            document.body.style.overflow = '';

            // Restore collapsed state if it was saved
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState === 'true') {
                this.isCollapsed = true;
                this.sidebar.classList.add('collapsed');
            }
        }

        // If switching from desktop to mobile
        if (!wasMobile && this.isMobile) {
            // Remove collapsed state and ensure sidebar is hidden
            this.sidebar.classList.remove('collapsed');
            this.isCollapsed = false;

            // Force mobile state
            this.forceMobileState();
        }
    }

    forceMobileState() {
        // Force mobile state immediately if on mobile device
        if (this.isMobile && this.sidebar) {
            console.log('Forcing mobile state - hiding sidebar');

            // Remove any classes that might show the sidebar
            this.sidebar.classList.remove('mobile-open');
            this.sidebar.classList.remove('collapsed');

            // Add the mobile-hidden class for extra enforcement
            this.sidebar.classList.add('mobile-hidden');

            // Force the transform style directly as backup
            this.sidebar.style.transform = 'translateX(100%)';

            // Ensure overlay is hidden
            if (this.mobileOverlay) {
                this.mobileOverlay.style.display = 'none';
                this.mobileOverlay.classList.remove('visible');
            }

            // Reset body overflow
            document.body.style.overflow = '';

            console.log('Mobile state forced - sidebar should be hidden');
        } else if (!this.isMobile && this.sidebar) {
            // Remove mobile-hidden class when not on mobile
            this.sidebar.classList.remove('mobile-hidden');
            this.sidebar.style.transform = '';
        }
    }

    updateAuthStatus() {
        const userInfo = document.getElementById('userInfo');
        const userName = document.getElementById('userName');
        const userRole = document.getElementById('userRole');
        const userAvatar = document.getElementById('userAvatar');
        const logoutBtn = document.getElementById('logoutBtn');
        const loginBtn = document.getElementById('loginBtn');

        if (typeof AuthUtils !== 'undefined' && AuthUtils.isAuthenticated()) {
            const userData = AuthUtils.getUserData();
            
            // Show user info
            if (userInfo) userInfo.style.display = 'flex';
            if (logoutBtn) logoutBtn.style.display = 'block';
            if (loginBtn) loginBtn.style.display = 'none';
            
            // Update user details
            if (userName) userName.textContent = userData?.email || userData?.loginId || userData?.sub || 'مستخدم';
            if (userRole) userRole.textContent = userData?.role?.name || 'User';
            if (userAvatar) {
                const displayName = userData?.email || userData?.loginId || userData?.sub || '';
                userAvatar.textContent = displayName.charAt(0).toUpperCase() || '👤';
            }
        } else {
            // Show login button
            if (userInfo) userInfo.style.display = 'none';
            if (logoutBtn) logoutBtn.style.display = 'none';
            if (loginBtn) loginBtn.style.display = 'block';
        }
    }

    setActivePage() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            
            if (href && (currentPath === href || currentPath.endsWith(href))) {
                link.classList.add('active');
            }
        });
        
        // Special case for index page
        if (currentPath === '/' || currentPath.endsWith('/index.html')) {
            const homeLink = document.querySelector('[data-page="home"]');
            if (homeLink) homeLink.classList.add('active');
        }
    }

    logout() {
        if (typeof AuthUtils !== 'undefined') {
            AuthUtils.clearTokens();
            window.location.href = '/pages/auth/login.html';
        }
    }

    // Public method to move existing content into main area
    moveContentToMain() {
        const existingContent = document.querySelector('.container, .dashboard-container');
        if (existingContent && this.mainContent) {
            // Create content container if it doesn't exist
            let contentContainer = this.mainContent.querySelector('.content-container');
            if (!contentContainer) {
                contentContainer = document.createElement('div');
                contentContainer.className = 'content-container';
                this.mainContent.appendChild(contentContainer);
            }
            
            // Move existing content
            contentContainer.appendChild(existingContent);
        }
    }
}

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.sidebarNav = new SidebarNavigation();

    // Move existing content to main area
    setTimeout(() => {
        window.sidebarNav.moveContentToMain();

        // Force mobile state one more time after everything is loaded
        if (window.sidebarNav.isMobile) {
            window.sidebarNav.forceMobileState();
        }
    }, 100);
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SidebarNavigation;
}
