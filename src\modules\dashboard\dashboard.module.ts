import { Module } from '@nestjs/common';
import { DashboardController } from './controllers/dashboard.controller';
import { IDashboardService } from './interfaces/dashboard-service.interface';
import { DashboardService } from './services/dashboard.service';

@Module({
  imports: [],
  controllers: [DashboardController],
  providers: [
    {
      provide: IDashboardService,
      useClass: DashboardService,
    },
  ],
  exports: [IDashboardService],
})
export class DashboardModule {}
