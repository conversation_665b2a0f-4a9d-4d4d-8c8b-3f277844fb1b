import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Feature (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/features (GET)', () => {
    it('should return empty list initially', () => {
      return request(app.getHttpServer())
        .get('/features')
        .expect(HttpStatus.UNAUTHORIZED); // Because of JWT auth guard
    });
  });
});
