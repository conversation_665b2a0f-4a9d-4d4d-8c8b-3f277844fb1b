import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '../../../infrastructure/database/schemas/base.schema';
import { Category } from '../entities/category.entity';

export const CategorySchema = new EntitySchema<Category>({
  name: Category.name,
  target: Category,
  tableName: 'categories',
  columns: {
    ...BaseSchemaProperties,
    nameEn: {
      type: String,
      nullable: false,
    },
    nameAr: {
      type: String,
      nullable: false,
    },
    descriptionEn: {
      type: String,
      nullable: true,
    },
    descriptionAr: {
      type: String,
      nullable: true,
    },
    icon: {
      type: String,
      nullable: true,
    },
    cover: {
      type: String,
      nullable: true,
    },
    slug: {
      type: String,
      nullable: true,
      unique: true,
    },
    numberOfBusinesses: {
      type: Number,
      nullable: false,
      default: 0,
    },
    sortOrder: {
      type: Number,
      nullable: false,
      default: 0,
    },
    level: {
      type: Number,
      nullable: false,
      default: 0,
    },
    path: {
      type: String,
      nullable: true,
    },
    parentId: {
      type: Number,
      nullable: true,
    },
  },
  relations: {
    parent: {
      target: Category.name,
      type: 'many-to-one',
      nullable: true,
      joinColumn: { name: 'parentId' },
      onDelete: 'CASCADE',
    },
    children: {
      target: Category.name,
      type: 'one-to-many',
      inverseSide: 'parent',
    },
  },
  indices: [
    {
      name: 'categories_slug_unique',
      columns: ['slug'],
      unique: true,
    },
    {
      name: 'categories_parent_id_index',
      columns: ['parentId'],
    },
    {
      name: 'categories_level_index',
      columns: ['level'],
    },
    {
      name: 'categories_sort_order_index',
      columns: ['sortOrder'],
    },
  ],
});
