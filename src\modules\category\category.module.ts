import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { CategoryController } from './controllers/category.controller';
import { ICategoryRepository } from './interfaces/category-repository/category-repository.interface';
import { ICategoryService } from './interfaces/category-service/category-service.interface';
import { CategoryRepository } from './repositories/category.repository';
import { CategorySchema } from './schemes/category.schema';
import { CategoryService } from './services/category.service';

@Module({
  imports: [TypeOrmModule.forFeature([CategorySchema]), AuthModule],
  controllers: [CategoryController],
  providers: [
    {
      provide: ICategoryRepository,
      useClass: CategoryRepository,
    },
    {
      provide: ICategoryService,
      useClass: CategoryService,
    },
  ],
  exports: [ICategoryService, ICategoryRepository],
})
export class CategoryModule {}
