import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeatureController } from './controllers/feature.controller';
import { IFeatureRepository } from './interfaces/feature-repository/feature-repository.interface';
import { IFeatureService } from './interfaces/feature-service/feature-service.interface';
import { FeatureRepository } from './repositories/feature.repository';
import { FeatureSchema } from './schemes/feature.schema';
import { FeatureService } from './services/feature.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [TypeOrmModule.forFeature([FeatureSchema]), AuthModule],
  controllers: [FeatureController],
  providers: [
    {
      provide: IFeatureRepository,
      useClass: FeatureRepository,
    },
    {
      provide: IFeatureService,
      useClass: FeatureService,
    },
  ],
  exports: [IFeatureService, IFeatureRepository],
})
export class FeatureModule {}
