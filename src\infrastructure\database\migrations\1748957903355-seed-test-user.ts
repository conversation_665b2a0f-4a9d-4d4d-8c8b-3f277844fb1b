import * as argon2 from 'argon2';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedTestUser1748957903355 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Hash the test password
    const hashedPassword = await argon2.hash('Test123!@#');

    // Get ADMIN role ID
    const adminRoleResult = await queryRunner.query(`
      SELECT id FROM "roles" WHERE name = 'Super Admin' LIMIT 1
    `);
    const adminRoleId = adminRoleResult[0]?.id;

    if (!adminRoleId) {
      throw new Error(
        'Super Admin role not found. Please run seed-default-roles migration first.',
      );
    }

    // Create test admin user with roleId
    await queryRunner.query(
      `
      INSERT INTO "users" ("email", "password", "firstName", "lastName", "roleId", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      ON CONFLICT ("email", "roleId") DO UPDATE SET
        "password" = $2,
        "firstName" = $3,
        "lastName" = $4,
        "updatedAt" = NOW()
    `,
      ['<EMAIL>', hashedPassword, 'Test', 'Admin', adminRoleId],
    );

    // Get USER role ID
    const customerRoleResult = await queryRunner.query(`
      SELECT id FROM "roles" WHERE name = 'Customer' LIMIT 1
    `);
    const customerRoleId = customerRoleResult[0]?.id;

    if (!customerRoleId) {
      throw new Error(
        'Customer role not found. Please run seed-default-roles migration first.',
      );
    }

    // Create test regular user with roleId
    await queryRunner.query(
      `
      INSERT INTO "users" ("email", "password", "firstName", "lastName", "roleId", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      ON CONFLICT ("email", "roleId") DO UPDATE SET
        "password" = $2,
        "firstName" = $3,
        "lastName" = $4,
        "updatedAt" = NOW()
    `,
      [
        '<EMAIL>',
        hashedPassword,
        'Test',
        'Customer',
        customerRoleId,
      ],
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    if (process.env.NODE_ENV !== 'test') {
      return;
    }

    // Remove test users
    await queryRunner.query(`
      DELETE FROM "users" WHERE email IN ('<EMAIL>', '<EMAIL>')
    `);
  }
}
