import { GenericService, IGetAllResponseInterface } from '@app/common';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { FindManyOptions } from 'typeorm';
import { CreateCategoryDto } from '../dtos/create-category.dto';
import { GetAllCategoryDto } from '../dtos/get-all-category.dto';
import { UpdateCategoryDto } from '../dtos/update-category.dto';
import { Category } from '../entities/category.entity';
import { ICategoryRepository } from '../interfaces/category-repository/category-repository.interface';
import { ICategoryService } from '../interfaces/category-service/category-service.interface';
import { CategorySchema } from '../schemes/category.schema';

@Injectable()
export class CategoryService
  extends GenericService<
    Category,
    typeof CategorySchema,
    GetAllCategoryDto,
    CreateCategoryDto,
    UpdateCategoryDto
  >
  implements ICategoryService
{
  constructor(
    @Inject(ICategoryRepository)
    private readonly categoryRepository: ICategoryRepository,
  ) {
    super(categoryRepository);
  }

  async getAll(
    getAllDto: GetAllCategoryDto,
    initialQuery?: FindManyOptions<Category>,
  ): Promise<IGetAllResponseInterface<Category>> {
    initialQuery = initialQuery || {};
    initialQuery['where'] = initialQuery['where'] || {};

    if (getAllDto.searchKey)
      getAllDto.searchOnJson = 'nameEn,nameAr,descriptionEn,descriptionAr';

    if (!getAllDto.sortKey) getAllDto.sortKey = 'numberOfBusinesses:-1';

    return super.getAll(getAllDto, initialQuery);
  }

  async create(
    entityType: new () => Category,
    dto: CreateCategoryDto,
  ): Promise<Category> {
    const category = plainToInstance(entityType, dto);

    category.slug = category.slug || this.generateSlug(category.nameEn);
    category.sortOrder = category.sortOrder ?? 0;
    category.numberOfBusinesses = 0;

    if (category.parentId) {
      const parent = await this.categoryRepository.findOneBy({
        where: { id: category.parentId },
      });
      if (!parent) {
        throw new NotFoundException('Parent category not found');
      }
      category.level = parent.level + 1;
      category.path = parent.path
        ? `${parent.path}/${parent.id}`
        : `${parent.id}`;
    } else {
      category.level = 0;
      category.path = undefined;
      category.parentId = undefined;
    }

    return this.categoryRepository.createOne(category);
  }

  async update(
    entityType: new () => Category,
    dto: UpdateCategoryDto & { id: number },
  ): Promise<Category> {
    const existingCategory = await this.findOneById(dto.id);
    // Transform DTO to entity instance and merge with existing
    const updatedCategory = plainToInstance(entityType, {
      ...existingCategory,
      ...dto,
    });

    // Ensure we keep the original ID
    updatedCategory.id = dto.id;

    // Handle slug generation if nameEn is updated
    if (dto.nameEn && dto.slug === undefined) {
      updatedCategory.slug = this.generateSlug(dto.nameEn);
    }

    // Handle parent hierarchy updates
    if (dto.parentId !== undefined) {
      if (dto.parentId === updatedCategory.id) {
        throw new BadRequestException('Category cannot be its own parent');
      }

      if (dto.parentId) {
        const parent = await this.findOneById(dto.parentId);
        updatedCategory.level = parent.level + 1;
        updatedCategory.path = parent.path
          ? `${parent.path}/${parent.id}`
          : `${parent.id}`;
      } else {
        updatedCategory.level = 0;
        updatedCategory.path = undefined;
        updatedCategory.parentId = undefined;
      }
    }

    await this.categoryRepository.updateOne(updatedCategory);
    return this.findOneById(existingCategory.id);
  }

  async findByParentId(parentId: number | null): Promise<Category[]> {
    return this.categoryRepository.findByParentId(parentId);
  }

  async findByLevel(level: number): Promise<Category[]> {
    return this.categoryRepository.findByLevel(level);
  }

  async findWithChildren(id: number): Promise<Category | null> {
    return this.categoryRepository.findWithChildren(id);
  }

  async findHierarchy(): Promise<Category[]> {
    return this.categoryRepository.findHierarchy();
  }

  async updateNumberOfBusinesses(id: number, count: number): Promise<void> {
    return this.categoryRepository.updateNumberOfBusinesses(id, count);
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}
