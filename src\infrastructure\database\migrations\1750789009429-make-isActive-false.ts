import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeIsActiveFalse1750789009429 implements MigrationInterface {
    name = 'MakeIsActiveFalse1750789009429'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "businesses" ALTER COLUMN "isActive" SET DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "businesses" ALTER COLUMN "isActive" SET DEFAULT true`);
    }

}
