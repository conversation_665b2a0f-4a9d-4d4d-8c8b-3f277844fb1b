import { IGenericRepository } from '@app/common';
import { User } from '../../entities/user.entity';
import { UserSchema } from '../../schemes/user.schema';

export interface IUserRepository
  extends IGenericRepository<User, typeof UserSchema> {
  findUserByEmailOrPhone(
    emailOrPhone: string,
    roleId?: number,
  ): Promise<User | null>;

  findOneWithDetailsById(id: number): Promise<User | null>;

  findManyByEmailOrPhone(emailOrPhone: string): Promise<User[]>;

  findUserByEmailOrPhoneAndRoleId(
    emailOrPhone: string,
    roleId: number,
  ): Promise<User | null>;
}

export const IUserRepository = Symbol('IUserRepository');
