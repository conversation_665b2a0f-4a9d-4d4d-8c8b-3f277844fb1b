import { IGenericService } from '@app/common';
import { CreateCategoryDto } from '../../dtos/create-category.dto';
import { GetAllCategoryDto } from '../../dtos/get-all-category.dto';
import { UpdateCategoryDto } from '../../dtos/update-category.dto';
import { Category } from '../../entities/category.entity';
import { CategorySchema } from '../../schemes/category.schema';

export interface ICategoryService
  extends IGenericService<
    Category,
    typeof CategorySchema,
    GetAllCategoryDto,
    CreateCategoryDto,
    UpdateCategoryDto
  > {
  findByParentId(parentId: number | null): Promise<Category[]>;
  findByLevel(level: number): Promise<Category[]>;
  findWithChildren(id: number): Promise<Category | null>;
  findHierarchy(): Promise<Category[]>;
  updateNumberOfBusinesses(id: number, count: number): Promise<void>;
}

export const ICategoryService = Symbol('ICategoryService');
