# Database Configuration Guide

## Schema-Entity Separation

This project uses a schema-entity separation pattern:

- **Schemas** (`*.schema.ts`): Define database structure, columns, relations, and constraints
- **Entities** (`*.entity.ts`): Extend schemas to add business logic, validation, and domain behavior

### Example Structure

```
src/modules/example/
├── user.schema.ts  # Database structure
└── user.entity.ts  # Business logic
```

## Working with Migrations

### Setting Up Database with Migrations

1. Ensure your database connection is properly configured in `.env`:

```
DATABASE_URL=postgresql://username:password@localhost:5432/qareeb
```

2. Use the following commands to manage migrations:

```bash
# Create a new empty migration
npm run migration:create --name=MigrationName

# Generate a migration based on entity changes
npm run migration:generate --name=MigrationName

# Run pending migrations
npm run migration:run

# Revert the last applied migration
npm run migration:revert
```

### Migration Files

Migrations are stored in `src/infastructure/database/migrations/`.

## Configuration

The database configuration is managed in:

- `src/infastructure/database/services/datasource.ts`: Main datasource configuration
- `src/infastructure/database/typeorm-config.ts`: TypeORM CLI configuration for migrations

## Best Practices

1. Always use migrations to make database changes
2. Never enable `synchronize: true` in production
3. Keep schemas focused on database structure only
4. Add business logic in entities, not schemas
5. Test migrations before deploying to production
