<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفئات - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pagination.css">
    
    <style>
        .categories-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .category-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .category-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .category-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-gradient);
            border-radius: 12px;
            color: white;
        }
        
        .category-info h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .category-info p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .category-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .category-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }
        
        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
            display: block;
        }
        
        @media (max-width: 768px) {
            .categories-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .categories-grid {
                grid-template-columns: 1fr;
            }
            
            .category-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">الفئات</h1>
            <p class="page-subtitle">إدارة وعرض جميع فئات الأعمال التجارية</p>
        </div>
        
        <div class="categories-header">
            <div>
                <input type="text" id="searchInput" class="search-input" placeholder="البحث في الفئات..." dir="rtl" style="padding: 10px 15px; border: 2px solid #e1e8ed; border-radius: 8px; font-size: 14px; min-width: 250px;">
            </div>
            
            <a href="form.html" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة فئة جديدة
            </a>
        </div>
        
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p>جاري تحميل الفئات...</p>
        </div>
        
        <div id="categoriesContainer" class="categories-grid" style="display: none;">
            <!-- Categories will be loaded here -->
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-folder-open"></i>
            <h3>لا توجد فئات</h3>
            <p>لم يتم العثور على أي فئات مطابقة للبحث</p>
            <a href="form.html" class="btn btn-primary" style="margin-top: 20px;">
                <i class="fas fa-plus"></i>
                إضافة أول فئة
            </a>
        </div>
        
        <div class="response-message" id="responseMessage"></div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script src="../../assets/js/pagination.js"></script>
    <script>
        let categories = [];
        let currentSearchTerm = '';
        let pagination = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePagination();
            loadCategories();
            setupEventListeners();
        });

        function initializePagination() {
            pagination = new PaginationManager({
                containerId: 'paginationContainer',
                pageSize: 12,
                onPageChange: (page, offset) => {
                    loadCategories(offset, pagination.pageSize);
                },
                onPageSizeChange: (pageSize) => {
                    loadCategories(0, pageSize);
                },
                showPageSizeSelector: true,
                pageSizeOptions: [6, 12, 24, 48]
            });
        }

        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearchTerm = this.value.trim();
                    loadCategories(0, pagination.pageSize);
                }, 300);
            });
        }
        
        async function loadCategories(offset = 0, limit = 12) {
            const loadingContainer = document.getElementById('loadingContainer');
            const categoriesContainer = document.getElementById('categoriesContainer');
            const emptyState = document.getElementById('emptyState');

            try {
                loadingContainer.style.display = 'block';
                categoriesContainer.style.display = 'none';
                emptyState.style.display = 'none';

                if (pagination) {
                    pagination.setLoading(true);
                }

                // Build query parameters
                const params = new URLSearchParams({
                    offset: offset.toString(),
                    limit: limit.toString()
                });

                if (currentSearchTerm) {
                    params.append('searchKey', currentSearchTerm);
                }

                const response = await fetch(`/v1/categories?${params.toString()}`, {
                    headers: AuthUtils.getAuthHeaders()
                });

                if (response.ok) {
                    const result = await response.json();
                    // Handle different response formats
                    if (result.data) {
                        // Format: { data: { items: [], count: number } }
                        categories = result.data.items || [];
                        const totalCount = result.data.count || result.data.totalCount || categories.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    } else {
                        // Direct format: { items: [], count: number }
                        categories = result.items || result;
                        const totalCount = result.count || categories.length;

                        // Update pagination
                        if (pagination) {
                            const currentPage = Math.floor(offset / limit) + 1;
                            pagination.updateData(totalCount, currentPage);
                        }
                    }

                    if (categories.length === 0) {
                        emptyState.style.display = 'block';
                    } else {
                        displayCategories(categories);
                        categoriesContainer.style.display = 'grid';
                    }
                } else {
                    throw new Error('Failed to load categories');
                }
            } catch (error) {
                console.error('Error loading categories:', error);
                showMessage('فشل في تحميل الفئات', 'error');
                emptyState.style.display = 'block';

                if (pagination) {
                    pagination.updateData(0);
                }
            } finally {
                loadingContainer.style.display = 'none';
                if (pagination) {
                    pagination.setLoading(false);
                }
            }
        }
        
        function displayCategories(categoriesToShow) {
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '';
            
            categoriesToShow.forEach(category => {
                const categoryCard = createCategoryCard(category);
                container.appendChild(categoryCard);
            });
        }
        
        function createCategoryCard(category) {
            const card = document.createElement('div');
            card.className = 'category-card';
            
            card.innerHTML = `
                <div class="category-header">
                    <div class="category-icon">${category.icon || '📂'}</div>
                    <div class="category-info">
                        <h3>${category.nameAr}</h3>
                        <p>${category.nameEn || ''}</p>
                    </div>
                </div>
                
                <div class="category-stats">
                    <div class="stat">
                        <div class="stat-number">${category.businessCount || 0}</div>
                        <div class="stat-label">الأعمال</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${category.id}</div>
                        <div class="stat-label">المعرف</div>
                    </div>
                </div>
                
                <div class="category-actions">
                    <a href="edit.html?id=${category.id}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <button class="btn btn-danger btn-sm" onclick="deleteCategory(${category.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            `;
            
            return card;
        }
        
        // Remove the old filterCategories function as search is now handled by loadCategories
        
        async function deleteCategory(categoryId) {
            if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
                return;
            }
            
            try {
                const response = await fetch(`/v1/categories/${categoryId}`, {
                    method: 'DELETE',
                    headers: AuthUtils.getAuthHeaders()
                });
                
                if (response.ok) {
                    showMessage('تم حذف الفئة بنجاح', 'success');
                    // Reload current page
                    const currentOffset = pagination ? pagination.getOffset() : 0;
                    const currentLimit = pagination ? pagination.pageSize : 12;
                    loadCategories(currentOffset, currentLimit);
                } else {
                    throw new Error('Failed to delete category');
                }
            } catch (error) {
                console.error('Error deleting category:', error);
                showMessage('فشل في حذف الفئة', 'error');
            }
        }
        
        function showMessage(message, type) {
            const responseMessage = document.getElementById('responseMessage');
            responseMessage.textContent = message;
            responseMessage.className = `response-message ${type}`;
            responseMessage.style.display = 'block';
            
            setTimeout(() => {
                responseMessage.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
