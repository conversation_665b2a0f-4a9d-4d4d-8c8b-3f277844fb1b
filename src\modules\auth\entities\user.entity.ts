import { Type } from 'class-transformer';
import {
  IsDate,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  <PERSON>Length,
  ValidateNested,
} from 'class-validator';
import {
  BaseEntity,
  CountryDialCode,
  IsPhoneNumberForRegion,
} from '../../../common';
import { UserStatus } from '../enums/user-status.enum';
import { Role } from './role.entity';

export class User extends BaseEntity {
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  @IsPhoneNumberForRegion('countryCode')
  phoneNumber?: string;

  @IsOptional()
  @IsEnum(CountryDialCode)
  countryCode?: CountryDialCode;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  apiKey?: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  @MaxLength(20)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message:
      'Password is too weak and must contain at least one uppercase letter, one lowercase letter, one number or special character',
  })
  password: string;

  @IsOptional()
  @IsNumber()
  age?: number;

  @IsOptional()
  @IsDate()
  birthDate?: Date;

  @IsNotEmpty()
  @IsEnum(UserStatus)
  status: UserStatus = UserStatus.ACTIVE;

  @IsNotEmpty()
  @IsNumber()
  roleId: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => Role)
  role?: Role;
}
