import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { DataSource } from 'typeorm';
import { AppModule } from '../../src/app.module';
import { Permission } from '../../src/modules/auth/entities/permission.entity';
import { AuthHelper } from '../helpers/auth.helper';
import { ErrorHelper } from '../helpers/error.helper';
import { getValidationPipe } from '@app/common';

describe('Permission Controller (e2e)', () => {
  let app: INestApplication<App>;
  let dataSource: DataSource;
  let authToken: string;
  let testPermission: Permission;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(getValidationPipe());
    await app.init();

    dataSource = app.get(DataSource);

    // Login once before all tests
    const tokens = await AuthHelper.login(app);
    authToken = tokens.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up test data with E2E_TESTING prefix
    await dataSource.query(`
      DELETE FROM permissions
      WHERE action LIKE 'E2E_TESTING%'
    `);
  });

  describe('/permissions (POST)', () => {
    it('should create a new permission', async () => {
      const createPermissionDto = {
        action: 'E2E_TESTING test create',
        manuallyAdded: true,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.action).toBe(createPermissionDto.action);
      expect(response.body.manuallyAdded).toBe(
        createPermissionDto.manuallyAdded,
      );
      expect(response.body).toHaveProperty('createdAt');
      expect(response.body).toHaveProperty('updatedAt');
    });

    it('should fail when creating permission without authentication', async () => {
      const createPermissionDto = {
        action: 'E2E_TESTING test create',
        manuallyAdded: true,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions')
        .send(createPermissionDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Create permission without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail when creating permission without action', async () => {
      const createPermissionDto = {
        manuallyAdded: true,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create permission without action',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['action']);
    });

    it('should fail when creating permission without manuallyAdded', async () => {
      const createPermissionDto = {
        action: 'E2E_TESTING test create',
      };

      const response = await request(app.getHttpServer())
        .post('/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create permission without manuallyAdded',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['manuallyAdded']);
    });

    it('should fail when creating permission with empty action', async () => {
      const createPermissionDto = {
        action: '',
        manuallyAdded: true,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create permission with empty action',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['action']);
    });
  });

  describe('/permissions (GET)', () => {
    beforeEach(async () => {
      // Create test permissions
      const permissionRepo = dataSource.getRepository(Permission);
      await permissionRepo.save({
        action: 'E2E_TESTING test read 1',
        manuallyAdded: true,
      });

      await permissionRepo.save({
        action: 'E2E_TESTING test read 2',
        manuallyAdded: false,
      });
    });

    it('should get all permissions with pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ offset: 0, limit: 10 })
        .expect(200);

      // getAll returns {items: [], count: number}
      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('count');
      expect(response.body.items).toBeInstanceOf(Array);
      expect(response.body.items.length).toBeGreaterThanOrEqual(2);
      expect(response.body.count).toBeGreaterThanOrEqual(2);
    });

    it('should filter permissions by search term', async () => {
      const response = await request(app.getHttpServer())
        .get('/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ searchKey: 'E2E_TESTING test read 1' })
        .expect(200);

      // Ensure all returned items match the search criteria
      expect(response.body.items.length).toBeGreaterThan(0);
      response.body.items.forEach((permission: any) => {
        expect(permission.action).toContain('E2E_TESTING test read 1');
      });
    });

    it('should fail to get permissions without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get('/permissions')
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get permissions without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/permissions/:id (GET)', () => {
    beforeEach(async () => {
      const permissionRepo = dataSource.getRepository(Permission);
      testPermission = await permissionRepo.save({
        action: 'E2E_TESTING test get by id',
        manuallyAdded: true,
      });
    });

    it('should get a permission by ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.id).toBe(testPermission.id);
      expect(response.body.action).toBe(testPermission.action);
      expect(response.body.manuallyAdded).toBe(testPermission.manuallyAdded);
    });

    it('should return 404 for non-existent permission', async () => {
      const response = await request(app.getHttpServer())
        .get('/permissions/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Get non-existent permission',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to get permission without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get(`/permissions/${testPermission.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get permission without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/permissions/:id (PUT)', () => {
    beforeEach(async () => {
      const permissionRepo = dataSource.getRepository(Permission);
      testPermission = await permissionRepo.save({
        action: 'E2E_TESTING test update',
        manuallyAdded: true,
      });
    });

    it('should update a permission', async () => {
      const updatePermissionDto = {
        id: testPermission.id,
        action: 'E2E_TESTING updated permission',
        manuallyAdded: false,
      };

      const response = await request(app.getHttpServer())
        .put(`/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updatePermissionDto)
        .expect(200);

      expect(response.body.id).toBe(testPermission.id);
      expect(response.body.action).toBe(updatePermissionDto.action);
      expect(response.body.manuallyAdded).toBe(
        updatePermissionDto.manuallyAdded,
      );
    });

    it('should fail to update non-existent permission', async () => {
      const updatePermissionDto = {
        id: 99999,
        action: 'E2E_TESTING updated permission',
        manuallyAdded: false,
      };

      const response = await request(app.getHttpServer())
        .put('/permissions/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updatePermissionDto)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Update non-existent permission',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to update permission without authentication', async () => {
      const updatePermissionDto = {
        action: 'E2E_TESTING updated permission',
        manuallyAdded: false,
      };

      const response = await request(app.getHttpServer())
        .put(`/permissions/${testPermission.id}`)
        .send(updatePermissionDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Update permission without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail to update permission with invalid data', async () => {
      const updatePermissionDto = {
        action: '',
        manuallyAdded: 'invalid',
      };

      const response = await request(app.getHttpServer())
        .put(`/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updatePermissionDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Update permission with invalid data',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, [
        'action',
        'manuallyAdded',
      ]);
    });
  });

  describe('/permissions/:id (DELETE)', () => {
    beforeEach(async () => {
      const permissionRepo = dataSource.getRepository(Permission);
      testPermission = await permissionRepo.save({
        action: 'E2E_TESTING test delete',
        manuallyAdded: true,
      });
    });

    it('should delete a permission', async () => {
      await request(app.getHttpServer())
        .delete(`/permissions/${testPermission.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify permission is deleted
      const permissionRepo = dataSource.getRepository(Permission);
      const deletedPermission = await permissionRepo.findOne({
        where: { id: testPermission.id },
      });
      expect(deletedPermission).toBeNull();
    });

    it('should return 404 when deleting non-existent permission', async () => {
      const response = await request(app.getHttpServer())
        .delete('/permissions/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Delete non-existent permission',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to delete permission without authentication', async () => {
      const response = await request(app.getHttpServer())
        .delete(`/permissions/${testPermission.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Delete permission without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/permissions/find-or-create (POST)', () => {
    it('should create a new permission when it does not exist', async () => {
      const createPermissionDto = {
        action: 'E2E_TESTING find or create new',
        manuallyAdded: true,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions/find-or-create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(200);

      expect(response.body).toHaveProperty('id');
      expect(response.body.action).toBe(createPermissionDto.action);
      expect(response.body.manuallyAdded).toBe(
        createPermissionDto.manuallyAdded,
      );
    });

    it('should return existing permission when it already exists', async () => {
      // First, create a permission
      const permissionRepo = dataSource.getRepository(Permission);
      const existingPermission = await permissionRepo.save({
        action: 'E2E_TESTING find or create existing',
        manuallyAdded: false,
      });

      const createPermissionDto = {
        action: 'E2E_TESTING find or create existing',
        manuallyAdded: true, // Different value, but should return existing
      };

      const response = await request(app.getHttpServer())
        .post('/permissions/find-or-create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(200);

      expect(response.body.id).toBe(existingPermission.id);
      expect(response.body.action).toBe(existingPermission.action);
      expect(response.body.manuallyAdded).toBe(
        existingPermission.manuallyAdded,
      ); // Should keep original value
    });

    it('should fail without authentication', async () => {
      const createPermissionDto = {
        action: 'E2E_TESTING find or create',
        manuallyAdded: true,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions/find-or-create')
        .send(createPermissionDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Find or create permission without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail with invalid data', async () => {
      const createPermissionDto = {
        action: '',
        manuallyAdded: 'invalid',
      };

      const response = await request(app.getHttpServer())
        .post('/permissions/find-or-create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPermissionDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Find or create permission with invalid data',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, [
        'action',
        'manuallyAdded',
      ]);
    });
  });
});
