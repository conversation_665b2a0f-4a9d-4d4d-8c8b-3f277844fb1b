version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: qareeb-backend
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=********************************************/qareeb
      - SWAGGER_PATH=docs
      - SWAGGER_DESCRIPTION=Qareeb API Documentation
      - SWAGGER_VERSION=1.0
      - HOST_URL=http://localhost:3000
      - SWAGGER_TITLE=Qareeb API
      - SWAGGER_DESCRIPTION=Qareeb API Documentation
    depends_on:
      - postgres
    volumes:
      - ./logs:/app/logs

  postgres:
    image: postgres:16-alpine
    container_name: qareeb-postgres
    restart: unless-stopped
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=qareeb
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
