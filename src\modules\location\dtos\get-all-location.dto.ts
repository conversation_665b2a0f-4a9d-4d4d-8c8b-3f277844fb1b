import { Transform } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, <PERSON>, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { GetAllDto } from '@app/common';

export class GetAllLocationDto extends GetAllDto {
  @ApiProperty({
    description: 'Filter by latitude (minimum)',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Transform(({ value }) => parseFloat(value))
  minLatitude?: number;

  @ApiProperty({
    description: 'Filter by latitude (maximum)',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Transform(({ value }) => parseFloat(value))
  maxLatitude?: number;

  @ApiProperty({
    description: 'Filter by longitude (minimum)',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Transform(({ value }) => parseFloat(value))
  minLongitude?: number;

  @ApiProperty({
    description: 'Filter by longitude (maximum)',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Transform(({ value }) => parseFloat(value))
  maxLongitude?: number;

  @ApiProperty({
    description: 'Center latitude for radius search',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Transform(({ value }) => parseFloat(value))
  centerLatitude?: number;

  @ApiProperty({
    description: 'Center longitude for radius search',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Transform(({ value }) => parseFloat(value))
  centerLongitude?: number;

  @ApiProperty({
    description: 'Radius in kilometers for proximity search',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  radiusKm?: number;

  @ApiProperty({
    description: 'Filter by city (English)',
    required: false,
    type: String,
  })
  @IsOptional()
  cityEn?: string;

  @ApiProperty({
    description: 'Filter by city (Arabic)',
    required: false,
    type: String,
  })
  @IsOptional()
  cityAr?: string;

  @ApiProperty({
    description: 'Filter by country (English)',
    required: false,
    type: String,
  })
  @IsOptional()
  countryEn?: string;

  @ApiProperty({
    description: 'Filter by country (Arabic)',
    required: false,
    type: String,
  })
  @IsOptional()
  countryAr?: string;

  @ApiProperty({
    description: 'Filter by parent location ID',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  parentId?: number;
}
