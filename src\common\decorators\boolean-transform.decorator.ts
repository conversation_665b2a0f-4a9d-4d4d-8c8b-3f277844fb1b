import { Transform } from 'class-transformer';

const BooleanTransform = () => {
  const toPlain = Transform(
    ({ value }) => {
      return value;
    },
    {
      toPlainOnly: true,
    },
  );
  const toClass = (target: any, key: string) => {
    return Transform(
      ({ obj }) => {
        return valueToBoolean(obj[key]);
      },
      {
        toClassOnly: true,
      },
    )(target, key);
  };
  return function (target: any, key: string) {
    toPlain(target, key);
    toClass(target, key);
  };
};

const valueToBoolean = (value: unknown): boolean | undefined => {
  if (typeof value === 'boolean') {
    return value;
  }

  if (typeof value === 'number') {
    return value === 1 ? true : value === 0 ? false : undefined;
  }

  if (typeof value === 'string') {
    const normalized = value.toLowerCase();

    if (['true', 'on', 'yes', '1'].includes(normalized)) {
      return true;
    }

    if (['false', 'off', 'no', '0'].includes(normalized)) {
      return false;
    }
  }

  return undefined;
};

export { BooleanTransform };
