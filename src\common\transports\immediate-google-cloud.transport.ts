import { LoggingWinston } from '@google-cloud/logging-winston';
import { transport } from 'winston';

export interface CloudLoggingFilter {
  levels?: string[]; // Log levels to send to cloud (e.g., ['error', 'warn'])
  contexts?: string[]; // Contexts to send to cloud (e.g., ['AuthService', 'PaymentService'])
  shouldLog?: (info: any) => boolean; // Custom filter function
}

export interface CloudLogEntry {
  message: string;
  level: string;
  context?: string;
  correlationId?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
  error?: {
    name?: string;
    message?: string;
    stack?: string;
    code?: string | number;
    details?: any;
  };
  request?: {
    method?: string;
    url?: string;
    headers?: Record<string, any>;
    body?: any;
    params?: any;
    query?: any;
    ip?: string;
  };
  response?: {
    statusCode?: number;
    body?: any;
    headers?: Record<string, any>;
  };
  performance?: {
    duration?: number;
    memory?: number;
  };
  environment?: string;
  labels?: Record<string, string>;
}

type LogCallback = (
  error?: any,
  level?: string,
  msg?: string,
  meta?: any,
) => void;

interface GoogleCloudTransportOptions extends transport.TransportStreamOptions {
  projectId: string;
  keyFilename: string;
  logName?: string;
  labels?: Record<string, string>;
  writeInterval?: number;
  batchSize?: number;
  maxRetries?: number;
  flushLevel?: string;
}

export class ImmediateGoogleCloudTransport extends LoggingWinston {
  private filter: CloudLoggingFilter;
  private readonly consoleLogger = console;

  constructor(
    opts: GoogleCloudTransportOptions,
    filter: CloudLoggingFilter = {},
  ) {
    super(opts);
    this.handleExceptions = true;
    this.filter = filter;

    // Log transport initialization
    this.consoleLogger.debug(
      'Initializing Google Cloud Transport with options:',
      {
        projectId: opts.projectId,
        logName: opts.logName,
        labels: opts.labels,
      },
    );
  }

  private shouldLogToCloud(info: any): boolean {
    // If no filters are set, don't log to cloud by default
    if (
      !this.filter.levels &&
      !this.filter.contexts &&
      !this.filter.shouldLog
    ) {
      this.consoleLogger.debug('No filters set, skipping cloud logging');
      return false;
    }

    // Check custom filter function first
    if (this.filter.shouldLog && !this.filter.shouldLog(info)) {
      this.consoleLogger.debug('Custom filter rejected log entry');
      return false;
    }

    // Check level filter
    if (this.filter.levels && !this.filter.levels.includes(info.level)) {
      this.consoleLogger.debug(
        `Log level ${info.level} not in allowed levels:`,
        this.filter.levels,
      );
      return false;
    }

    // Check context filter
    if (
      this.filter.contexts &&
      info.context &&
      !this.filter.contexts.includes(info.context)
    ) {
      this.consoleLogger.debug(
        `Context ${info.context} not in allowed contexts:`,
        this.filter.contexts,
      );
      return false;
    }

    this.consoleLogger.debug(
      'Log entry passed all filters, will be sent to cloud',
    );
    return true;
  }

  private enhanceLogEntry(info: any): CloudLogEntry {
    const now = new Date();
    const baseEntry: CloudLogEntry = {
      message: info.message,
      level: info.level,
      context: info.context,
      correlationId: info.correlationId,
      timestamp: now.toISOString(),
      environment: process.env.NODE_ENV || 'development',
      performance: {
        duration: info.duration || info.ms,
        memory: process.memoryUsage().heapUsed,
      },
    };

    // Add error details if present
    if (info.error || info.stack) {
      baseEntry.error = {
        name: info.error?.name || 'Error',
        message: info.error?.message || info.message,
        stack: info.error?.stack || info.stack,
        code: info.error?.code,
        details: info.error?.details,
      };
    }

    // Add request/response if present
    if (info.request) {
      baseEntry.request = info.request;
    }
    if (info.response) {
      baseEntry.response = info.response;
    }

    // Add any additional metadata
    const metadata = { ...info };
    [
      'message',
      'level',
      'context',
      'correlationId',
      'timestamp',
      'error',
      'stack',
      'request',
      'response',
      'ms',
      'duration',
    ].forEach((key) => {
      delete metadata[key];
    });
    if (Object.keys(metadata).length > 0) {
      baseEntry.metadata = metadata;
    }

    return baseEntry;
  }

  log(info: any, callback: LogCallback): void {
    try {
      // Only log to cloud if it passes the filters
      if (!this.shouldLogToCloud(info)) {
        callback(); // Skip logging to cloud but continue
        return;
      }

      // Enhance the log entry with additional context
      const enhancedInfo = this.enhanceLogEntry(info);

      this.consoleLogger.debug('Attempting to send log to Google Cloud:', {
        level: enhancedInfo.level,
        message: enhancedInfo.message,
        context: enhancedInfo.context,
      });

      setImmediate(() => {
        super.log(
          enhancedInfo,
          (error?: any, level?: string, msg?: string, meta?: any) => {
            if (error) {
              this.consoleLogger.error(
                'Failed to send log to Google Cloud:',
                error,
              );
            } else {
              this.consoleLogger.debug('Successfully sent log to Google Cloud');
            }
            if (callback) {
              callback(error, level, msg, meta);
            }
          },
        );
      });
    } catch (error) {
      this.consoleLogger.error('Error in Google Cloud Transport:', error);
      callback(error);
    }
  }

  // Helper methods for different log levels
  error(entry: Partial<CloudLogEntry>): void {
    this.log({ ...entry, level: 'error' }, () => {});
  }

  warn(entry: Partial<CloudLogEntry>): void {
    this.log({ ...entry, level: 'warn' }, () => {});
  }

  info(entry: Partial<CloudLogEntry>): void {
    this.log({ ...entry, level: 'info' }, () => {});
  }

  debug(entry: Partial<CloudLogEntry>): void {
    this.log({ ...entry, level: 'debug' }, () => {});
  }

  verbose(entry: Partial<CloudLogEntry>): void {
    this.log({ ...entry, level: 'verbose' }, () => {});
  }

  // Method to log HTTP request/response
  logHttp(entry: Partial<CloudLogEntry>): void {
    this.log(
      {
        ...entry,
        level: 'http',
        performance: {
          ...entry.performance,
          memory: process.memoryUsage().heapUsed,
        },
      },
      () => {},
    );
  }

  // Method specifically for logging exceptions
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  logException(entry: Partial<CloudLogEntry> & { error: Error | any }): void {
    this.error({
      ...entry,
      level: 'error',
      performance: {
        ...entry.performance,
        memory: process.memoryUsage().heapUsed,
      },
    });
  }
}
