import { IGenericService } from '@app/common';
import { CreatePermissionsDto } from '../../dtos/permission/create-permissions.dto';
import { GetAllPermissionsDto } from '../../dtos/permission/get-all-permissions.dto';
import { Permission } from '../../entities/permission.entity';
import { PermissionSchema } from '../../schemes/permission.schema';

export interface IPermissionService
  extends IGenericService<
    Permission,
    typeof PermissionSchema,
    GetAllPermissionsDto,
    CreatePermissionsDto,
    CreatePermissionsDto
  > {
  findOrCreate(createDto: CreatePermissionsDto): Promise<Permission>;
}

export const IPermissionService = Symbol('IPermissionService');
