import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '../../../infrastructure/database/schemas/base.schema';
import { Permission } from '../entities/permission.entity';

export const PermissionSchema = new EntitySchema<Permission>({
  name: Permission.name,
  target: Permission,
  tableName: 'permissions',
  columns: {
    ...BaseSchemaProperties,
    action: {
      type: String,
      nullable: false,
      unique: true,
    },
    manuallyAdded: {
      type: Boolean,
      nullable: false,
    },
  },
});
