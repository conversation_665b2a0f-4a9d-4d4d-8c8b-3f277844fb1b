import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GenericRepository } from '@app/common';
import { Repository } from 'typeorm';
import { Feature } from '../entities/feature.entity';
import { IFeatureRepository } from '../interfaces/feature-repository/feature-repository.interface';
import { FeatureSchema } from '../schemes/feature.schema';

@Injectable()
export class FeatureRepository
  extends GenericRepository<Feature, typeof FeatureSchema>
  implements IFeatureRepository
{
  constructor(
    @InjectRepository(FeatureSchema)
    private readonly featureRepository: Repository<Feature>,
  ) {
    super(featureRepository);
  }

  async findByName(name: string): Promise<Feature | null> {
    return this.featureRepository.findOne({
      where: { nameEn: name },
    });
  }
}
