import {
  buildMessage,
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { CountryCode, isValidPhoneNumber } from 'libphonenumber-js';
import { countries } from '../constants/country-codes.constant';

export function IsPhoneNumberForRegion(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (target: unknown, propertyName: string) {
    registerDecorator({
      name: 'isPhoneNumberForRegion',
      target: (target as any).constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          // the property name that holds the country code
          const [countryCodeField] = args.constraints;

          // the value of the country code on the target object
          const countryCode = (args.object as any)[countryCodeField];

          // validate phone number for a specified region
          const country = countries.find(
            (country) => country.dialCode == countryCode,
          );

          if (/\D/.test(value)) return false;

          return isValidPhoneNumber(
            `${countryCode}${value}`,
            (country ? country.isoCode : 'QA') as CountryCode,
          );
        },
        // specify custom error message
        defaultMessage: buildMessage(
          (eachPrefix) =>
            `${eachPrefix} $property must be a valid phone number in the specified region`,
          validationOptions,
        ),
      },
    });
  };
}
