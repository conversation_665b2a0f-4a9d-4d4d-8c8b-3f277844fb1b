/**
 * Reusable Pagination Component
 * Handles pagination logic and UI for list pages
 */

class PaginationManager {
    constructor(options = {}) {
        this.currentPage = 1;
        this.pageSize = options.pageSize || 10;
        this.totalItems = 0;
        this.totalPages = 0;
        this.containerId = options.containerId || 'paginationContainer';
        this.onPageChange = options.onPageChange || (() => {});
        this.onPageSizeChange = options.onPageSizeChange || (() => {});
        this.maxVisiblePages = options.maxVisiblePages || 5;
        this.pageSizeOptions = options.pageSizeOptions || [5, 10, 20, 50];
        this.showPageSizeSelector = options.showPageSizeSelector !== false;
        this.showQuickJump = options.showQuickJump || false;
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.render();
    }
    
    createContainer() {
        const existingContainer = document.getElementById(this.containerId);
        if (existingContainer) {
            return;
        }
        
        const container = document.createElement('div');
        container.id = this.containerId;
        container.className = 'pagination-container';
        
        // Find a good place to insert the pagination
        const contentContainer = document.querySelector('.content-container, .container');
        if (contentContainer) {
            contentContainer.appendChild(container);
        }
    }
    
    updateData(totalItems, currentPage = 1) {
        this.totalItems = totalItems;
        this.totalPages = Math.ceil(totalItems / this.pageSize);
        this.currentPage = Math.max(1, Math.min(currentPage, this.totalPages));
        this.render();
    }
    
    setPageSize(newPageSize) {
        this.pageSize = newPageSize;
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
        this.currentPage = 1; // Reset to first page when changing page size
        this.render();
        this.onPageSizeChange(this.pageSize);
    }
    
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }
        
        this.currentPage = page;
        this.render();
        this.onPageChange(this.currentPage, this.getOffset());
    }
    
    getOffset() {
        return (this.currentPage - 1) * this.pageSize;
    }
    
    getCurrentRange() {
        if (this.totalItems === 0) {
            return { start: 0, end: 0 };
        }
        
        const start = this.getOffset() + 1;
        const end = Math.min(start + this.pageSize - 1, this.totalItems);
        return { start, end };
    }
    
    render() {
        const container = document.getElementById(this.containerId);
        if (!container) return;
        
        if (this.totalItems === 0) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'flex';
        
        const { start, end } = this.getCurrentRange();
        
        container.innerHTML = `
            <div class="pagination-info">
                <div class="pagination-summary">
                    <span>عرض</span>
                    <span class="current-range">${start} - ${end}</span>
                    <span>من</span>
                    <span class="total-items">${this.totalItems}</span>
                    <span>عنصر</span>
                </div>
            </div>
            
            <div class="pagination-controls">
                ${this.showPageSizeSelector ? this.renderPageSizeSelector() : ''}
                ${this.renderNavigation()}
                ${this.showQuickJump ? this.renderQuickJump() : ''}
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    renderPageSizeSelector() {
        return `
            <div class="pagination-size-selector">
                <label for="pageSize">عرض:</label>
                <select id="pageSize">
                    ${this.pageSizeOptions.map(size => 
                        `<option value="${size}" ${size === this.pageSize ? 'selected' : ''}>${size}</option>`
                    ).join('')}
                </select>
                <span>عنصر</span>
            </div>
        `;
    }
    
    renderNavigation() {
        if (this.totalPages <= 1) {
            return '<div class="pagination-nav"></div>';
        }
        
        const pages = this.getVisiblePages();
        
        return `
            <div class="pagination-nav">
                <button class="pagination-btn prev ${this.currentPage === 1 ? 'disabled' : ''}" 
                        data-page="${this.currentPage - 1}">
                    <i class="fas fa-chevron-right"></i>
                    <span class="btn-text">السابق</span>
                </button>
                
                ${pages.map(page => {
                    if (page === '...') {
                        return '<span class="pagination-ellipsis">...</span>';
                    }
                    return `
                        <button class="pagination-btn ${page === this.currentPage ? 'active' : ''}" 
                                data-page="${page}">
                            ${page}
                        </button>
                    `;
                }).join('')}
                
                <button class="pagination-btn next ${this.currentPage === this.totalPages ? 'disabled' : ''}" 
                        data-page="${this.currentPage + 1}">
                    <span class="btn-text">التالي</span>
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        `;
    }
    
    renderQuickJump() {
        return `
            <div class="pagination-jump">
                <span>الذهاب إلى:</span>
                <input type="number" id="jumpToPage" min="1" max="${this.totalPages}" placeholder="${this.currentPage}">
                <button id="jumpBtn">اذهب</button>
            </div>
        `;
    }
    
    getVisiblePages() {
        const pages = [];
        const half = Math.floor(this.maxVisiblePages / 2);
        let start = Math.max(1, this.currentPage - half);
        let end = Math.min(this.totalPages, start + this.maxVisiblePages - 1);
        
        // Adjust start if we're near the end
        if (end - start + 1 < this.maxVisiblePages) {
            start = Math.max(1, end - this.maxVisiblePages + 1);
        }
        
        // Add first page and ellipsis if needed
        if (start > 1) {
            pages.push(1);
            if (start > 2) {
                pages.push('...');
            }
        }
        
        // Add visible pages
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        
        // Add ellipsis and last page if needed
        if (end < this.totalPages) {
            if (end < this.totalPages - 1) {
                pages.push('...');
            }
            pages.push(this.totalPages);
        }
        
        return pages;
    }
    
    attachEventListeners() {
        const container = document.getElementById(this.containerId);
        
        // Page size selector
        const pageSizeSelect = container.querySelector('#pageSize');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.setPageSize(parseInt(e.target.value));
            });
        }
        
        // Navigation buttons
        const navButtons = container.querySelectorAll('.pagination-btn:not(.disabled)');
        navButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.currentTarget.dataset.page);
                if (page && !isNaN(page)) {
                    this.goToPage(page);
                }
            });
        });
        
        // Quick jump
        const jumpInput = container.querySelector('#jumpToPage');
        const jumpBtn = container.querySelector('#jumpBtn');
        
        if (jumpInput && jumpBtn) {
            const handleJump = () => {
                const page = parseInt(jumpInput.value);
                if (page && page >= 1 && page <= this.totalPages) {
                    this.goToPage(page);
                    jumpInput.value = '';
                }
            };
            
            jumpBtn.addEventListener('click', handleJump);
            jumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    handleJump();
                }
            });
        }
    }
    
    setLoading(isLoading) {
        const container = document.getElementById(this.containerId);
        if (container) {
            container.classList.toggle('pagination-loading', isLoading);
        }
    }
    
    destroy() {
        const container = document.getElementById(this.containerId);
        if (container) {
            container.remove();
        }
    }
}

// Export for use in other scripts
window.PaginationManager = PaginationManager;
