import {
  EntityManager,
  EntitySchema,
  FindManyOptions,
  ObjectLiteral,
} from 'typeorm';
import { IGetAllResponseInterface } from './general-response.interface';

export interface IGenericService<
  Entity extends ObjectLiteral,
  Schema extends EntitySchema<Entity>,
  GetAllDto,
  CreateDto,
  UpdateDto,
> {
  getAll(
    getAllDto: GetAllDto,
    initialQuery?: FindManyOptions<Entity>,
  ): Promise<IGetAllResponseInterface<Entity>>;

  findOneById(id: number, manager?: EntityManager): Promise<Entity>;

  create(entityType: new () => Entity, createDto: CreateDto): Promise<Entity>;
  create(
    entityType: new () => Entity,
    createDto: CreateDto,
    entityManager?: EntityManager,
  ): Promise<Entity>;

  update(entityType: new () => Entity, updateDto: UpdateDto): Promise<Entity>;

  update(
    entityType: new () => Entity,
    updateDto: UpdateDto,
    entityManager: EntityManager,
  ): Promise<Entity>;

  updateMany(
    entityType: new () => Entity,
    updateDto: UpdateDto[],
    entityManager?: EntityManager,
  ): Promise<Entity[]>;

  createMany(
    entityType: new () => Entity,
    createDto: CreateDto[],
    entityManager?: EntityManager,
  ): Promise<Entity[]>;

  deleteOne(id: number): Promise<void>;

  save(entities: Entity[], entityManager?: EntityManager): Promise<Entity[]>;
}

export const IGenericService = Symbol('IGenericService');
