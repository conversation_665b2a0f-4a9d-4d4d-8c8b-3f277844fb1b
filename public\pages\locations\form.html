<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موقع جديد - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/forms.css">
    
    <style>
        .coordinates-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
        
        .coordinates-preview.valid {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .coordinates-preview.invalid {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .map-url-helper {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 0.9rem;
        }
        
        .map-url-helper h4 {
            margin-bottom: 10px;
            color: #1976d2;
        }
        
        .map-url-helper ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .map-url-helper li {
            margin-bottom: 5px;
        }
        
        .coordinate-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        @media (max-width: 768px) {
            .coordinate-inputs {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">إضافة موقع جديد</h1>
            <p class="page-subtitle">أنشئ موقعاً جغرافياً جديداً في النظام</p>
        </div>
        
        <div class="form-container">
            <form id="locationForm">
                <div class="form-section">
                    <h2>📍 المعلومات الأساسية</h2>
                    
                    <div class="form-group">
                        <div class="bilingual-label">
                            <span class="ar">اسم الموقع (عربي)</span>
                            <span class="en">Location Name (Arabic)</span>
                        </div>
                        <input type="text" id="nameAr" name="nameAr" required dir="rtl" placeholder="أدخل اسم الموقع بالعربية">
                    </div>
                    
                    <div class="form-group">
                        <div class="bilingual-label">
                            <span class="ar">اسم الموقع (إنجليزي)</span>
                            <span class="en">Location Name (English)</span>
                        </div>
                        <input type="text" id="nameEn" name="nameEn" required dir="ltr" placeholder="Enter location name in English">
                    </div>
                </div>
                
                <div class="form-section">
                    <h2>🗺️ الإحداثيات</h2>
                    
                    <div class="form-group">
                        <label for="mapUrl">رابط الخريطة (اختياري)</label>
                        <input type="url" id="mapUrl" name="mapUrl" dir="ltr" placeholder="https://maps.google.com/...">
                        <small>الصق رابط من خرائط جوجل لاستخراج الإحداثيات تلقائياً</small>
                        
                        <div class="map-url-helper">
                            <h4>كيفية الحصول على رابط الخريطة:</h4>
                            <ul>
                                <li>افتح خرائط جوجل واختر الموقع</li>
                                <li>انقر بزر الماوس الأيمن على الموقع</li>
                                <li>اختر "ما هذا المكان؟"</li>
                                <li>انسخ الرابط من شريط العنوان</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="coordinate-inputs">
                        <div class="form-group">
                            <label for="latitude">خط العرض (Latitude) *</label>
                            <input type="number" id="latitude" name="latitude" required step="any" min="-90" max="90" dir="ltr" placeholder="24.7136">
                        </div>
                        
                        <div class="form-group">
                            <label for="longitude">خط الطول (Longitude) *</label>
                            <input type="number" id="longitude" name="longitude" required step="any" min="-180" max="180" dir="ltr" placeholder="46.6753">
                        </div>
                    </div>
                    
                    <div class="coordinates-preview" id="coordinatesPreview">
                        الإحداثيات: غير محددة
                    </div>
                </div>
                
                <div class="form-section">
                    <h2>🏠 العنوان</h2>
                    
                    <div class="form-group">
                        <label for="streetAddressAr">عنوان الشارع (عربي)</label>
                        <input type="text" id="streetAddressAr" name="streetAddressAr" dir="rtl" placeholder="شارع الملك فهد، حي العليا">
                    </div>
                    
                    <div class="form-group">
                        <label for="streetAddressEn">عنوان الشارع (إنجليزي)</label>
                        <input type="text" id="streetAddressEn" name="streetAddressEn" dir="ltr" placeholder="King Fahd Road, Al Olaya District">
                    </div>
                    
                    <div class="coordinate-inputs">
                        <div class="form-group">
                            <label for="cityAr">المدينة (عربي)</label>
                            <input type="text" id="cityAr" name="cityAr" dir="rtl" placeholder="الرياض">
                        </div>
                        
                        <div class="form-group">
                            <label for="cityEn">المدينة (إنجليزي)</label>
                            <input type="text" id="cityEn" name="cityEn" dir="ltr" placeholder="Riyadh">
                        </div>
                    </div>
                    
                    <div class="coordinate-inputs">
                        <div class="form-group">
                            <label for="stateAr">المنطقة/الولاية (عربي)</label>
                            <input type="text" id="stateAr" name="stateAr" dir="rtl" placeholder="منطقة الرياض">
                        </div>
                        
                        <div class="form-group">
                            <label for="stateEn">المنطقة/الولاية (إنجليزي)</label>
                            <input type="text" id="stateEn" name="stateEn" dir="ltr" placeholder="Riyadh Region">
                        </div>
                    </div>
                    
                    <div class="coordinate-inputs">
                        <div class="form-group">
                            <label for="countryAr">البلد (عربي)</label>
                            <input type="text" id="countryAr" name="countryAr" dir="rtl" placeholder="المملكة العربية السعودية">
                        </div>
                        
                        <div class="form-group">
                            <label for="countryEn">البلد (إنجليزي)</label>
                            <input type="text" id="countryEn" name="countryEn" dir="ltr" placeholder="Saudi Arabia">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="postalCode">الرمز البريدي</label>
                        <input type="text" id="postalCode" name="postalCode" dir="ltr" placeholder="12345">
                    </div>
                </div>
                
                <div class="form-section">
                    <h2>🏛️ معلومات إضافية</h2>
                    
                    <div class="form-group">
                        <label for="nearestLandmarkAr">أقرب معلم (عربي)</label>
                        <input type="text" id="nearestLandmarkAr" name="nearestLandmarkAr" dir="rtl" placeholder="برج المملكة">
                    </div>
                    
                    <div class="form-group">
                        <label for="nearestLandmarkEn">أقرب معلم (إنجليزي)</label>
                        <input type="text" id="nearestLandmarkEn" name="nearestLandmarkEn" dir="ltr" placeholder="Kingdom Tower">
                    </div>
                    
                    <div class="form-group">
                        <label for="descriptionAr">الوصف (عربي)</label>
                        <textarea id="descriptionAr" name="descriptionAr" rows="3" dir="rtl" placeholder="وصف مختصر عن الموقع"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="descriptionEn">الوصف (إنجليزي)</label>
                        <textarea id="descriptionEn" name="descriptionEn" rows="3" dir="ltr" placeholder="Brief description of the location"></textarea>
                    </div>
                </div>
                
                <div class="form-section">
                    <h2>⚙️ إعدادات متقدمة</h2>
                    
                    <div class="form-group">
                        <label for="parentId">الموقع الأب (اختياري)</label>
                        <select id="parentId" name="parentId">
                            <option value="">موقع رئيسي</option>
                        </select>
                        <small>اختر موقعاً أب لإنشاء موقع فرعي</small>
                    </div>
                    
                    <div class="coordinate-inputs">
                        <div class="form-group">
                            <label for="accuracy">دقة الإحداثيات (0-5)</label>
                            <input type="number" id="accuracy" name="accuracy" min="0" max="5" step="0.1" dir="ltr" placeholder="4.5">
                            <small>مستوى دقة الإحداثيات</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="altitude">الارتفاع (متر)</label>
                            <input type="number" id="altitude" name="altitude" step="any" dir="ltr" placeholder="612">
                            <small>الارتفاع عن سطح البحر</small>
                        </div>
                    </div>
                    
                    <div class="coordinate-inputs">
                        <div class="form-group">
                            <label for="timezone">المنطقة الزمنية</label>
                            <input type="text" id="timezone" name="timezone" dir="ltr" placeholder="Asia/Riyadh">
                            <small>المنطقة الزمنية للموقع</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="placeId">معرف المكان (Google Place ID)</label>
                            <input type="text" id="placeId" name="placeId" dir="ltr" placeholder="ChIJ...">
                            <small>معرف المكان في خرائط جوجل</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span id="submitBtnText">إنشاء الموقع</span>
                        <div class="loading-spinner" id="submitSpinner" style="display: none;"></div>
                    </button>
                    <a href="list.html" class="btn btn-secondary">إلغاء</a>
                </div>
                
                <div class="response-message" id="responseMessage"></div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script>
        let locations = [];
        
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!AuthUtils.isAuthenticated()) {
                window.location.href = '../auth/login.html';
                return;
            }

            loadParentLocations();
            setupEventListeners();
        });

        function setupEventListeners() {
            const form = document.getElementById('locationForm');
            const mapUrlInput = document.getElementById('mapUrl');
            const latitudeInput = document.getElementById('latitude');
            const longitudeInput = document.getElementById('longitude');

            form.addEventListener('submit', handleSubmit);
            mapUrlInput.addEventListener('input', extractCoordinatesFromMapUrl);
            latitudeInput.addEventListener('input', updateCoordinatesPreview);
            longitudeInput.addEventListener('input', updateCoordinatesPreview);
        }

        function extractCoordinatesFromMapUrl() {
            const mapUrl = document.getElementById('mapUrl').value;
            if (!mapUrl) return;

            // Try to extract coordinates from Google Maps URL
            // Format: https://www.google.com/maps/@24.7136,46.6753,15z
            // Or: https://maps.google.com/?q=24.7136,46.6753
            const patterns = [
                /@(-?\d+\.?\d*),(-?\d+\.?\d*)/,  // @lat,lng format
                /q=(-?\d+\.?\d*),(-?\d+\.?\d*)/,  // q=lat,lng format
                /!3d(-?\d+\.?\d*)!4d(-?\d+\.?\d*)/, // 3d/4d format
            ];

            for (const pattern of patterns) {
                const match = mapUrl.match(pattern);
                if (match) {
                    const lat = parseFloat(match[1]);
                    const lng = parseFloat(match[2]);

                    if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                        document.getElementById('latitude').value = lat;
                        document.getElementById('longitude').value = lng;
                        updateCoordinatesPreview();
                        showMessage('تم استخراج الإحداثيات من رابط الخريطة بنجاح', 'success');
                        return;
                    }
                }
            }
        }

        function updateCoordinatesPreview() {
            const latitude = document.getElementById('latitude').value;
            const longitude = document.getElementById('longitude').value;
            const preview = document.getElementById('coordinatesPreview');

            if (latitude && longitude) {
                const lat = parseFloat(latitude);
                const lng = parseFloat(longitude);

                if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                    preview.textContent = `الإحداثيات: ${lat}, ${lng}`;
                    preview.className = 'coordinates-preview valid';
                } else {
                    preview.textContent = 'إحداثيات غير صحيحة';
                    preview.className = 'coordinates-preview invalid';
                }
            } else {
                preview.textContent = 'الإحداثيات: غير محددة';
                preview.className = 'coordinates-preview';
            }
        }

        async function loadParentLocations() {
            try {
                const response = await fetch('/v1/locations?limit=1000', {
                    headers: AuthUtils.getAuthHeaders()
                });
                if (response.ok) {
                    const result = await response.json();
                    locations = result.data?.items || result.data || result;
                    populateParentLocations();
                }
            } catch (error) {
                console.error('Error loading locations:', error);
            }
        }

        function populateParentLocations() {
            const select = document.getElementById('parentId');

            // Only show top-level locations as potential parents
            const topLevelLocations = locations.filter(loc => !loc.parentId);

            topLevelLocations.forEach(location => {
                const option = document.createElement('option');
                option.value = location.id;
                option.textContent = location.nameAr;
                select.appendChild(option);
            });
        }

        async function handleSubmit(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const submitBtnText = document.getElementById('submitBtnText');
            const submitSpinner = document.getElementById('submitSpinner');

            // Show loading state
            submitBtn.disabled = true;
            submitBtnText.style.display = 'none';
            submitSpinner.style.display = 'inline-block';

            try {
                const formData = new FormData(e.target);
                const locationData = {
                    nameAr: formData.get('nameAr'),
                    nameEn: formData.get('nameEn'),
                    latitude: parseFloat(formData.get('latitude')),
                    longitude: parseFloat(formData.get('longitude'))
                };

                // Add optional fields if they have values
                const optionalFields = [
                    'streetAddressAr', 'streetAddressEn', 'cityAr', 'cityEn',
                    'stateAr', 'stateEn', 'countryAr', 'countryEn', 'postalCode',
                    'nearestLandmarkAr', 'nearestLandmarkEn', 'descriptionAr', 'descriptionEn',
                    'mapUrl', 'placeId', 'timezone'
                ];

                optionalFields.forEach(field => {
                    const value = formData.get(field);
                    if (value) locationData[field] = value;
                });

                // Handle numeric fields
                const accuracy = formData.get('accuracy');
                if (accuracy) locationData.accuracy = parseFloat(accuracy);

                const altitude = formData.get('altitude');
                if (altitude) locationData.altitude = parseFloat(altitude);

                const parentId = formData.get('parentId');
                if (parentId) locationData.parentId = parseInt(parentId);

                const response = await fetch('/v1/locations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...AuthUtils.getAuthHeaders()
                    },
                    body: JSON.stringify(locationData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showMessage('تم إنشاء الموقع بنجاح!', 'success');
                    setTimeout(() => {
                        window.location.href = 'list.html';
                    }, 2000);
                } else {
                    showMessage(result.message || 'فشل في إنشاء الموقع', 'error');
                }
            } catch (error) {
                console.error('Error creating location:', error);
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                // Reset loading state
                submitBtn.disabled = false;
                submitBtnText.style.display = 'inline';
                submitSpinner.style.display = 'none';
            }
        }

        function showMessage(message, type) {
            const responseMessage = document.getElementById('responseMessage');
            responseMessage.textContent = message;
            responseMessage.className = `response-message ${type}`;
            responseMessage.style.display = 'block';

            if (type === 'success') {
                setTimeout(() => {
                    responseMessage.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
