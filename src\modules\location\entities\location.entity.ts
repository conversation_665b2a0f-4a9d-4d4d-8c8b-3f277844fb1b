import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { BaseEntity } from '../../../common';

export class Location extends BaseEntity {
  @IsNotEmpty()
  @IsString()
  nameEn: string;

  @IsNotEmpty()
  @IsString()
  nameAr: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  @IsOptional()
  @IsString()
  streetAddressEn?: string;

  @IsOptional()
  @IsString()
  streetAddressAr?: string;

  @IsOptional()
  @IsString()
  cityEn?: string;

  @IsOptional()
  @IsString()
  cityAr?: string;

  @IsOptional()
  @IsString()
  stateEn?: string;

  @IsOptional()
  @IsString()
  stateAr?: string;

  @IsOptional()
  @IsString()
  countryEn?: string;

  @IsOptional()
  @IsString()
  countryAr?: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsOptional()
  @IsString()
  nearestLandmarkEn?: string;

  @IsOptional()
  @IsString()
  nearestLandmarkAr?: string;

  @IsOptional()
  @IsString()
  descriptionEn?: string;

  @IsOptional()
  @IsString()
  descriptionAr?: string;

  @IsOptional()
  @IsString()
  @IsUrl()
  mapUrl?: string;

  @IsOptional()
  @IsString()
  placeId?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  accuracy?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  altitude?: number;

  @IsOptional()
  @IsString()
  timezone?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => Location)
  parent?: Location;

  @IsOptional()
  @IsNumber()
  parentId?: number;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Location)
  children?: Location[];
}
