import { DefaultR<PERSON> } from '@app/common';
import { Inject, Injectable } from '@nestjs/common';
import { DataSource, EntitySubscriberInterface, InsertEvent } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { IRoleService } from '../interfaces/role/role-service.interface';

@Injectable()
export class PermissionDataListener
  implements EntitySubscriberInterface<Permission>
{
  constructor(
    dataSource: DataSource,
    @Inject(IRoleService)
    private readonly roleService: IRoleService,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return Permission;
  }

  async afterInsert(event: InsertEvent<Permission>) {
    try {
      const permission = event.entity;
      const role = await this.roleService.findByName(DefaultRole.SUPER_ADMIN);
      await this.roleService.addNewPermission(role, permission, event.manager);
    } catch (error) {
      // Silently fail if a Super Admin role doesn't exist (e.g., in test environments),
      // This prevents breaking tests that don't have the role seeded
      if (process.env.NODE_ENV !== 'test') {
        console.error('Failed to add permission to Super Admin role:', error);
      }
    }
  }
}
