import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, IsNull } from 'typeorm';
import { GenericRepository } from '@app/common';
import { Location } from '../entities/location.entity';
import { LocationSchema } from '../schemes/location.schema';
import { ILocationRepository } from '../interfaces/location-repository/location-repository.interface';

@Injectable()
export class LocationRepository
  extends GenericRepository<Location, typeof LocationSchema>
  implements ILocationRepository
{
  constructor(
    @InjectRepository(LocationSchema)
    private readonly locationRepository: Repository<Location>,
  ) {
    super(locationRepository);
  }

  async findByCoordinates(
    latitude: number,
    longitude: number,
    radiusKm = 10,
  ): Promise<Location[]> {
    const latRadian = (latitude * Math.PI) / 180;
    const degLatKm = 110.574235;
    const degLongKm = 110.572833 * Math.cos(latRadian);
    const deltaLat = radiusKm / degLatKm;
    const deltaLong = radiusKm / degLongKm;

    return this.locationRepository
      .createQueryBuilder('location')
      .where('location.latitude BETWEEN :minLat AND :maxLat', {
        minLat: latitude - deltaLat,
        maxLat: latitude + deltaLat,
      })
      .andWhere('location.longitude BETWEEN :minLng AND :maxLng', {
        minLng: longitude - deltaLong,
        maxLng: longitude + deltaLong,
      })
      .orderBy(
        `(6371 * acos(cos(radians(:lat)) * cos(radians(location.latitude)) * cos(radians(location.longitude) - radians(:lng)) + sin(radians(:lat)) * sin(radians(location.latitude))))`,
        'ASC',
      )
      .setParameters({ lat: latitude, lng: longitude })
      .getMany();
  }

  async findWithinBounds(
    minLatitude: number,
    maxLatitude: number,
    minLongitude: number,
    maxLongitude: number,
  ): Promise<Location[]> {
    return this.locationRepository.find({
      where: {
        latitude: Between(minLatitude, maxLatitude),
        longitude: Between(minLongitude, maxLongitude),
      },
      order: { nameEn: 'ASC' },
    });
  }

  async findByCity(cityEn?: string, cityAr?: string): Promise<Location[]> {
    const whereConditions: any = {};

    if (cityEn) whereConditions.cityEn = cityEn;
    if (cityAr) whereConditions.cityAr = cityAr;

    return this.locationRepository.find({
      where: whereConditions,
      order: { nameEn: 'ASC' },
    });
  }

  async findByCountry(
    countryEn?: string,
    countryAr?: string,
  ): Promise<Location[]> {
    const whereConditions: any = {};

    if (countryEn) whereConditions.countryEn = countryEn;
    if (countryAr) whereConditions.countryAr = countryAr;

    return this.locationRepository.find({
      where: whereConditions,
      order: { cityEn: 'ASC', nameEn: 'ASC' },
    });
  }

  async findByParentId(parentId: number | null): Promise<Location[]> {
    return this.locationRepository.find({
      where: { parentId: parentId === null ? IsNull() : parentId },
      order: { nameEn: 'ASC' },
    });
  }

  async findWithChildren(id: number): Promise<Location | null> {
    return this.locationRepository.findOne({
      where: { id },
      relations: ['children', 'parent'],
    });
  }

  async findNearest(
    latitude: number,
    longitude: number,
    limit = 10,
  ): Promise<Location[]> {
    return this.locationRepository
      .createQueryBuilder('location')
      .orderBy(
        `(6371 * acos(cos(radians(:lat)) * cos(radians(location.latitude)) * cos(radians(location.longitude) - radians(:lng)) + sin(radians(:lat)) * sin(radians(location.latitude))))`,
        'ASC',
      )
      .setParameters({ lat: latitude, lng: longitude })
      .limit(limit)
      .getMany();
  }

  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
