import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IGetAllResponseInterface } from '../../../common/interfaces';
import { Permissions } from '../../../common/decorators/permissions.decorator';
import { CreatePermissionsDto } from '../dtos/permission/create-permissions.dto';
import { GetAllPermissionsDto } from '../dtos/permission/get-all-permissions.dto';
import { Permission } from '../entities/permission.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { IPermissionService } from '../interfaces/permission/permission-service.interface';
import { UpdatePermissionDto } from '../dtos/permission/update-permission.dto';

/**
 * Controller for managing permission entities
 */
@Controller('permissions')
@ApiTags('Permissions')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class PermissionController {
  constructor(
    @Inject(IPermissionService)
    private readonly permissionService: IPermissionService,
  ) {}

  /**
   * Get all permissions with pagination and filtering
   */
  @Get()
  @ApiOperation({
    summary: 'Get all permissions with pagination and filtering',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The permissions have been successfully retrieved.',
    type: Permission,
    isArray: true,
  })
  async getAll(
    @Query() getAllDto: GetAllPermissionsDto,
  ): Promise<IGetAllResponseInterface<Permission>> {
    return this.permissionService.getAll(getAllDto);
  }

  /**
   * Get permission by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get permission by ID' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The permission has been successfully retrieved.',
    type: Permission,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission not found.',
  })
  async getById(@Param('id', ParseIntPipe) id: number): Promise<Permission> {
    return this.permissionService.findOneById(id);
  }

  /**
   * Create new permission
   */
  @Post()
  @ApiOperation({ summary: 'Create new permission' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The permission has been successfully created.',
    type: Permission,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDto: CreatePermissionsDto): Promise<Permission> {
    return this.permissionService.create(Permission, createDto);
  }

  /**
   * Update an existing permission
   */
  @Put(':id')
  @ApiOperation({ summary: 'Update an existing permission' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The permission has been successfully updated.',
    type: Permission,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission not found.',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdatePermissionDto,
  ): Promise<Permission> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.permissionService.update(Permission, updateDtoWithId);
  }

  /**
   * Delete a permission
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a permission' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The permission has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.permissionService.deleteOne(id);
  }

  /**
   * Finds a permission by action or creates a new one if it doesn't exist
   * @param createDto The permission data to find or create
   * @returns The found or created permission
   */
  @Post('find-or-create')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Find or create a permission' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission found or created successfully',
    type: Permission,
  })
  async findOrCreate(
    @Body() createDto: CreatePermissionsDto,
  ): Promise<Permission> {
    return this.permissionService.findOrCreate(createDto);
  }
}
