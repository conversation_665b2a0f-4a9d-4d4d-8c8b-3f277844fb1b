import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '../../../infrastructure/database/schemas/base.schema';
import { Business } from '../entities/business.entity';
import { PriceRange } from '../enums/price-range.enum';

export const BusinessSchema = new EntitySchema<Business>({
  name: Business.name,
  target: Business,
  tableName: 'businesses',
  columns: {
    ...BaseSchemaProperties,
    nameAr: {
      type: String,
      nullable: false,
    },
    nameEn: {
      type: String,
      nullable: false,
    },
    phoneNumber: {
      type: String,
      nullable: true,
    },
    whatsAppNumber: {
      type: String,
      nullable: true,
    },
    priceRange: {
      type: 'enum',
      enum: PriceRange,
      nullable: true,
    },
    locationId: {
      type: Number,
      nullable: false,
    },
    primaryCategoryId: {
      type: Number,
      nullable: false,
    },
    operatingHours: {
      type: 'jsonb',
      nullable: true,
    },
    isOpen24x7: {
      type: Boolean,
      nullable: false,
      default: false,
    },
    ramadanHours: {
      type: 'jsonb',
      nullable: true,
    },
    logoUrl: {
      type: String,
      nullable: true,
    },
    coverPhotoUrl: {
      type: String,
      nullable: true,
    },
    galleryUrls: {
      type: 'jsonb',
      nullable: true,
      default: () => "'[]'",
    },
    shortDescriptionAr: {
      type: String,
      length: 160,
      nullable: true,
    },
    shortDescriptionEn: {
      type: String,
      length: 160,
      nullable: true,
    },
    fullDescriptionAr: {
      type: 'text',
      nullable: true,
    },
    fullDescriptionEn: {
      type: 'text',
      nullable: true,
    },
    isActive: {
      type: Boolean,
      nullable: false,
      default: false,
    },
    isVerified: {
      type: Boolean,
      nullable: false,
      default: false,
    },
    isPremium: {
      type: Boolean,
      nullable: false,
      default: false,
    },
    premiumExpiresAt: {
      type: 'timestamp',
      nullable: true,
    },
    averageRating: {
      type: 'decimal',
      precision: 3,
      scale: 2,
      nullable: false,
      default: 0,
    },
    totalReviewsCount: {
      type: Number,
      nullable: false,
      default: 0,
    },
    totalViewsCount: {
      type: Number,
      nullable: false,
      default: 0,
    },
    lastMonthViews: {
      type: Number,
      nullable: false,
      default: 0,
    },
    ownerUserId: {
      type: Number,
      nullable: true,
    },
    paymentMethods: {
      type: 'jsonb',
      nullable: true,
      default: () => "'[]'",
    },
  },
  relations: {
    location: {
      target: 'Location',
      type: 'many-to-one',
      nullable: false,
      joinColumn: { name: 'locationId' },
      onDelete: 'CASCADE',
    },
    primaryCategory: {
      target: 'Category',
      type: 'many-to-one',
      nullable: false,
      joinColumn: { name: 'primaryCategoryId' },
      onDelete: 'CASCADE',
    },
    owner: {
      target: 'User',
      type: 'many-to-one',
      nullable: true,
      joinColumn: { name: 'ownerUserId' },
      onDelete: 'CASCADE',
    },
    features: {
      target: 'Feature',
      type: 'many-to-many',
      joinTable: {
        name: 'business_features',
        joinColumn: {
          name: 'businessId',
          referencedColumnName: 'id',
        },
        inverseJoinColumn: {
          name: 'featureId',
          referencedColumnName: 'id',
        },
      },
    },
  },
  indices: [
    {
      name: 'businesses_name_ar_index',
      columns: ['nameAr'],
    },
    {
      name: 'businesses_name_en_index',
      columns: ['nameEn'],
    },
    {
      name: 'businesses_location_id_index',
      columns: ['locationId'],
    },
    {
      name: 'businesses_primary_category_id_index',
      columns: ['primaryCategoryId'],
    },
    {
      name: 'businesses_owner_user_id_index',
      columns: ['ownerUserId'],
    },
    {
      name: 'businesses_is_active_index',
      columns: ['isActive'],
    },
    {
      name: 'businesses_is_verified_index',
      columns: ['isVerified'],
    },
    {
      name: 'businesses_is_premium_index',
      columns: ['isPremium'],
    },
    {
      name: 'businesses_average_rating_index',
      columns: ['averageRating'],
    },
    {
      name: 'businesses_price_range_index',
      columns: ['priceRange'],
    },
    {
      name: 'businesses_premium_expires_at_index',
      columns: ['premiumExpiresAt'],
    },
    {
      name: 'businesses_payment_methods_gin_index',
      columns: ['paymentMethods'],
    },
  ],
});
