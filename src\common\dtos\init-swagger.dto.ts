import { NestExpressApplication } from '@nestjs/platform-express';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsNotEmpty,
  IsString,
  IsUrl,
  Matches,
  MinLength,
} from 'class-validator';

export class InitSwaggerDto {
  @IsNotEmpty()
  @ApiProperty({
    description: 'The NestJS application instance',
    type: 'object',
    additionalProperties: true,
  })
  app: NestExpressApplication;

  @IsString()
  @MinLength(3)
  @ApiProperty({
    description: 'The title of the API',
    example: 'My API',
    minLength: 3,
  })
  title: string;

  @IsString()
  @MinLength(10)
  @ApiProperty({
    description: 'A detailed description of the API',
    example: 'This API provides endpoints for managing users and their data',
    minLength: 10,
  })
  description: string;

  @IsString()
  @Matches(/^\d+\.\d+\.\d+$/, {
    message: 'Version must be in semantic versioning format (e.g., 1.0.0)',
  })
  @ApiProperty({
    description: 'The version of the API',
    example: '1.0.0',
    pattern: '^\\d+\\.\\d+\\.\\d+$',
  })
  version: string;

  @IsString()
  @Transform(({ value }) => value?.replace(/^\/+|\/+$/g, '') || 'api-docs')
  @Matches(/^[a-zA-Z0-9-_/]+$/, {
    message:
      'Path can only contain letters, numbers, hyphens, underscores, and forward slashes',
  })
  @ApiProperty({
    description:
      'The path where Swagger UI will be mounted (leading/trailing slashes will be trimmed)',
    example: 'api-docs',
    default: 'api-docs',
  })
  path: string;

  @IsUrl({
    require_tld: false,
    require_protocol: true,
  })
  @ApiProperty({
    description: 'The base URL where the API is hosted',
    example: 'http://localhost:3000',
  })
  hostURL: string;
}
