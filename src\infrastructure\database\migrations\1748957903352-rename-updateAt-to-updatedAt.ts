import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameUpdateAtToUpdatedAt1748957903352
  implements MigrationInterface
{
  name = 'RenameUpdateAtToUpdatedAt1748957903352';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" RENAME COLUMN "updateAt" TO "updatedAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" RENAME COLUMN "updateAt" TO "updatedAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permissions" RENAME COLUMN "updateAt" TO "updatedAt"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "permissions" RENAME COLUMN "updatedAt" TO "updateAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" RENAME COLUMN "updatedAt" TO "updateAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" RENAME COLUMN "updatedAt" TO "updateAt"`,
    );
  }
}
