import { DynamicModule } from '@nestjs/common';

import { ConfigModule } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';
import { ConfigurationValidationDto } from '../common/dtos';

export class DotEnvConfiguration {
  static init<T extends ConfigurationValidationDto>(
    configurationClass: new () => T,
    envPath: string,
  ): Promise<DynamicModule> {
    return ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: envPath.split(','),
      expandVariables: true,
      validate: (config: Record<string, any>) =>
        DotEnvConfiguration.validate<T>(configurationClass, config),
    });
  }

  static validate<ConfigurationType extends ConfigurationValidationDto>(
    classType: new () => ConfigurationType,
    config: Record<string, unknown>,
  ): ConfigurationType {
    const validateConfig = plainToInstance(classType, config, {
      enableImplicitConversion: true,
    });
    const errors = validateSync(validateConfig, {
      skipMissingProperties: false,
    });
    if (errors.length > 0) {
      throw new Error(errors.toString());
    }
    return validateConfig;
  }
}
