{"name": "@qareeb/api-client", "version": "1.0.0", "description": "TypeScript API client for Qareeb Plus Backend", "main": "api-client.js", "types": "api-client.d.ts", "files": ["api-client.ts", "api-client.js", "api-client.d.ts", "react-hooks.ts", "react-hooks.js", "react-hooks.d.ts", "examples.tsx", "README.md"], "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint *.ts *.tsx --fix", "type-check": "tsc --noEmit"}, "keywords": ["qareeb", "api", "client", "typescript", "react", "business-directory"], "author": "Qareeb Development Team", "license": "MIT", "dependencies": {"whatwg-fetch": "^3.6.2"}, "peerDependencies": {"react": ">=16.8.0", "@types/react": ">=16.8.0", "typescript": ">=4.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "@types/react": {"optional": true}}, "devDependencies": {"@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.0", "typescript": "^4.9.0"}, "repository": {"type": "git", "url": "https://github.com/qareeb/qareeb-plus-backend.git", "directory": "frontend-integration"}, "homepage": "https://github.com/qareeb/qareeb-plus-backend#readme", "bugs": {"url": "https://github.com/qareeb/qareeb-plus-backend/issues"}}