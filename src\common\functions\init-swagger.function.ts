import {
  DocumentBuilder,
  SwaggerCustomOptions,
  SwaggerModule,
} from '@nestjs/swagger';
import metadata from '../../metadata';
import { InitSwaggerDto } from '../dtos';

/**
 * Initializes Swagger/OpenAPI documentation for a NestJS application
 * @param initSwaggerDto Configuration options for Swagger initialization
 * @returns void
 */
export async function InitSwagger(
  initSwaggerDto: InitSwaggerDto,
): Promise<void> {
  try {
    // Normalize the path (remove leading/trailing slashes and ensure proper format)
    const normalizedPath = initSwaggerDto.path.replace(/^\/+|\/+$/g, '');

    // Configure Swagger document
    const config = new DocumentBuilder()
      .setTitle(initSwaggerDto.title)
      .setDescription(initSwaggerDto.description)
      .setVersion(initSwaggerDto.version)
      .setContact(
        'API Support',
        'https://your-domain.com/support',
        '<EMAIL>',
      )
      .setLicense('MIT', 'https://opensource.org/licenses/MIT')
      .setExternalDoc('API Documentation', 'https://your-domain.com/docs')
      .addServer(initSwaggerDto.hostURL, 'Production')
      .addServer(initSwaggerDto.hostURL, 'Local Development')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'Authorization',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'x-api-key',
          in: 'header',
          description: 'API Key for external services',
        },
        'ApiKey',
      )
      .addSecurityRequirements('JWT')
      .addSecurityRequirements('ApiKey')
      .build();

    // Temporarily disable metadata loading to avoid circular dependency
    // await SwaggerModule.loadPluginMetadata(metadata);
    // Create Swagger document
    const document = SwaggerModule.createDocument(initSwaggerDto.app, config, {
      deepScanRoutes: true,
      ignoreGlobalPrefix: false,
    });

    // Configure Swagger UI options
    const customOptions: SwaggerCustomOptions = {
      swaggerOptions: {
        persistAuthorization: true,
        docExpansion: 'none',
        filter: true,
        tagsSorter: 'alpha',
        operationsSorter: 'method',
        defaultModelsExpandDepth: 3,
        defaultModelExpandDepth: 3,
        displayRequestDuration: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        requestSnippetsEnabled: true,
        'syntaxHighlight.activate': true,
        'syntaxHighlight.theme': 'monokai',
        defaultModelRendering: 'model',
      },
      customSiteTitle: `${initSwaggerDto.title} API Documentation`,
      customfavIcon: '/favicon.ico',
      customJs: '/swagger-custom.js',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info { margin: 30px 0 }
        .swagger-ui .scheme-container { margin: 30px 0 }
        .swagger-ui .info .title { font-size: 32px }
        .swagger-ui .info .description { font-size: 16px; line-height: 1.5 }
        .swagger-ui .opblock .opblock-summary-description { font-size: 14px }
        .swagger-ui .opblock .opblock-summary { padding: 12px }
        .swagger-ui .opblock { margin: 0 0 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1) }
        .swagger-ui .opblock .opblock-section-header { padding: 12px; border-radius: 4px 4px 0 0 }
        .swagger-ui .opblock .opblock-section-header h4 { font-size: 16px }
        .swagger-ui .btn { border-radius: 4px }
        .swagger-ui select { border-radius: 4px }
        .swagger-ui input { border-radius: 4px }
        .swagger-ui textarea { border-radius: 4px; min-height: 180px; font-family: monaco, monospace; }
        .swagger-ui .parameter__name { font-size: 14px }
        .swagger-ui .parameter__type { font-size: 12px }
        .swagger-ui table tbody tr td { padding: 12px }
        .swagger-ui .responses-table .response-col_status { width: 120px }
        .swagger-ui .markdown p { font-size: 14px; line-height: 1.5 }
        .swagger-ui .servers > label select { min-width: 200px }

        /* Enhanced JSON editor styles */
        .swagger-ui .body-param__text {
          width: 100% !important;
          min-height: 240px !important;
          padding: 10px !important;
          font-size: 13px !important;
          font-family: monaco, monospace !important;
          resize: vertical !important;
          line-height: 1.4 !important;
          background-color: #f8f9fa !important;
          color: #333 !important;
          border: 1px solid #dee2e6 !important;
        }

        .swagger-ui .body-param__text:focus {
          outline: none !important;
          border-color: #80bdff !important;
          box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25) !important;
        }

        /* Button Styles */
        .swagger-ui .try-out__btn {
          background-color: #4a9eff !important;
          border-color: #4a9eff !important;
          color: white !important;
          padding: 8px 16px !important;
          border-radius: 4px !important;
          transition: all 0.2s !important;
        }

        .swagger-ui .try-out__btn:hover {
          background-color: #357abd !important;
          border-color: #357abd !important;
          transform: translateY(-1px) !important;
        }

        .swagger-ui .execute {
          background-color: #4CAF50 !important;
          border-color: #4CAF50 !important;
          color: white !important;
          padding: 8px 20px !important;
          border-radius: 4px !important;
          transition: all 0.2s !important;
        }

        .swagger-ui .execute:hover {
          background-color: #3d8b40 !important;
          border-color: #3d8b40 !important;
          transform: translateY(-1px) !important;
        }

        /* Dark Mode Styles */
        .dark-mode {
          background-color: #1a1a1a !important;
          color: #ffffff !important;
        }

        .dark-mode .swagger-ui {
          background-color: #1a1a1a !important;
          color: #ffffff !important;
        }

        .dark-mode .swagger-ui .body-param__text {
          background-color: #2d2d2d !important;
          color: #ffffff !important;
          border-color: #404040 !important;
        }

        .dark-mode .swagger-ui .opblock {
          background-color: #2d2d2d !important;
          border-color: #404040 !important;
        }

        /* Syntax highlighting styles */
        .string { color: #24b062; }
        .number { color: #d19a66; }
        .boolean { color: #c678dd; }
        .null { color: #808080; }
        .key { color: #4a9eff; }
      `,
      explorer: true,
      swaggerUrl: `/${normalizedPath}/swagger-json`,
      useGlobalPrefix: true,
    };

    // Setup Swagger endpoint
    SwaggerModule.setup(
      normalizedPath,
      initSwaggerDto.app,
      document,
      customOptions,
    );

    console.log(`✨ Swagger documentation is available at: /${normalizedPath}`);
  } catch (error) {
    console.error('Failed to initialize Swagger:', error);
    throw error;
  }
}
