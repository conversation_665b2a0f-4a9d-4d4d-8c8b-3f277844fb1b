/**
 * Qareeb API Integration Examples
 * 
 * This file contains practical examples of how to integrate the Qareeb API
 * with React applications, including authentication, business listing,
 * location services, and more.
 */

import React, { useState, useEffect } from 'react';
import {
  QareebApiProvider,
  useAuth,
  useBusinesses,
  useCategories,
  useLocations,
  useGeolocation,
} from './react-hooks';

// ============== APP SETUP ==============
/**
 * Main App component with API provider setup
 */
export const App: React.FC = () => {
  return (
    <QareebApiProvider baseUrl="http://localhost:3000/v1">
      <div className="app">
        <AuthenticationExample />
        <BusinessListingExample />
        <LocationBasedExample />
        <CategoryBrowserExample />
      </div>
    </QareebApiProvider>
  );
};

// ============== AUTHENTICATION EXAMPLES ==============
/**
 * Authentication component with login/register functionality
 */
export const AuthenticationExample: React.FC = () => {
  const { login, register, logout, user, isAuthenticated, loading, error } = useAuth();
  const [loginForm, setLoginForm] = useState({ loginId: '', password: '' });
  const [registerForm, setRegisterForm] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
  });

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await login(loginForm);
    if (result) {
      console.log('Login successful:', result);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await register(registerForm);
    if (result) {
      console.log('Registration successful:', result);
    }
  };

  if (loading) return <div>Loading...</div>;

  if (isAuthenticated && user) {
    return (
      <div className="user-profile">
        <h2>Welcome, {user.firstName} {user.lastName}!</h2>
        <p>Email: {user.email}</p>
        <p>Role: {user.role?.name}</p>
        <button onClick={logout}>Logout</button>
      </div>
    );
  }

  return (
    <div className="auth-forms">
      <div className="login-form">
        <h3>Login</h3>
        <form onSubmit={handleLogin}>
          <input
            type="email"
            placeholder="Email"
            value={loginForm.loginId}
            onChange={(e) => setLoginForm({ ...loginForm, loginId: e.target.value })}
            required
          />
          <input
            type="password"
            placeholder="Password"
            value={loginForm.password}
            onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
            required
          />
          <button type="submit">Login</button>
        </form>
      </div>

      <div className="register-form">
        <h3>Register</h3>
        <form onSubmit={handleRegister}>
          <input
            type="email"
            placeholder="Email"
            value={registerForm.email}
            onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
            required
          />
          <input
            type="password"
            placeholder="Password"
            value={registerForm.password}
            onChange={(e) => setRegisterForm({ ...registerForm, password: e.target.value })}
            required
          />
          <input
            type="text"
            placeholder="First Name"
            value={registerForm.firstName}
            onChange={(e) => setRegisterForm({ ...registerForm, firstName: e.target.value })}
            required
          />
          <input
            type="text"
            placeholder="Last Name"
            value={registerForm.lastName}
            onChange={(e) => setRegisterForm({ ...registerForm, lastName: e.target.value })}
          />
          <input
            type="tel"
            placeholder="Phone Number"
            value={registerForm.phoneNumber}
            onChange={(e) => setRegisterForm({ ...registerForm, phoneNumber: e.target.value })}
          />
          <button type="submit">Register</button>
        </form>
      </div>

      {error && <div className="error">{error}</div>}
    </div>
  );
};

// ============== BUSINESS LISTING EXAMPLES ==============
/**
 * Business listing with filtering and search
 */
export const BusinessListingExample: React.FC = () => {
  const [filters, setFilters] = useState({
    searchKey: '',
    priceRange: undefined as 'LOW' | 'MEDIUM' | 'HIGH' | undefined,
    isVerified: undefined as boolean | undefined,
    minRating: undefined as number | undefined,
  });

  const { businesses, total, loading, error, refetch } = useBusinesses(filters);

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
    refetch(newFilters);
  };

  if (loading) return <div>Loading businesses...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="business-listing">
      <h2>Business Directory</h2>
      
      {/* Filters */}
      <div className="filters">
        <input
          type="text"
          placeholder="Search businesses..."
          value={filters.searchKey}
          onChange={(e) => handleFilterChange({ ...filters, searchKey: e.target.value })}
        />
        
        <select
          value={filters.priceRange || ''}
          onChange={(e) => handleFilterChange({ 
            ...filters, 
            priceRange: e.target.value as 'LOW' | 'MEDIUM' | 'HIGH' | undefined 
          })}
        >
          <option value="">All Price Ranges</option>
          <option value="LOW">Low (₤)</option>
          <option value="MEDIUM">Medium (₤₤)</option>
          <option value="HIGH">High (₤₤₤)</option>
        </select>

        <label>
          <input
            type="checkbox"
            checked={filters.isVerified || false}
            onChange={(e) => handleFilterChange({ ...filters, isVerified: e.target.checked })}
          />
          Verified Only
        </label>

        <input
          type="number"
          placeholder="Min Rating"
          min="0"
          max="5"
          step="0.1"
          value={filters.minRating || ''}
          onChange={(e) => handleFilterChange({ 
            ...filters, 
            minRating: e.target.value ? parseFloat(e.target.value) : undefined 
          })}
        />
      </div>

      {/* Results */}
      <div className="results-summary">
        Found {total} businesses
      </div>

      <div className="business-grid">
        {businesses.map((business) => (
          <BusinessCard key={business.id} business={business} />
        ))}
      </div>
    </div>
  );
};

/**
 * Individual business card component
 */
const BusinessCard: React.FC<{ business: any }> = ({ business }) => {
  return (
    <div className="business-card">
      {business.logoUrl && (
        <img src={business.logoUrl} alt={business.nameEn} className="business-logo" />
      )}
      <h3>{business.nameEn}</h3>
      <p className="business-description">{business.shortDescriptionEn}</p>
      <div className="business-meta">
        <span className="rating">⭐ {business.averageRating?.toFixed(1) || 'No rating'}</span>
        <span className="price-range">{business.priceRange}</span>
        {business.isVerified && <span className="verified">✓ Verified</span>}
        {business.isPremium && <span className="premium">👑 Premium</span>}
      </div>
      <div className="business-contact">
        {business.phoneNumber && <span>📞 {business.phoneNumber}</span>}
        {business.whatsAppNumber && <span>💬 WhatsApp</span>}
      </div>
    </div>
  );
};

// ============== LOCATION-BASED EXAMPLES ==============
/**
 * Location-based business finder
 */
export const LocationBasedExample: React.FC = () => {
  const { latitude, longitude, loading: geoLoading, error: geoError } = useGeolocation();
  const { businesses: nearbyBusinesses, getNearby } = useBusinesses();
  const [radius, setRadius] = useState(5); // km
  const [nearbyResults, setNearbyResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const findNearbyBusinesses = async () => {
    if (!latitude || !longitude) return;
    
    setLoading(true);
    try {
      const results = await getNearby(latitude, longitude, radius);
      setNearbyResults(results);
    } catch (error) {
      console.error('Failed to find nearby businesses:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (latitude && longitude) {
      findNearbyBusinesses();
    }
  }, [latitude, longitude, radius]);

  if (geoLoading) return <div>Getting your location...</div>;
  if (geoError) return <div>Location error: {geoError}</div>;

  return (
    <div className="location-based-finder">
      <h2>Nearby Businesses</h2>
      
      {latitude && longitude && (
        <div className="location-info">
          <p>Your location: {latitude.toFixed(4)}, {longitude.toFixed(4)}</p>
          
          <div className="radius-selector">
            <label>
              Search radius:
              <select value={radius} onChange={(e) => setRadius(parseInt(e.target.value))}>
                <option value={1}>1 km</option>
                <option value={2}>2 km</option>
                <option value={5}>5 km</option>
                <option value={10}>10 km</option>
                <option value={20}>20 km</option>
              </select>
            </label>
          </div>
        </div>
      )}

      {loading && <div>Finding nearby businesses...</div>}
      
      <div className="nearby-results">
        {nearbyResults.map((business) => (
          <BusinessCard key={business.id} business={business} />
        ))}
      </div>
    </div>
  );
};

// ============== CATEGORY BROWSER EXAMPLE ==============
/**
 * Hierarchical category browser
 */
export const CategoryBrowserExample: React.FC = () => {
  const { hierarchy, loading, error } = useCategories();
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const { businesses: categoryBusinesses, refetch } = useBusinesses();

  const handleCategorySelect = async (category: any) => {
    setSelectedCategory(category);
    await refetch({ primaryCategoryId: category.id });
  };

  if (loading) return <div>Loading categories...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="category-browser">
      <h2>Browse by Category</h2>
      
      <div className="category-hierarchy">
        {hierarchy.map((category) => (
          <CategoryNode
            key={category.id}
            category={category}
            onSelect={handleCategorySelect}
            selectedId={selectedCategory?.id}
          />
        ))}
      </div>

      {selectedCategory && (
        <div className="category-businesses">
          <h3>Businesses in "{selectedCategory.nameEn}"</h3>
          <div className="business-grid">
            {categoryBusinesses.map((business) => (
              <BusinessCard key={business.id} business={business} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Recursive category tree node
 */
const CategoryNode: React.FC<{
  category: any;
  onSelect: (category: any) => void;
  selectedId?: number;
}> = ({ category, onSelect, selectedId }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="category-node">
      <div 
        className={`category-item ${selectedId === category.id ? 'selected' : ''}`}
        onClick={() => onSelect(category)}
        style={{ paddingLeft: category.level * 20 }}
      >
        {category.children && category.children.length > 0 && (
          <button
            className="expand-button"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded(!expanded);
            }}
          >
            {expanded ? '−' : '+'}
          </button>
        )}
        
        {category.icon && <img src={category.icon} alt="" className="category-icon" />}
        <span className="category-name">{category.nameEn}</span>
        <span className="business-count">({category.numberOfBusinesses})</span>
      </div>

      {expanded && category.children && (
        <div className="category-children">
          {category.children.map((child: any) => (
            <CategoryNode
              key={child.id}
              category={child}
              onSelect={onSelect}
              selectedId={selectedId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// ============== ADVANCED SEARCH EXAMPLE ==============
/**
 * Advanced search with multiple filters
 */
export const AdvancedSearchExample: React.FC = () => {
  const [searchParams, setSearchParams] = useState({
    searchKey: '',
    cityEn: '',
    priceRange: undefined as 'LOW' | 'MEDIUM' | 'HIGH' | undefined,
    isVerified: false,
    isOpen24x7: false,
    minRating: 0,
  });

  const { businesses, total, loading, refetch } = useBusinesses(searchParams);
  const { categories } = useCategories();

  const handleSearch = () => {
    refetch(searchParams);
  };

  return (
    <div className="advanced-search">
      <h2>Advanced Business Search</h2>
      
      <div className="search-form">
        <div className="form-row">
          <input
            type="text"
            placeholder="Search businesses..."
            value={searchParams.searchKey}
            onChange={(e) => setSearchParams({ ...searchParams, searchKey: e.target.value })}
          />
          <button onClick={handleSearch}>Search</button>
        </div>

        <div className="form-row">
          <input
            type="text"
            placeholder="City"
            value={searchParams.cityEn}
            onChange={(e) => setSearchParams({ ...searchParams, cityEn: e.target.value })}
          />
          
          <select
            value={searchParams.priceRange || ''}
            onChange={(e) => setSearchParams({ 
              ...searchParams, 
              priceRange: e.target.value as 'LOW' | 'MEDIUM' | 'HIGH' | undefined 
            })}
          >
            <option value="">Any Price Range</option>
            <option value="LOW">Low (₤)</option>
            <option value="MEDIUM">Medium (₤₤)</option>
            <option value="HIGH">High (₤₤₤)</option>
          </select>
        </div>

        <div className="form-row">
          <label>
            <input
              type="checkbox"
              checked={searchParams.isVerified}
              onChange={(e) => setSearchParams({ ...searchParams, isVerified: e.target.checked })}
            />
            Verified businesses only
          </label>

          <label>
            <input
              type="checkbox"
              checked={searchParams.isOpen24x7}
              onChange={(e) => setSearchParams({ ...searchParams, isOpen24x7: e.target.checked })}
            />
            Open 24/7
          </label>
        </div>

        <div className="form-row">
          <label>
            Minimum rating:
            <input
              type="range"
              min="0"
              max="5"
              step="0.5"
              value={searchParams.minRating}
              onChange={(e) => setSearchParams({ ...searchParams, minRating: parseFloat(e.target.value) })}
            />
            <span>{searchParams.minRating} stars</span>
          </label>
        </div>
      </div>

      {loading && <div>Searching...</div>}
      
      <div className="search-results">
        <p>Found {total} businesses</p>
        <div className="business-grid">
          {businesses.map((business) => (
            <BusinessCard key={business.id} business={business} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default {
  App,
  AuthenticationExample,
  BusinessListingExample,
  LocationBasedExample,
  CategoryBrowserExample,
  AdvancedSearchExample,
};