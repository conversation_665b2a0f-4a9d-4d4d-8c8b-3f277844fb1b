import { IGenericRepository } from '@app/common';
import { Business } from '../../entities/business.entity';
import { PaymentMethod, PriceRange } from '../../enums';
import { BusinessSchema } from '../../schemes/business.schema';

export interface IBusinessRepository
  extends IGenericRepository<Business, typeof BusinessSchema> {
  findByLocationId(locationId: number): Promise<Business[]>;
  findByPrimaryCategoryId(primaryCategoryId: number): Promise<Business[]>;
  findByOwnerUserId(ownerUserId: number): Promise<Business[]>;
  findByFeatureId(featureId: number): Promise<Business[]>;
  findByPriceRange(priceRange: PriceRange): Promise<Business[]>;
  findByPaymentMethod(paymentMethod: PaymentMethod): Promise<Business[]>;
  findByRatingRange(minRating: number, maxRating?: number): Promise<Business[]>;
  findByCity(cityEn?: string, cityAr?: string): Promise<Business[]>;
  findNearby(
    latitude: number,
    longitude: number,
    radiusKm?: number,
  ): Promise<Business[]>;
  findActiveBusinesses(): Promise<Business[]>;
  findVerifiedBusinesses(): Promise<Business[]>;
  findPremiumBusinesses(): Promise<Business[]>;
  updateViewsCount(businessId: number): Promise<void>;
  updateRating(
    businessId: number,
    averageRating: number,
    totalReviews: number,
  ): Promise<void>;
  findExpiredPremiumBusinesses(): Promise<Business[]>;
  createBusinessWithFeatures(
    business: Business,
    featureIds?: number[],
  ): Promise<Business>;
  updateBusinessWithFeatures(
    business: Business,
    featureIds?: number[],
  ): Promise<Business>;
  incrementCategoryBusinessCount(categoryId: number): Promise<void>;
  decrementCategoryBusinessCount(categoryId: number): Promise<void>;
  updateCategoryBusinessCount(categoryId: number, count: number): Promise<void>;
}

export const IBusinessRepository = Symbol('IBusinessRepository');
