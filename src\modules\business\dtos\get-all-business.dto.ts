import { Transform } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { BooleanTransform, GetAllDto } from '@app/common';
import { PaymentMethod, PriceRange } from '../enums';

export class GetAllBusinessDto extends GetAllDto {
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  locationId?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  primaryCategoryId?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  ownerUserId?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  featureId?: number;

  @IsOptional()
  @IsEnum(PriceRange)
  priceRange?: PriceRange;

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @BooleanTransform()
  isActive?: boolean;

  @IsOptional()
  @BooleanTransform()
  isVerified?: boolean;

  @IsOptional()
  @BooleanTransform()
  isPremium?: boolean;

  @IsOptional()
  @BooleanTransform()
  isOpen24x7?: boolean;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  minRating?: number;

  @IsOptional()
  @IsString()
  cityEn?: string;

  @IsOptional()
  @IsString()
  cityAr?: string;

  @IsOptional()
  @BooleanTransform()
  includeLocation?: boolean;

  @IsOptional()
  @BooleanTransform()
  includeCategory?: boolean;

  @IsOptional()
  @BooleanTransform()
  includeFeatures?: boolean;

  @IsOptional()
  @BooleanTransform()
  includeOwner?: boolean;
}
