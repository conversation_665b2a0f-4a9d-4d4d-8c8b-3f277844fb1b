import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GenericRepository } from '@app/common';
import { Repository } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { IPermissionRepository } from '../interfaces/permission/permission-repository.interface';
import { PermissionSchema } from '../schemes/permission.schema';

@Injectable()
export class PermissionRepository
  extends GenericRepository<Permission, typeof PermissionSchema>
  implements IPermissionRepository
{
  constructor(
    @InjectRepository(PermissionSchema)
    private readonly permissionRepository: Repository<Permission>,
  ) {
    super(permissionRepository);
  }
}
