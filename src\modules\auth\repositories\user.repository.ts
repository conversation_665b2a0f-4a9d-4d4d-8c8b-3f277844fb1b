import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GenericRepository } from '@app/common';
import { FindOptionsWhere, Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { IUserRepository } from '../interfaces/user/user-repository.interface';
import { UserSchema } from '../schemes/user.schema';

@Injectable()
export class UserRepository
  extends GenericRepository<User, typeof UserSchema>
  implements IUserRepository
{
  constructor(
    @InjectRepository(UserSchema)
    private readonly userRepository: Repository<User>,
  ) {
    super(userRepository);
  }

  async findUserByEmailOrPhone(emailOrPhone: string, roleId?: number) {
    const where: FindOptionsWhere<User>[] = [
      { email: emailOrPhone },
      { phoneNumber: emailOrPhone },
    ];
    if (roleId) {
      where[0].roleId = roleId;
      where[1].roleId = roleId;
    }
    return this.userRepository.findOne({
      where,
    });
  }

  async findManyByEmailOrPhone(emailOrPhone: string) {
    return this.userRepository.find({
      where: [{ email: emailOrPhone }, { phoneNumber: emailOrPhone }],
      relations: ['role'],
    });
  }

  async findUserByEmailOrPhoneAndRoleId(emailOrPhone: string, roleId: number) {
    return this.userRepository.findOne({
      where: [
        { email: emailOrPhone, roleId },
        { phoneNumber: emailOrPhone, roleId },
      ],
      relations: ['role'],
    });
  }

  async findOneWithDetailsById(id: number) {
    return this.userRepository.findOne({
      where: { id },
      relations: ['role', 'role.permissions'],
    });
  }
}
