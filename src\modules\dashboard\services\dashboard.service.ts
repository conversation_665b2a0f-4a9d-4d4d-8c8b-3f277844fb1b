import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { DashboardStatisticsDto } from '../dtos/dashboard-statistics.dto';
import { IDashboardService } from '../interfaces/dashboard-service.interface';

@Injectable()
export class DashboardService implements IDashboardService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
  ) {}

  async getStatistics(): Promise<DashboardStatisticsDto> {
    try {
      // Method 1: Raw SQL Query (Most Efficient - Single Database Round Trip)
      const query = `
        SELECT
          (SELECT COUNT(*) FROM categories WHERE "deletedAt" IS NULL) as categories_count,
          (SELECT COUNT(*) FROM features WHERE "deletedAt" IS NULL) as features_count,
          (SELECT COUNT(*) FROM locations WHERE "deletedAt" IS NULL) as locations_count,
          (SELECT COUNT(*) FROM businesses WHERE "deletedAt" IS NULL) as businesses_count,
          (SELECT COUNT(*) FROM businesses WHERE "deletedAt" IS NULL AND "isActive" = true) as active_businesses_count,
          (SELECT COUNT(*) FROM businesses WHERE "deletedAt" IS NULL AND "isVerified" = true) as verified_businesses_count,
          (SELECT COUNT(*) FROM businesses WHERE "deletedAt" IS NULL AND "isPremium" = true) as premium_businesses_count
      `;

      const result = await this.entityManager.query(query);
      const counts = result[0];

      // Map database results to DTO
      const statistics: DashboardStatisticsDto = {
        categoriesCount: parseInt(counts.categories_count) || 0,
        featuresCount: parseInt(counts.features_count) || 0,
        locationsCount: parseInt(counts.locations_count) || 0,
        businessesCount: parseInt(counts.businesses_count) || 0,
        activeBusinessesCount: parseInt(counts.active_businesses_count) || 0,
        verifiedBusinessesCount:
          parseInt(counts.verified_businesses_count) || 0,
        premiumBusinessesCount: parseInt(counts.premium_businesses_count) || 0,
      };

      return statistics;
    } catch (error) {
      console.error('Error getting dashboard statistics:', error);

      // Return default values in case of error
      return {
        categoriesCount: 0,
        featuresCount: 0,
        locationsCount: 0,
        businessesCount: 0,
        activeBusinessesCount: 0,
        verifiedBusinessesCount: 0,
        premiumBusinessesCount: 0,
      };
    }
  }
}
