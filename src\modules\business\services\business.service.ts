import { GenericService, IGetAllResponseInterface } from '@app/common';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Feature } from 'src/modules/feature';
import { FindManyOptions, In } from 'typeorm';
import { IFeatureService } from '../../feature/interfaces/feature-service/feature-service.interface';
import { Location } from '../../location/entities/location.entity';
import { ILocationService } from '../../location/interfaces/location-service/location-service.interface';
import { CreateBusinessDto } from '../dtos/create-business.dto';
import { GetAllBusinessDto } from '../dtos/get-all-business.dto';
import { UpdateBusinessDto } from '../dtos/update-business.dto';
import { Business } from '../entities/business.entity';
import { PaymentMethod, PriceRange } from '../enums';
import { IBusinessRepository } from '../interfaces/business-repository/business-repository.interface';
import { IBusinessService } from '../interfaces/business-service/business-service.interface';
import { BusinessSchema } from '../schemes/business.schema';

@Injectable()
export class BusinessService
  extends GenericService<
    Business,
    typeof BusinessSchema,
    GetAllBusinessDto,
    CreateBusinessDto,
    UpdateBusinessDto
  >
  implements IBusinessService
{
  constructor(
    @Inject(IBusinessRepository)
    private readonly businessRepository: IBusinessRepository,
    @Inject(IFeatureService)
    private readonly featureService: IFeatureService,
    @Inject(ILocationService)
    private readonly locationService: ILocationService,
  ) {
    super(businessRepository);
  }

  async getAll(
    getAllDto: GetAllBusinessDto,
    initialQuery?: FindManyOptions<Business>,
  ): Promise<IGetAllResponseInterface<Business>> {
    initialQuery = initialQuery || {};
    initialQuery['where'] = initialQuery['where'] || {};

    if (getAllDto.searchKey) {
      getAllDto.searchOnJson =
        'nameEn,nameAr,shortDescriptionEn,shortDescriptionAr,fullDescriptionEn,fullDescriptionAr';
    }

    // Handle business-specific filters
    if (getAllDto.locationId !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        locationId: getAllDto.locationId,
      };
    }

    if (getAllDto.primaryCategoryId !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        primaryCategoryId: getAllDto.primaryCategoryId,
      };
    }

    if (getAllDto.ownerUserId !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        ownerUserId: getAllDto.ownerUserId,
      };
    }

    if (getAllDto.priceRange !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        priceRange: getAllDto.priceRange,
      };
    }

    if (getAllDto.isActive !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        isActive: getAllDto.isActive,
      };
    } else {
      initialQuery['where'] = {
        ...initialQuery['where'],
        isActive: true,
      };
    }

    if (getAllDto.isVerified !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        isVerified: getAllDto.isVerified,
      };
    }

    if (getAllDto.isPremium !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        isPremium: getAllDto.isPremium,
      };
    }

    if (getAllDto.isOpen24x7 !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        isOpen24x7: getAllDto.isOpen24x7,
      };
    }

    // Handle feature filtering - use repository method instead of direct query
    if (getAllDto.featureId !== undefined) {
      // For feature filtering, we need to use the repository's findByFeatureId method
      // which properly handles the many-to-many relationship
      const businessesWithFeature =
        await this.businessRepository.findByFeatureId(getAllDto.featureId);
      const businessIds = businessesWithFeature.map((b) => b.id);

      if (businessIds.length === 0) {
        // No businesses found with this feature, return empty result
        return {
          items: [],
          count: 0,
        };
      }

      // Add the business IDs to the where clause
      initialQuery['where'] = {
        ...initialQuery['where'],
        id: businessIds.length === 1 ? businessIds[0] : In(businessIds),
      };
    }

    // Handle relations
    if (
      getAllDto.includeLocation ||
      getAllDto.includeCategory ||
      getAllDto.includeFeatures ||
      getAllDto.includeOwner
    ) {
      initialQuery['relations'] = initialQuery['relations'] || {};

      if (getAllDto.includeLocation) {
        initialQuery['relations']['location'] = true;
      }

      if (getAllDto.includeCategory) {
        initialQuery['relations']['primaryCategory'] = true;
      }

      if (getAllDto.includeFeatures) {
        initialQuery['relations']['features'] = true;
      }

      if (getAllDto.includeOwner) {
        initialQuery['relations']['owner'] = true;
      }
    }

    return super.getAll(getAllDto, initialQuery);
  }

  async create(
    entityType: new () => Business,
    dto: CreateBusinessDto,
  ): Promise<Business> {
    const { featureIds, location, locationId, ...businessData } = dto;

    // Handle location creation or validation
    let finalLocationId: number;
    if (location) {
      // Create new location if location object is provided
      const createdLocation = await this.locationService.create(
        Location,
        location,
      );
      finalLocationId = createdLocation.id;
    } else if (locationId) {
      // Validate existing location
      await this.locationService.findOneById(locationId);
      finalLocationId = locationId;
    } else {
      throw new Error('Either location or locationId must be provided');
    }

    const business = plainToInstance(entityType, {
      ...businessData,
      locationId: finalLocationId,
    });

    // Validate features exist if provided
    if (featureIds && featureIds.length > 0) {
      for (const featureId of featureIds) {
        await this.featureService.findOneById(featureId);
      }
    }

    // Initialize metrics
    business.averageRating = 0;
    business.totalReviewsCount = 0;
    business.totalViewsCount = 0;
    business.lastMonthViews = 0;
    business.isActive = true;
    business.isVerified = false;
    business.isPremium = false;

    // Create business with features
    const createdBusiness =
      await this.businessRepository.createBusinessWithFeatures(
        business,
        featureIds,
      );

    // Increment the category business count
    await this.businessRepository.incrementCategoryBusinessCount(
      business.primaryCategoryId,
    );

    return createdBusiness;
  }

  async update(
    entityType: new () => Business,
    dto: UpdateBusinessDto & { id: number },
  ): Promise<Business> {
    const { featureIds, location, locationId, ...businessData } = dto;
    const existingBusiness = await this.findOneById(dto.id);

    // Check if the category is being changed
    const oldCategoryId = existingBusiness.primaryCategoryId;
    const newCategoryId = businessData.primaryCategoryId;
    const categoryChanged = newCategoryId && newCategoryId !== oldCategoryId;

    // Handle location update if provided
    let finalLocationId: number = existingBusiness.locationId;
    if (location) {
      // Create new location if location object is provided
      const createdLocation = await this.locationService.create(
        Location,
        location,
      );
      finalLocationId = createdLocation.id;
    } else if (locationId) {
      // Validate existing location
      await this.locationService.findOneById(locationId);
      finalLocationId = locationId;
    }

    const updatedBusiness = plainToInstance(entityType, {
      ...existingBusiness,
      ...businessData,
      locationId: finalLocationId,
    });

    updatedBusiness.id = dto.id;

    const features: Feature[] = [];
    // Validate features exist if provided
    if (featureIds && featureIds.length > 0) {
      for (const featureId of featureIds) {
        const feature = await this.featureService.findOneById(featureId);
        features.push(feature);
      }
    }

    // Handle category count updates if category changed
    if (categoryChanged) {
      // Decrement the count in the old category
      await this.businessRepository.decrementCategoryBusinessCount(oldCategoryId);
      
      // Increment the count in the new category
      await this.businessRepository.incrementCategoryBusinessCount(newCategoryId);
    }

    // Update business with features
    const result = await this.businessRepository.updateBusinessWithFeatures(
      updatedBusiness,
      featureIds,
    );

    return this.findOneById(result.id);
  }

  async findByLocationId(locationId: number): Promise<Business[]> {
    return this.businessRepository.findByLocationId(locationId);
  }

  async findByPrimaryCategoryId(
    primaryCategoryId: number,
  ): Promise<Business[]> {
    return this.businessRepository.findByPrimaryCategoryId(primaryCategoryId);
  }

  async findByOwnerUserId(ownerUserId: number): Promise<Business[]> {
    return this.businessRepository.findByOwnerUserId(ownerUserId);
  }

  async findByFeatureId(featureId: number): Promise<Business[]> {
    return this.businessRepository.findByFeatureId(featureId);
  }

  async findByPriceRange(priceRange: PriceRange): Promise<Business[]> {
    return this.businessRepository.findByPriceRange(priceRange);
  }

  async findByPaymentMethod(paymentMethod: PaymentMethod): Promise<Business[]> {
    return this.businessRepository.findByPaymentMethod(paymentMethod);
  }

  async findByRatingRange(
    minRating: number,
    maxRating?: number,
  ): Promise<Business[]> {
    return this.businessRepository.findByRatingRange(minRating, maxRating);
  }

  async findByCity(cityEn?: string, cityAr?: string): Promise<Business[]> {
    return this.businessRepository.findByCity(cityEn, cityAr);
  }

  async findNearby(
    latitude: number,
    longitude: number,
    radiusKm?: number,
  ): Promise<Business[]> {
    return this.businessRepository.findNearby(latitude, longitude, radiusKm);
  }

  async findActiveBusinesses(): Promise<Business[]> {
    return this.businessRepository.findActiveBusinesses();
  }

  async findVerifiedBusinesses(): Promise<Business[]> {
    return this.businessRepository.findVerifiedBusinesses();
  }

  async findPremiumBusinesses(): Promise<Business[]> {
    return this.businessRepository.findPremiumBusinesses();
  }

  async incrementViews(businessId: number): Promise<void> {
    await this.businessRepository.updateViewsCount(businessId);
  }

  async updateBusinessRating(
    businessId: number,
    averageRating: number,
    totalReviews: number,
  ): Promise<void> {
    await this.businessRepository.updateRating(
      businessId,
      averageRating,
      totalReviews,
    );
  }

  async activateBusiness(businessId: number): Promise<Business> {
    const business = await this.findOneById(businessId);
    business.isActive = true;
    await this.businessRepository.updateOne(business);
    return business;
  }

  async deactivateBusiness(businessId: number): Promise<Business> {
    const business = await this.findOneById(businessId);
    business.isActive = false;
    await this.businessRepository.updateOne(business);
    return business;
  }

  async verifyBusiness(businessId: number): Promise<Business> {
    const business = await this.findOneById(businessId);
    business.isVerified = true;
    await this.businessRepository.updateOne(business);
    return business;
  }

  async unverifyBusiness(businessId: number): Promise<Business> {
    const business = await this.findOneById(businessId);
    business.isVerified = false;
    await this.businessRepository.updateOne(business);
    return business;
  }

  async upgradeToPremium(
    businessId: number,
    expiresAt: Date,
  ): Promise<Business> {
    const business = await this.findOneById(businessId);
    business.isPremium = true;
    business.premiumExpiresAt = expiresAt;
    await this.businessRepository.updateOne(business);
    return business;
  }

  async downgradePremium(businessId: number): Promise<Business> {
    const business = await this.findOneById(businessId);
    business.isPremium = false;
    business.premiumExpiresAt = undefined;
    await this.businessRepository.updateOne(business);
    return business;
  }

  async findExpiredPremiumBusinesses(): Promise<Business[]> {
    return this.businessRepository.findExpiredPremiumBusinesses();
  }

  async validateBusinessOwnership(
    businessId: number,
    userId: number,
  ): Promise<boolean> {
    const business = await this.businessRepository.findOneBy({
      where: { id: businessId },
    });

    if (!business) {
      throw new NotFoundException('Business not found');
    }

    return business.ownerUserId === userId;
  }

  async updateCategoryBusinessCounts(): Promise<void> {
    // This would implement logic to update category business counts
    // You might want to use a scheduled job for this
    console.log('Updating category business counts...');
  }

  async findOneById(id: number): Promise<Business> {
    const business = await this.businessRepository.findOneBy({
      where: { id },
      relations: ['location', 'features'],
    });
    if (!business) {
      throw new NotFoundException('Business not found');
    }
    return business;
  }
}
