import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { LocationController } from './controllers/location.controller';
import { ILocationRepository } from './interfaces/location-repository/location-repository.interface';
import { ILocationService } from './interfaces/location-service/location-service.interface';
import { LocationRepository } from './repositories/location.repository';
import { LocationSchema } from './schemes/location.schema';
import { LocationService } from './services/location.service';

@Module({
  imports: [TypeOrmModule.forFeature([LocationSchema]), AuthModule],
  controllers: [LocationController],
  providers: [
    {
      provide: ILocationRepository,
      useClass: LocationRepository,
    },
    {
      provide: ILocationService,
      useClass: LocationService,
    },
  ],
  exports: [ILocationService, ILocationRepository],
})
export class LocationModule {}
