import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { DataSource } from 'typeorm';
import { AppModule } from '../../src/app.module';
import { Role } from '../../src/modules/auth/entities/role.entity';
import { User } from '../../src/modules/auth/entities/user.entity';
import { UserStatus } from '../../src/modules/auth/enums/user-status.enum';
import { AuthHelper } from '../helpers/auth.helper';
import { ErrorHelper } from '../helpers/error.helper';
import { getValidationPipe } from '@app/common';

describe('User Controller (e2e)', () => {
  let app: INestApplication<App>;
  let dataSource: DataSource;
  let authToken: string;
  let testUser: User;
  let testRole: Role;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(getValidationPipe());
    await app.init();

    dataSource = app.get(DataSource);

    // Login once before all tests
    const tokens = await AuthHelper.login(app);
    authToken = tokens.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up test data with E2E_TESTING prefix
    await dataSource.query(`
      DELETE FROM users
      WHERE "firstName" LIKE 'E2E_TESTING%'
    `);

    await dataSource.query(`
      DELETE FROM roles
      WHERE name LIKE 'E2E_TESTING%'
    `);

    // Create test role
    const roleRepo = dataSource.getRepository(Role);
    testRole = await roleRepo.save({
      name: 'E2E_TESTING Test Role',
    });
  });

  describe('/users (POST)', () => {
    it('should create a new user', async () => {
      const createUserDto = {
        firstName: 'E2E_TESTING John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.firstName).toBe(createUserDto.firstName);
      expect(response.body.lastName).toBe(createUserDto.lastName);
      expect(response.body.email).toBe(createUserDto.email);
      expect(response.body.status).toBe(createUserDto.status);
      expect(response.body.roleId).toBe(createUserDto.roleId);
      expect(response.body).toHaveProperty('createdAt');
      expect(response.body).toHaveProperty('updatedAt');
      expect(response.body).not.toHaveProperty('password');
      expect(response.body).not.toHaveProperty('apiKey');
    });

    it('should fail when creating user without authentication', async () => {
      const createUserDto = {
        firstName: 'E2E_TESTING John',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Create user without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail when creating user without firstName', async () => {
      const createUserDto = {
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create user without firstName',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['firstName']);
    });

    it('should fail when creating user without password', async () => {
      const createUserDto = {
        firstName: 'E2E_TESTING John',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create user without password',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['password']);
    });

    it('should fail when creating user with weak password', async () => {
      const createUserDto = {
        firstName: 'E2E_TESTING John',
        password: 'weak',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create user with weak password',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['password']);
    });

    it('should fail when creating user with invalid email', async () => {
      const createUserDto = {
        firstName: 'E2E_TESTING John',
        email: 'invalid-email',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create user with invalid email',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['email']);
    });

    it('should fail when creating user with invalid status', async () => {
      const createUserDto = {
        firstName: 'E2E_TESTING John',
        password: 'TestPassword123!',
        status: 'INVALID_STATUS',
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create user with invalid status',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['status']);
    });
  });

  describe('/users (GET)', () => {
    beforeEach(async () => {
      // Create test users
      const userRepo = dataSource.getRepository(User);
      await userRepo.save({
        firstName: 'E2E_TESTING Alice',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      });

      await userRepo.save({
        firstName: 'E2E_TESTING Bob',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        status: UserStatus.INACTIVE,
        roleId: testRole.id,
      });
    });

    it('should get all users with pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ offset: 0, limit: 10 })
        .expect(200);

      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('count');
      expect(response.body.items).toBeInstanceOf(Array);
      expect(response.body.items.length).toBeGreaterThanOrEqual(2);
      expect(response.body.count).toBeGreaterThanOrEqual(2);
    });

    it('should filter users by search term', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ searchKey: 'E2E_TESTING Alice' })
        .expect(200);

      expect(response.body.items.length).toBeGreaterThan(0);
      response.body.items.forEach((user: any) => {
        expect(user.firstName).toContain('E2E_TESTING Alice');
      });
    });

    it('should filter users by status', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ status: UserStatus.ACTIVE })
        .expect(200);

      expect(response.body.items.length).toBeGreaterThan(0);
      response.body.items.forEach((user: any) => {
        expect(user.status).toBe(UserStatus.ACTIVE);
      });
    });

    it('should filter users by roleId', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ roleId: testRole.id })
        .expect(200);

      expect(response.body.items.length).toBeGreaterThan(0);
      response.body.items.forEach((user: any) => {
        expect(user.roleId).toBe(testRole.id);
      });
    });

    it('should fail to get users without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get users without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/users/:id (GET)', () => {
    beforeEach(async () => {
      const userRepo = dataSource.getRepository(User);
      testUser = await userRepo.save({
        firstName: 'E2E_TESTING Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      });
    });

    it('should get a user by ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.id).toBe(testUser.id);
      expect(response.body.firstName).toBe(testUser.firstName);
      expect(response.body.lastName).toBe(testUser.lastName);
      expect(response.body.email).toBe(testUser.email);
      expect(response.body.status).toBe(testUser.status);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Get non-existent user',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to get user without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${testUser.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get user without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/users/:id (PUT)', () => {
    beforeEach(async () => {
      const userRepo = dataSource.getRepository(User);
      testUser = await userRepo.save({
        firstName: 'E2E_TESTING Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      });
    });

    it('should update a user', async () => {
      const updateUserDto = {
        id: testUser.id,
        firstName: 'E2E_TESTING Updated',
        lastName: 'User Updated',
        email: '<EMAIL>',
        password: 'UpdatedPassword123!',
        status: UserStatus.INACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .put(`/users/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateUserDto)
        .expect(200);

      expect(response.body.id).toBe(testUser.id);
      expect(response.body.firstName).toBe(updateUserDto.firstName);
      expect(response.body.lastName).toBe(updateUserDto.lastName);
      expect(response.body.email).toBe(updateUserDto.email);
      expect(response.body.status).toBe(updateUserDto.status);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should fail to update non-existent user', async () => {
      const updateUserDto = {
        id: 99999,
        firstName: 'E2E_TESTING Updated',
        password: 'UpdatedPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .put('/users/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateUserDto)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Update non-existent user',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to update user without authentication', async () => {
      const updateUserDto = {
        firstName: 'E2E_TESTING Updated',
        password: 'UpdatedPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .put(`/users/${testUser.id}`)
        .send(updateUserDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Update user without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail to update user with invalid data', async () => {
      const updateUserDto = {
        id: testUser.id,
        firstName: '',
        email: 'invalid-email',
        password: 'weak',
        status: 'INVALID_STATUS',
        roleId: testRole.id,
      };

      const response = await request(app.getHttpServer())
        .put(`/users/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateUserDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Update user with invalid data',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, [
        'firstName',
        'email',
        'password',
        'status',
      ]);
    });
  });

  describe('/users/:id (DELETE)', () => {
    beforeEach(async () => {
      const userRepo = dataSource.getRepository(User);
      testUser = await userRepo.save({
        firstName: 'E2E_TESTING Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        status: UserStatus.ACTIVE,
        roleId: testRole.id,
      });
    });

    it('should delete a user', async () => {
      await request(app.getHttpServer())
        .delete(`/users/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify user is deleted
      const userRepo = dataSource.getRepository(User);
      const deletedUser = await userRepo.findOne({
        where: { id: testUser.id },
      });
      expect(deletedUser).toBeNull();
    });

    it('should return 404 when deleting non-existent user', async () => {
      const response = await request(app.getHttpServer())
        .delete('/users/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Delete non-existent user',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to delete user without authentication', async () => {
      const response = await request(app.getHttpServer())
        .delete(`/users/${testUser.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Delete user without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/users/profile (GET)', () => {
    it('should get current user profile', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('firstName');
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('roleId');
      expect(response.body).not.toHaveProperty('password');
      expect(response.body).not.toHaveProperty('apiKey');
    });

    it('should fail to get profile without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/profile')
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get profile without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });
});
