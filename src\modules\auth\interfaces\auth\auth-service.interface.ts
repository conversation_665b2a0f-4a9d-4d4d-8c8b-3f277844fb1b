import { LoginDto } from '../../dtos/auth/login.dto';
import { RegisterDto } from '../../dtos/auth/register.dto';
import { AuthResponseDto } from '../../dtos/auth/auth-response.dto';
import { RefreshTokenDto } from '../../dtos/auth/refresh-token.dto';
import { User } from '../../entities/user.entity';

export interface IAuthService {
  register(dto: RegisterDto): Promise<AuthResponseDto>;

  login(dto: LoginDto): Promise<AuthResponseDto>;

  refreshToken(dto: RefreshTokenDto): Promise<AuthResponseDto>;

  generateTokens(user: User): Promise<AuthResponseDto>;
}
