import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMultilingualSupport1748959000000 implements MigrationInterface {
  name = 'AddMultilingualSupport1748959000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new multilingual columns to categories table
    await queryRunner.query(
      `ALTER TABLE "categories" ADD "nameEn" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD "nameAr" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD "descriptionEn" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD "descriptionAr" character varying`,
    );

    // Copy existing data from old columns to new English columns
    await queryRunner.query(
      `UPDATE "categories" SET "nameEn" = "name", "descriptionEn" = "description"`,
    );

    // Drop old columns
    await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "name"`);
    await queryRunner.query(
      `ALTER TABLE "categories" DROP COLUMN "description"`,
    );

    // Features table already has multilingual columns (nameEn, nameAr) from its creation migration
    // No changes needed for features table as it was created with the correct structure
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Features table rollback is handled by its own migration
    // No changes needed here for features table

    // Reverse the changes for categories table
    await queryRunner.query(
      `ALTER TABLE "categories" ADD "name" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" ADD "description" character varying`,
    );

    // Copy data back from English columns
    await queryRunner.query(
      `UPDATE "categories" SET "name" = "nameEn", "description" = "descriptionEn"`,
    );

    // Drop new columns
    await queryRunner.query(
      `ALTER TABLE "categories" DROP COLUMN "descriptionAr"`,
    );
    await queryRunner.query(
      `ALTER TABLE "categories" DROP COLUMN "descriptionEn"`,
    );
    await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "nameAr"`);
    await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "nameEn"`);
  }
}
