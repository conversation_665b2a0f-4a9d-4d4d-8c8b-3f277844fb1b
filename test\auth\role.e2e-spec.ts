import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { DataSource } from 'typeorm';
import { AppModule } from '../../src/app.module';
import { Permission } from '../../src/modules/auth/entities/permission.entity';
import { Role } from '../../src/modules/auth/entities/role.entity';
import { AuthHelper } from '../helpers/auth.helper';
import { ErrorHelper } from '../helpers/error.helper';
import { getValidationPipe } from '@app/common';

describe('Role Controller (e2e)', () => {
  let app: INestApplication<App>;
  let dataSource: DataSource;
  let authToken: string;
  let testRole: Role;
  let testPermission: Permission;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(getValidationPipe());
    await app.init();

    dataSource = app.get(DataSource);

    // Login once before all tests
    const tokens = await AuthHelper.login(app);
    authToken = tokens.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up test data with E2E_TESTING prefix
    await dataSource.query(`
      DELETE FROM roles
      WHERE name LIKE 'E2E_TESTING%'
    `);

    await dataSource.query(`
      DELETE FROM permissions
      WHERE action LIKE 'E2E_TESTING%'
    `);

    // Create test permission
    const permissionRepo = dataSource.getRepository(Permission);
    testPermission = await permissionRepo.save({
      action: 'E2E_TESTING test read',
      manuallyAdded: true,
    });
  });

  describe('/roles (POST)', () => {
    it('should create a new role with permissions', async () => {
      const createRoleDto = {
        name: 'E2E_TESTING Test Role',
        permissions: [testPermission.id],
      };

      const response = await request(app.getHttpServer())
        .post('/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRoleDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(createRoleDto.name);
      expect(response.body).toHaveProperty('createdAt');
      expect(response.body).toHaveProperty('updatedAt');
    });

    it('should fail when creating role without authentication', async () => {
      const createRoleDto = {
        name: 'E2E_TESTING Test Role',
        permissions: [testPermission.id],
      };

      const response = await request(app.getHttpServer())
        .post('/roles')
        .send(createRoleDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Create role without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail when creating role without name', async () => {
      const createRoleDto = {
        permissions: [testPermission.id],
      };

      const response = await request(app.getHttpServer())
        .post('/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRoleDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create role without name',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['name']);
    });

    it('should fail when creating role without permissions', async () => {
      const createRoleDto = {
        name: 'E2E_TESTING Test Role',
      };

      const response = await request(app.getHttpServer())
        .post('/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRoleDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Create role without permissions',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['permissions']);
    });

    it('should fail when creating role with invalid permission IDs', async () => {
      const createRoleDto = {
        name: 'E2E_TESTING Test Role',
        permissions: [99999],
      };

      const response = await request(app.getHttpServer())
        .post('/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRoleDto)
        .expect(404); // Based on test output, this returns 404, not 400

      const errorResponse = ErrorHelper.handleTestError(
        'Create role with invalid permission IDs',
        response,
        404,
        'Not Found',
      );
    });
  });

  describe('/roles (GET)', () => {
    beforeEach(async () => {
      // Create test roles
      const roleRepo = dataSource.getRepository(Role);
      testRole = await roleRepo.save({
        name: 'E2E_TESTING Test Role 1',
      });

      // Establish the relationship properly
      testRole.permissions = [testPermission];
      await roleRepo.save(testRole);

      await roleRepo.save({
        name: 'E2E_TESTING Test Role 2',
      });
    });

    it('should get all roles with pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ offset: 0, limit: 10 })
        .expect(200);

      console.log(response.body);
      // getAll returns {items: [], count: number}
      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('count');
      expect(response.body.items).toBeInstanceOf(Array);
      expect(response.body.items.length).toBeGreaterThanOrEqual(2);
      expect(response.body.count).toBeGreaterThanOrEqual(2);
    });

    it('should filter roles by search term', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ searchKey: 'E2E_TESTING Test Role 1' })
        .expect(200);

      // Ensure all returned items match the search criteria
      expect(response.body.items.length).toBeGreaterThan(0);
      response.body.items.forEach((role: any) => {
        expect(role.name).toContain('E2E_TESTING Test Role 1');
      });
    });

    it('should fail to get roles without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles')
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get roles without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/roles/:id (GET)', () => {
    beforeEach(async () => {
      const roleRepo = dataSource.getRepository(Role);
      testRole = await roleRepo.save({
        name: 'E2E_TESTING Test Role',
      });

      // Establish the relationship properly
      testRole.permissions = [testPermission];
      await roleRepo.save(testRole);
    });

    it('should get a role by ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.id).toBe(testRole.id);
      expect(response.body.name).toBe(testRole.name);
    });

    it('should return 404 for non-existent role', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Get non-existent role',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to get role without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get(`/roles/${testRole.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get role without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/roles/:id (PUT)', () => {
    beforeEach(async () => {
      const roleRepo = dataSource.getRepository(Role);
      testRole = await roleRepo.save({
        name: 'E2E_TESTING Test Role',
      });

      // Establish the relationship properly
      testRole.permissions = [testPermission];
      await roleRepo.save(testRole);
    });

    it('should update a role', async () => {
      const updateRoleDto = {
        id: testRole.id,
        name: 'E2E_TESTING Updated Role',
        permissions: [testPermission.id],
      };

      const response = await request(app.getHttpServer())
        .put(`/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateRoleDto)
        .expect(200);

      expect(response.body.id).toBe(testRole.id);
      expect(response.body.name).toBe(updateRoleDto.name);
    });

    it('should fail to update non-existent role', async () => {
      const updateRoleDto = {
        name: 'E2E_TESTING Updated Role',
        permissions: [],
      };

      const response = await request(app.getHttpServer())
        .put('/roles/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateRoleDto)
        .expect(400); // Based on test output, this returns 400, not 404

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Update non-existent role',
        response,
      );
      ErrorHelper.expectBadRequestError(errorResponse);
    });

    it('should fail to update role without authentication', async () => {
      const updateRoleDto = {
        name: 'E2E_TESTING Updated Role',
        permissions: [],
      };

      const response = await request(app.getHttpServer())
        .put(`/roles/${testRole.id}`)
        .send(updateRoleDto)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Update role without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should fail to update role with invalid data', async () => {
      const updateRoleDto = {
        name: '',
        permissions: [],
      };

      const response = await request(app.getHttpServer())
        .put(`/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateRoleDto)
        .expect(400);

      const errorResponse = ErrorHelper.displayAndValidateError(
        'Update role with invalid data',
        response,
      );
      ErrorHelper.expectValidationErrors(errorResponse, ['name', 'id']);
    });
  });

  describe('/roles/:id (DELETE)', () => {
    beforeEach(async () => {
      const roleRepo = dataSource.getRepository(Role);
      testRole = await roleRepo.save({
        name: 'E2E_TESTING Test Role',
      });

      // Establish the relationship properly
      testRole.permissions = [testPermission];
      await roleRepo.save(testRole);
    });

    it('should delete a role', async () => {
      await request(app.getHttpServer())
        .delete(`/roles/${testRole.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify role is deleted
      const roleRepo = dataSource.getRepository(Role);
      const deletedRole = await roleRepo.findOne({
        where: { id: testRole.id },
      });
      expect(deletedRole).toBeNull();
    });

    it('should return 404 when deleting non-existent role', async () => {
      const response = await request(app.getHttpServer())
        .delete('/roles/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Delete non-existent role',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail to delete role without authentication', async () => {
      const response = await request(app.getHttpServer())
        .delete(`/roles/${testRole.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Delete role without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/roles/:id/permissions (GET)', () => {
    beforeEach(async () => {
      const roleRepo = dataSource.getRepository(Role);
      testRole = await roleRepo.save({
        name: 'E2E_TESTING Test Role',
      });

      // Establish the relationship properly
      testRole.permissions = [testPermission];
      await roleRepo.save(testRole);
    });

    it('should get a role with its permissions', async () => {
      const response = await request(app.getHttpServer())
        .get(`/roles/${testRole.id}/permissions`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.id).toBe(testRole.id);
      expect(response.body.name).toBe(testRole.name);
      expect(response.body.permissions).toBeDefined();
      expect(response.body.permissions).toHaveLength(1);
      expect(response.body.permissions[0].id).toBe(testPermission.id);
    });

    it('should return 404 for non-existent role', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles/99999/permissions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Get permissions for non-existent role',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail without authentication', async () => {
      const response = await request(app.getHttpServer())
        .get(`/roles/${testRole.id}/permissions`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Get role permissions without authentication',
        response,
        401,
        'Unauthorized',
      );
    });
  });

  describe('/roles/:id/permissions (POST)', () => {
    let newPermission: Permission;

    beforeEach(async () => {
      const roleRepo = dataSource.getRepository(Role);
      testRole = await roleRepo.save({
        name: 'E2E_TESTING Test Role',
      });

      // Establish the relationship properly
      testRole.permissions = [testPermission];
      await roleRepo.save(testRole);

      // Create another permission to add
      const permissionRepo = dataSource.getRepository(Permission);
      newPermission = await permissionRepo.save({
        action: 'E2E_TESTING test create',
        manuallyAdded: true,
      });
    });

    it('should add a permission to a role', async () => {
      const response = await request(app.getHttpServer())
        .post(`/roles/${testRole.id}/permissions/${newPermission.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.body.id).toBe(testRole.id);
      expect(response.body.permissions).toHaveLength(2);
      const permissionIds = response.body.permissions.map(
        (p: Permission) => p.id,
      );
      expect(permissionIds).toContain(testPermission.id);
      expect(permissionIds).toContain(newPermission.id);
    });

    it('should return 404 for non-existent role', async () => {
      const response = await request(app.getHttpServer())
        .post(`/roles/99999/permissions/${newPermission.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Add permission to non-existent role',
        response,
        404,
        'Not Found',
      );
    });

    it('should fail without authentication', async () => {
      const response = await request(app.getHttpServer())
        .post(`/roles/${testRole.id}/permissions/${newPermission.id}`)
        .expect(401);

      const errorResponse = ErrorHelper.handleTestError(
        'Add permission without authentication',
        response,
        401,
        'Unauthorized',
      );
    });

    it('should return 404 for non-existent permission', async () => {
      const response = await request(app.getHttpServer())
        .post(`/roles/${testRole.id}/permissions/99999`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      const errorResponse = ErrorHelper.handleTestError(
        'Add non-existent permission to role',
        response,
        404,
        'Not Found',
      );
    });
  });
});
