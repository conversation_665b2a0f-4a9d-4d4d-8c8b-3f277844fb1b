import { ApiHideProperty, OmitType } from '@nestjs/swagger';
import { IsMongoId, IsOptional } from 'class-validator';
import { DefaultRole } from '@app/common';
import { User } from '../../entities/user.entity';

export class UpdateUserDto extends OmitType(User, ['role', 'apiKey'] as const) {
  @IsOptional()
  @IsMongoId()
  parentId?: string;

  @ApiHideProperty()
  @IsOptional()
  role?: DefaultRole;
}
