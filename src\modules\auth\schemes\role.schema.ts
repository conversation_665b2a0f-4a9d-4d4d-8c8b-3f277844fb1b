import { EntitySchema } from 'typeorm';
import { BaseSchemaProperties } from '../../../infrastructure/database/schemas/base.schema';
import { Permission } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';

export const RoleSchema = new EntitySchema<Role>({
  name: Role.name,
  target: Role,
  tableName: 'roles',
  columns: {
    ...BaseSchemaProperties,
    name: {
      type: String,
      nullable: false,
      unique: true,
    },
  },
  relations: {
    permissions: {
      target: Permission.name,
      type: 'many-to-many',
      joinTable: {
        name: 'roles_permissions_mapping',
        joinColumn: { name: 'roleId' },
        inverseJoinColumn: { name: 'permissionId' },
      },
    },
  },
});
