import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  Api<PERSON>asicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IGetAllResponseInterface } from '../../../common/interfaces';
import { Permissions } from '../../../common/decorators/permissions.decorator';
import { CreateRoleDto } from '../dtos/role/create-role.dto';
import { GetAllRoleDto } from '../dtos/role/get-all-role.dto';
import { UpdateRoleDto } from '../dtos/role/update-role.dto';
import { Role } from '../entities/role.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { IRoleService } from '../interfaces/role/role-service.interface';
import { IPermissionService } from '../interfaces/permission/permission-service.interface';

/**
 * Controller for managing role entities
 */
@Controller('roles')
@ApiTags('Roles')
@ApiBearerAuth('JWT')
@ApiBasicAuth('ApiKey')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class RoleController {
  constructor(
    @Inject(IRoleService) private readonly roleService: IRoleService,
    @Inject(IPermissionService)
    private readonly permissionService: IPermissionService,
  ) {}

  /**
   * Get all roles with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all roles with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The roles have been successfully retrieved.',
    type: Role,
    isArray: true,
  })
  @Permissions('roles read')
  async getAll(
    @Query() getAllDto: GetAllRoleDto,
  ): Promise<IGetAllResponseInterface<Role>> {
    return this.roleService.getAll(getAllDto);
  }

  /**
   * Get role by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The role has been successfully retrieved.',
    type: Role,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found.',
  })
  @Permissions('roles read')
  async getById(@Param('id', ParseIntPipe) id: number): Promise<Role> {
    return this.roleService.findOneById(id);
  }

  /**
   * Create new role
   */
  @Post()
  @ApiOperation({ summary: 'Create new role' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The role has been successfully created.',
    type: Role,
  })
  @HttpCode(HttpStatus.CREATED)
  @Permissions('roles create')
  async create(@Body() createDto: CreateRoleDto): Promise<Role> {
    return this.roleService.create(Role, createDto);
  }

  /**
   * Update an existing role
   */
  @Put(':id')
  @ApiOperation({ summary: 'Update an existing role' })
  @ApiParam({ name: 'id', description: 'Role ID', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The role has been successfully updated.',
    type: Role,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found.',
  })
  @Permissions('roles update')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateRoleDto,
  ): Promise<Role> {
    const updateDtoWithId = {
      ...updateDto,
      id,
    };
    return this.roleService.update(Role, updateDtoWithId);
  }

  /**
   * Delete a role
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a role' })
  @ApiParam({ name: 'id', description: 'Role ID', type: Number })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The role has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @Permissions('roles delete')
  async delete(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.roleService.deleteOne(id);
  }

  /**
   * Retrieves a role with its permissions by ID
   * @param id The role ID
   * @returns The role with permissions
   */
  @Get(':id/permissions')
  @ApiOperation({ summary: 'Get role with permissions by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Role with permissions retrieved successfully',
    type: Role,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found',
  })
  @Permissions('roles read')
  async findOneWithPermissions(@Param('id') id: number): Promise<Role> {
    return this.roleService.findOneWithPermissions(id);
  }

  /**
   * Adds a new permission to a role
   * @param id The role ID
   * @param permission The permission to add
   * @returns The updated role
   */
  @Post(':id/permissions/:permissionId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Add permission to role' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission added to role successfully',
    type: Role,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found',
  })
  async addPermission(
    @Param('id') id: number,
    @Param('permissionId') permissionId: number,
  ): Promise<Role | null> {
    const role = await this.roleService.findOneWithPermissions(id);
    const permission = await this.permissionService.findOneById(permissionId);
    return this.roleService.addNewPermission(role, permission);
  }
}
