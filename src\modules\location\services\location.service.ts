import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { GenericService, IGetAllResponseInterface } from '@app/common';
import { FindManyOptions } from 'typeorm';
import { CreateLocationDto } from '../dtos/create-location.dto';
import { GetAllLocationDto } from '../dtos/get-all-location.dto';
import { UpdateLocationDto } from '../dtos/update-location.dto';
import { Location } from '../entities/location.entity';
import { LocationSchema } from '../schemes/location.schema';
import { ILocationRepository } from '../interfaces/location-repository/location-repository.interface';
import { ILocationService } from '../interfaces/location-service/location-service.interface';

@Injectable()
export class LocationService
  extends GenericService<
    Location,
    typeof LocationSchema,
    GetAllLocationDto,
    CreateLocationDto,
    UpdateLocationDto
  >
  implements ILocationService
{
  constructor(
    @Inject(ILocationRepository)
    private readonly locationRepository: ILocationRepository,
  ) {
    super(locationRepository);
  }

  async getAll(
    getAllDto: GetAllLocationDto,
    initialQuery?: FindManyOptions<Location>,
  ): Promise<IGetAllResponseInterface<Location>> {
    initialQuery = initialQuery || {};
    initialQuery['where'] = initialQuery['where'] || {};

    if (getAllDto.searchKey) {
      getAllDto.searchOnJson =
        'nameEn,nameAr,streetAddressEn,streetAddressAr,cityEn,cityAr,nearestLandmarkEn,nearestLandmarkAr';
    }

    // Handle geospatial filtering - these bypass the generic service
    if (
      getAllDto.centerLatitude &&
      getAllDto.centerLongitude &&
      getAllDto.radiusKm
    ) {
      return this.getLocationsByRadius(getAllDto);
    }

    if (
      getAllDto.minLatitude &&
      getAllDto.maxLatitude &&
      getAllDto.minLongitude &&
      getAllDto.maxLongitude
    ) {
      return this.getLocationsByBounds(getAllDto);
    }

    // Handle location-specific filters
    if (getAllDto.cityEn || getAllDto.cityAr) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        ...(getAllDto.cityEn && { cityEn: getAllDto.cityEn }),
        ...(getAllDto.cityAr && { cityAr: getAllDto.cityAr }),
      };
    }

    if (getAllDto.countryEn || getAllDto.countryAr) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        ...(getAllDto.countryEn && { countryEn: getAllDto.countryEn }),
        ...(getAllDto.countryAr && { countryAr: getAllDto.countryAr }),
      };
    }

    if (getAllDto.parentId !== undefined) {
      initialQuery['where'] = {
        ...initialQuery['where'],
        parentId: getAllDto.parentId,
      };
    }

    return super.getAll(getAllDto, initialQuery);
  }

  async create(
    entityType: new () => Location,
    dto: CreateLocationDto,
  ): Promise<Location> {
    if (!this.validateCoordinates(dto.latitude, dto.longitude)) {
      throw new BadRequestException('Invalid coordinates provided');
    }

    const location = plainToInstance(entityType, dto);

    if (location.parentId) {
      const parent = await this.locationRepository.findOneBy({
        where: { id: location.parentId },
      });
      if (!parent) {
        throw new NotFoundException('Parent location not found');
      }
    }

    return this.locationRepository.createOne(location);
  }

  async update(
    entityType: new () => Location,
    dto: UpdateLocationDto & { id: number },
  ): Promise<Location> {
    const existingLocation = await this.findOneById(dto.id);

    if (dto.latitude !== undefined && dto.longitude !== undefined) {
      if (!this.validateCoordinates(dto.latitude, dto.longitude)) {
        throw new BadRequestException('Invalid coordinates provided');
      }
    }

    const updatedLocation = plainToInstance(entityType, {
      ...existingLocation,
      ...dto,
    });

    updatedLocation.id = dto.id;

    if (dto.parentId !== undefined) {
      if (dto.parentId === updatedLocation.id) {
        throw new BadRequestException('Location cannot be its own parent');
      }

      if (dto.parentId) {
        const parent = await this.findOneById(dto.parentId);
        if (!parent) {
          throw new NotFoundException('Parent location not found');
        }
      }
    }

    await this.locationRepository.updateOne(updatedLocation);
    return this.findOneById(existingLocation.id);
  }

  async findByCoordinates(
    latitude: number,
    longitude: number,
    radiusKm?: number,
  ): Promise<Location[]> {
    if (!this.validateCoordinates(latitude, longitude)) {
      throw new BadRequestException('Invalid coordinates provided');
    }
    return this.locationRepository.findByCoordinates(
      latitude,
      longitude,
      radiusKm,
    );
  }

  async findWithinBounds(
    minLatitude: number,
    maxLatitude: number,
    minLongitude: number,
    maxLongitude: number,
  ): Promise<Location[]> {
    if (
      !this.validateCoordinates(minLatitude, minLongitude) ||
      !this.validateCoordinates(maxLatitude, maxLongitude)
    ) {
      throw new BadRequestException('Invalid bounds provided');
    }
    return this.locationRepository.findWithinBounds(
      minLatitude,
      maxLatitude,
      minLongitude,
      maxLongitude,
    );
  }

  async findByCity(cityEn?: string, cityAr?: string): Promise<Location[]> {
    return this.locationRepository.findByCity(cityEn, cityAr);
  }

  async findByCountry(
    countryEn?: string,
    countryAr?: string,
  ): Promise<Location[]> {
    return this.locationRepository.findByCountry(countryEn, countryAr);
  }

  async findByParentId(parentId: number | null): Promise<Location[]> {
    return this.locationRepository.findByParentId(parentId);
  }

  async findWithChildren(id: number): Promise<Location | null> {
    return this.locationRepository.findWithChildren(id);
  }

  async findNearest(
    latitude: number,
    longitude: number,
    limit?: number,
  ): Promise<Location[]> {
    if (!this.validateCoordinates(latitude, longitude)) {
      throw new BadRequestException('Invalid coordinates provided');
    }
    return this.locationRepository.findNearest(latitude, longitude, limit);
  }

  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    return this.locationRepository.calculateDistance(lat1, lon1, lat2, lon2);
  }

  validateCoordinates(latitude: number, longitude: number): boolean {
    return (
      latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180
    );
  }

  private async getLocationsByRadius(
    getAllDto: GetAllLocationDto,
  ): Promise<IGetAllResponseInterface<Location>> {
    const locations = await this.findByCoordinates(
      getAllDto.centerLatitude!,
      getAllDto.centerLongitude!,
      getAllDto.radiusKm,
    );

    const offset = getAllDto.offset || 0;
    const limit = getAllDto.limit || 10;
    const paginatedLocations = locations.slice(offset, offset + limit);

    return {
      items: paginatedLocations,
      count: locations.length,
    };
  }

  private async getLocationsByBounds(
    getAllDto: GetAllLocationDto,
  ): Promise<IGetAllResponseInterface<Location>> {
    const locations = await this.findWithinBounds(
      getAllDto.minLatitude!,
      getAllDto.maxLatitude!,
      getAllDto.minLongitude!,
      getAllDto.maxLongitude!,
    );

    const offset = getAllDto.offset || 0;
    const limit = getAllDto.limit || 10;
    const paginatedLocations = locations.slice(offset, offset + limit);

    return {
      items: paginatedLocations,
      count: locations.length,
    };
  }
}
