<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الميزة - قريب بلس</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/forms.css">
    
    <style>
        .emoji-picker {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            max-height: 200px;
            overflow-y: auto;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 15px;
            background: #fafbfc;
            margin-top: 10px;
        }
        
        .emoji-option {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            border: 2px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
            background: white;
        }
        
        .emoji-option:hover {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
        }
        
        .emoji-option.selected {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.2);
        }
        
        .selected-emoji {
            font-size: 2rem;
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .feature-type-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .feature-type-option {
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
            text-align: center;
            background: white;
        }
        
        .feature-type-option:hover {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
        }
        
        .feature-type-option.selected {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
        }
        
        .feature-type-option input[type="radio"] {
            display: none;
        }
        
        .feature-type-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .feature-type-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .feature-type-desc {
            font-size: 0.9rem;
            color: #666;
        }
        
        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }
        
        .feature-id {
            background: #e9ecef;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Content will be moved to main area by navigation.js -->
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">تعديل الميزة</h1>
            <p class="page-subtitle">تعديل بيانات الميزة المحددة</p>
        </div>
        
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p>جاري تحميل بيانات الميزة...</p>
        </div>
        
        <div class="form-container" id="formContainer" style="display: none;">
            <div class="feature-id" id="featureId">
                معرف الميزة: #
            </div>
            
            <form id="featureForm">
                <div class="form-section">
                    <h2>⭐ المعلومات الأساسية</h2>
                    
                    <div class="form-group">
                        <div class="bilingual-label">
                            <span class="ar">اسم الميزة (عربي)</span>
                            <span class="en">Feature Name (Arabic)</span>
                        </div>
                        <input type="text" id="nameAr" name="nameAr" required dir="rtl" placeholder="أدخل اسم الميزة بالعربية">
                    </div>
                    
                    <div class="form-group">
                        <div class="bilingual-label">
                            <span class="ar">اسم الميزة (إنجليزي)</span>
                            <span class="en">Feature Name (English)</span>
                        </div>
                        <input type="text" id="nameEn" name="nameEn" required dir="ltr" placeholder="Enter feature name in English">
                    </div>
                    
                    <div class="form-group">
                        <label>أيقونة الميزة</label>
                        <div class="icon-input-container" style="display: flex; gap: 15px; align-items: center; margin-bottom: 15px;">
                            <div class="selected-emoji" id="selectedEmoji" style="flex-shrink: 0;">⭐</div>
                            <div style="flex-grow: 1;">
                                <label for="iconInput" style="display: block; margin-bottom: 5px; font-size: 14px; color: #666;">أو أدخل أيقونة مخصصة:</label>
                                <input 
                                    type="text" 
                                    id="iconInput" 
                                    placeholder="اكتب أو الصق أيقونة (مثل: 🎯 أو ⚡)"
                                    style="padding: 12px; border: 2px solid #e1e8ed; border-radius: 8px; font-size: 16px; text-align: center; width: 100%;"
                                    maxlength="2"
                                />
                                <small style="color: #666; font-size: 12px;">يمكنك استخدام أي رمز تعبيري أو رمز خاص</small>
                            </div>
                        </div>
                        <div class="emoji-picker" id="emojiPicker">
                            <!-- Emojis will be populated here -->
                        </div>
                        <input type="hidden" id="icon" name="icon" value="⭐">
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span id="submitBtnText">حفظ التغييرات</span>
                        <div class="loading-spinner" id="submitSpinner" style="display: none;"></div>
                    </button>
                    <a href="list.html" class="btn btn-secondary">إلغاء</a>
                    <button type="button" class="btn btn-danger" id="deleteBtn">
                        <i class="fas fa-trash"></i>
                        حذف الميزة
                    </button>
                </div>
                
                <div class="response-message" id="responseMessage"></div>
            </form>
        </div>
        
        <div id="errorContainer" class="empty-state" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>خطأ في تحميل البيانات</h3>
            <p>لم يتم العثور على الميزة المطلوبة</p>
            <a href="list.html" class="btn btn-primary" style="margin-top: 20px;">
                العودة إلى قائمة الميزات
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/auth-utils.js"></script>
    <script src="../../assets/js/navigation.js"></script>
    <script>
        const featureEmojis = [
            '⭐', '🛎️', '🏢', '💳', '♿', '🚗', '📶', '🔒',
            '🌡️', '❄️', '🔥', '💡', '🎵', '📺', '📻', '🎮',
            '🍽️', '☕', '🍺', '🚭', '🐕', '👶', '🚻', '🚹',
            '🚺', '🛏️', '🛁', '🚿', '🧴', '🧻', '🧽', '🧹',
            '🔌', '🔋', '💻', '📱', '📞', '📠', '🖨️', '📧',
            '🌐', '📡', '🛰️', '📍', '🗺️', '🧭', '⏰', '⏱️',
            '🎯', '🎲', '🎪', '🎨', '🎭', '🎬', '🎤', '🎧',
            '🎸', '🥁', '🎹', '🎺', '🎷', '🎻', '🪕', '🎪',
            '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️'
        ];
        
        let currentFeatureId = null;
        let currentFeature = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!AuthUtils.isAuthenticated()) {
                window.location.href = '../auth/login.html';
                return;
            }
            
            // Get feature ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            currentFeatureId = urlParams.get('id');
            
            if (!currentFeatureId) {
                showError();
                return;
            }
            
            loadFeature();
            populateEmojiPicker();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            const form = document.getElementById('featureForm');
            const deleteBtn = document.getElementById('deleteBtn');
            const iconInput = document.getElementById('iconInput');
            
            form.addEventListener('submit', handleSubmit);
            deleteBtn.addEventListener('click', handleDelete);
            
            // Handle custom icon input
            iconInput.addEventListener('input', function() {
                const customIcon = this.value.trim();
                if (customIcon) {
                    selectEmoji(customIcon);
                    // Clear selection from emoji picker if custom icon is used
                    document.querySelectorAll('.emoji-option').forEach(option => {
                        option.classList.remove('selected');
                    });
                }
            });
            
            // Keep icon input in sync with selected emoji
            iconInput.addEventListener('focus', function() {
                this.value = document.getElementById('icon').value;
            });
        }
        
        async function loadFeature() {
            const loadingContainer = document.getElementById('loadingContainer');
            const formContainer = document.getElementById('formContainer');
            const errorContainer = document.getElementById('errorContainer');
            
            try {
                const response = await fetch(`/v1/features/${currentFeatureId}`, {
                    headers: AuthUtils.getAuthHeaders()
                });
                if (response.ok) {
                    const result = await response.json();
                    currentFeature = result.data || result;
                    populateForm(currentFeature);
                    
                    loadingContainer.style.display = 'none';
                    formContainer.style.display = 'block';
                } else {
                    throw new Error('Feature not found');
                }
            } catch (error) {
                console.error('Error loading feature:', error);
                loadingContainer.style.display = 'none';
                errorContainer.style.display = 'block';
            }
        }
        
        function populateForm(feature) {
            document.getElementById('featureId').textContent = `معرف الميزة: #${feature.id}`;
            document.getElementById('nameAr').value = feature.nameAr || '';
            document.getElementById('nameEn').value = feature.nameEn || '';

            if (feature.icon) {
                selectEmoji(feature.icon);
            }
        }
        
        function populateEmojiPicker() {
            const emojiPicker = document.getElementById('emojiPicker');
            
            featureEmojis.forEach(emoji => {
                const emojiOption = document.createElement('div');
                emojiOption.className = 'emoji-option';
                emojiOption.textContent = emoji;
                emojiOption.addEventListener('click', () => selectEmoji(emoji));
                emojiPicker.appendChild(emojiOption);
            });
        }
        
        function selectEmoji(emoji) {
            document.getElementById('selectedEmoji').textContent = emoji;
            document.getElementById('icon').value = emoji;
            
            document.querySelectorAll('.emoji-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            const selectedOption = Array.from(document.querySelectorAll('.emoji-option'))
                .find(option => option.textContent === emoji);
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
        }
        

        
        async function handleSubmit(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const submitBtnText = document.getElementById('submitBtnText');
            const submitSpinner = document.getElementById('submitSpinner');
            
            submitBtn.disabled = true;
            submitBtnText.style.display = 'none';
            submitSpinner.style.display = 'inline-block';
            
            try {
                const formData = new FormData(e.target);
                const featureData = {
                    nameAr: formData.get('nameAr'),
                    nameEn: formData.get('nameEn'),
                    icon: formData.get('icon')
                };
                
                const response = await fetch(`/v1/features/${currentFeatureId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...AuthUtils.getAuthHeaders()
                    },
                    body: JSON.stringify(featureData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage('تم تحديث الميزة بنجاح!', 'success');
                    setTimeout(() => {
                        window.location.href = 'list.html';
                    }, 2000);
                } else {
                    showMessage(result.message || 'فشل في تحديث الميزة', 'error');
                }
            } catch (error) {
                console.error('Error updating feature:', error);
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtnText.style.display = 'inline';
                submitSpinner.style.display = 'none';
            }
        }
        
        async function handleDelete() {
            if (!confirm('هل أنت متأكد من حذف هذه الميزة؟ سيتم إزالتها من جميع الأعمال التجارية المرتبطة بها.')) {
                return;
            }
            
            try {
                const response = await fetch(`/v1/features/${currentFeatureId}`, {
                    method: 'DELETE',
                    headers: AuthUtils.getAuthHeaders()
                });
                
                if (response.ok) {
                    showMessage('تم حذف الميزة بنجاح', 'success');
                    setTimeout(() => {
                        window.location.href = 'list.html';
                    }, 2000);
                } else {
                    throw new Error('Failed to delete feature');
                }
            } catch (error) {
                console.error('Error deleting feature:', error);
                showMessage('فشل في حذف الميزة', 'error');
            }
        }
        
        function showError() {
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('errorContainer').style.display = 'block';
        }
        
        function showMessage(message, type) {
            const responseMessage = document.getElementById('responseMessage');
            responseMessage.textContent = message;
            responseMessage.className = `response-message ${type}`;
            responseMessage.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    responseMessage.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
